import { http } from "@/utils/http";

export interface TaskExecutionDetail {
  id: number;
  taskId: number;
  taskName: string;
  searchRule: string;
  startTime: string;
  endTime: string;
  executionDurationSeconds: number;
  totalResults: number;
  validResults: number;
  duplicateResults: number;
  outOfRangeResults: number;
  lastAssetIp?: string;
  lastAssetPort?: string;
  lastAssetDomain?: string;
  lastAssetDiscoveryTime?: string;
  executionStatus: string;
  errorMessage?: string;
  createdAt: string;
  // 新增字段
  retryCount?: number;
  retryStrategy?: string;
  totalRetryDelayMs?: number;
  peakMemoryUsageMb?: number;
  avgMemoryUsageMb?: number;
  browserRestartCount?: number;
}

export interface TaskExecutionStats {
  taskName?: string;
  executionCount: number;
  averageDurationSeconds: number;
  totalValidResults: number;
  lastExecution?: TaskExecutionDetail;
  totalTaskNames?: number;
  totalExecutions?: number;
  recentExecutions?: number;
  taskNames?: string[];
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  total?: number;
  message: string;
}

export interface PageResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
}

/**
 * 获取任务执行详情列表
 * @param params 查询参数
 */
export const getTaskExecutionDetails = (params: {
  taskName?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  size?: number;
}) => {
  return http.request<ApiResponse<PageResponse<TaskExecutionDetail>>>(
    "get",
    "/api/tasks/execution-details",
    {
      params
    }
  );
};

/**
 * 获取任务执行统计信息
 * @param taskName 任务名称（可选）
 */
export const getTaskExecutionStats = (taskName?: string) => {
  const params = taskName ? { taskName } : {};
  return http.request<ApiResponse<TaskExecutionStats>>(
    "get",
    "/api/tasks/execution-stats",
    {
      params
    }
  );
};

/**
 * 获取所有任务名称列表
 */
export const getAllTaskNames = () => {
  return http.request<ApiResponse<string[]>>(
    "get",
    "/api/tasks/execution-task-names"
  );
};

// 新增接口定义
export interface Task {
  id: number;
  name: string;
  rule: string;
  status: string;
  priority?: number;
  timeRange?: string;
  errorMessage?: string;
  createdAt: string;
  executedAt?: string;
  completedAt?: string;
  retryCount?: number;
  // 定时任务相关字段
  scheduleEnabled?: boolean;
  scheduleTime?: string;
  scheduleStatus?: string;
  scheduleFrequency?: string;
  timeWindowStrategy?: string;
  lastScheduledAt?: string;
}

export interface BatchTaskStartRequest {
  taskIds: number[];
  timeRange: string;
  forceStart?: boolean;
}

export interface BatchTaskDeleteRequest {
  taskIds: number[];
  forceDelete?: boolean;
}

export interface BatchTaskStatusUpdateRequest {
  taskIds: number[];
  status: string;
  errorMessage?: string;
}

export interface BatchTaskCreateRequest {
  keywords: string[];
  namePrefix?: string;
  queryTemplate?: string;
  priority?: number;
}

export interface BatchOperationResult {
  operationType: string;
  successCount: number;
  failureCount: number;
  results: TaskResult[];
}

export interface TaskResult {
  taskId: number;
  taskName: string;
  success: boolean;
  message: string;
}

export interface BatchTaskStats {
  totalCount: number;
  pendingCount: number;
  processingCount: number;
  completedCount: number;
  failedCount: number;
  skippedCount: number;
}

// 批量操作API
export const batchCreateTasks = (request: BatchTaskCreateRequest): Promise<ApiResponse<BatchOperationResult>> => {
  return http.request<ApiResponse<BatchOperationResult>>("post", "/api/tasks/batch/create", { data: request });
};

export const batchStartTasks = (request: BatchTaskStartRequest): Promise<ApiResponse<BatchOperationResult>> => {
  return http.request<ApiResponse<BatchOperationResult>>("post", "/api/tasks/batch/start", { data: request });
};

export const batchDeleteTasks = (request: BatchTaskDeleteRequest): Promise<ApiResponse<BatchOperationResult>> => {
  return http.request<ApiResponse<BatchOperationResult>>("delete", "/api/tasks/batch", { data: request });
};

export const batchUpdateTaskStatus = (request: BatchTaskStatusUpdateRequest): Promise<ApiResponse<BatchOperationResult>> => {
  return http.request<ApiResponse<BatchOperationResult>>("put", "/api/tasks/batch/status", { data: request });
};

export const getBatchTaskStats = (taskIds: number[]): Promise<ApiResponse<BatchTaskStats>> => {
  return http.request<ApiResponse<BatchTaskStats>>("post", "/api/tasks/batch/stats", { data: taskIds });
};

// 资源监控API
export interface MemoryInfo {
  heap: {
    used: number;
    max: number;
    committed: number;
    init: number;
    usageRatio: number;
  };
  nonHeap: {
    used: number;
    max: number;
    committed: number;
    init: number;
    usageRatio: number;
  };
  timestamp: number;
}

export interface RuntimeInfo {
  runtime: {
    availableProcessors: number;
    totalMemory: number;
    freeMemory: number;
    maxMemory: number;
    usedMemory: number;
  };
  jvm: {
    version: string;
    vendor: string;
    vmName: string;
    vmVersion: string;
  };
  timestamp: number;
}

export const getMemoryUsage = (): Promise<ApiResponse<MemoryInfo>> => {
  return http.request<ApiResponse<MemoryInfo>>("get", "/api/monitor/memory");
};

export const performGarbageCollection = (): Promise<ApiResponse<any>> => {
  return http.request<ApiResponse<any>>("post", "/api/monitor/gc");
};

export const getRuntimeInfo = (): Promise<ApiResponse<RuntimeInfo>> => {
  return http.request<ApiResponse<RuntimeInfo>>("get", "/api/monitor/runtime");
};

// ==================== 定时任务相关API ====================

export interface ScheduleConfigRequest {
  scheduleEnabled: boolean;
  scheduleTime?: string;
  scheduleCron?: string;
  timeWindowStrategy?: string;
  timeWindowConfig?: string;
  timeRange?: string;
  scheduleFrequency?: string;
}

export interface ScheduleStatistics {
  totalScheduled: number;
  activeScheduled: number;
  pausedScheduled: number;
}

// 执行频率选项
export const SCHEDULE_FREQUENCY_OPTIONS = [
  { value: "DAILY", label: "每日执行", description: "每天在指定时间执行一次" },
  { value: "EVERY_THREE_DAYS", label: "每三日执行", description: "每三天在指定时间执行一次" }
];

/**
 * 获取所有定时任务列表
 */
export const getScheduledTasks = (): Promise<ApiResponse<Task[]>> => {
  return http.request<ApiResponse<Task[]>>("get", "/api/tasks/scheduled");
};

/**
 * 获取启用的定时任务列表
 */
export const getEnabledScheduledTasks = (): Promise<ApiResponse<Task[]>> => {
  return http.request<ApiResponse<Task[]>>("get", "/api/tasks/scheduled/enabled");
};

/**
 * 启用任务的定时功能
 */
export const enableTaskSchedule = (taskId: number, scheduleTime: string, timeWindowStrategy: string = "SMART", scheduleFrequency: string = "DAILY"): Promise<ApiResponse<Task>> => {
  return http.request<ApiResponse<Task>>("post", `/api/tasks/${taskId}/enable-schedule`, {
    params: { scheduleTime, timeWindowStrategy, scheduleFrequency }
  });
};

/**
 * 禁用任务的定时功能
 */
export const disableTaskSchedule = (taskId: number): Promise<ApiResponse<Task>> => {
  return http.request<ApiResponse<Task>>("post", `/api/tasks/${taskId}/disable-schedule`);
};

/**
 * 更新任务的定时配置
 */
export const updateScheduleConfig = (taskId: number, config: ScheduleConfigRequest): Promise<ApiResponse<Task>> => {
  return http.request<ApiResponse<Task>>("put", `/api/tasks/${taskId}/schedule-config`, { data: config });
};

/**
 * 手动触发定时任务执行
 */
export const triggerScheduledTask = (taskId: number): Promise<ApiResponse<string>> => {
  return http.request<ApiResponse<string>>("post", `/api/tasks/${taskId}/trigger-schedule`);
};

/**
 * 获取定时任务统计信息
 */
export const getScheduleStatistics = (): Promise<ApiResponse<ScheduleStatistics>> => {
  return http.request<ApiResponse<ScheduleStatistics>>("get", "/api/tasks/schedule-statistics");
};

/**
 * 暂停定时任务
 */
export const pauseScheduledTask = (taskId: number): Promise<ApiResponse<string>> => {
  return http.request<ApiResponse<string>>("post", `/api/tasks/${taskId}/pause-schedule`);
};

/**
 * 恢复定时任务
 */
export const resumeScheduledTask = (taskId: number): Promise<ApiResponse<string>> => {
  return http.request<ApiResponse<string>>("post", `/api/tasks/${taskId}/resume-schedule`);
};
