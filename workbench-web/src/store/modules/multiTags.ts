import { defineStore } from "pinia";
import {
  type multiType,
  type positionType,
  store,
  isUrl,
  isEqual,
  isNumber,
  isBoolean,
  getConfig,
  routerArrays,
  storageLocal,
  responsiveStorageNameSpace
} from "../utils";
import { usePermissionStoreHook } from "./permission";

// 清理无效的标签页数据
function cleanInvalidTags(tags: any[]): any[] {
  if (!Array.isArray(tags)) return [];

  return tags.filter(tag => {
    // 检查基本结构
    if (!tag || typeof tag !== 'object') return false;

    // 检查路径是否包含无效参数
    if (tag.path && typeof tag.path === 'string') {
      if (tag.path.includes('undefined') || tag.path.includes('null') || tag.path.includes('/ip/undefined') || tag.path.includes('/ip/null')) {
        console.warn('Removing invalid tag with path:', tag.path);
        return false;
      }
    }

    // 检查参数是否有效
    if (tag.params && typeof tag.params === 'object') {
      for (const [key, value] of Object.entries(tag.params)) {
        if (value === 'undefined' || value === 'null' || (typeof value === 'string' && value.trim() === '')) {
          console.warn(`Removing tag with invalid parameter ${key}:`, value);
          return false;
        }
      }
    }

    return true;
  });
}

export const useMultiTagsStore = defineStore("pure-multiTags", {
  state: () => {
    // 获取存储的标签页数据并清理无效项
    const storedTags = storageLocal().getItem<StorageConfigs>(
      `${responsiveStorageNameSpace()}configure`
    )?.multiTagsCache
      ? storageLocal().getItem<StorageConfigs>(
          `${responsiveStorageNameSpace()}tags`
        )
      : null;

    const defaultTags = [
      ...routerArrays,
      ...usePermissionStoreHook().flatteningRoutes.filter(
        v => v?.meta?.fixedTag
      )
    ] as any;

    const cleanedTags = storedTags ? cleanInvalidTags(storedTags) : defaultTags;

    // 如果清理后的标签与原始标签不同，更新存储
    if (storedTags && cleanedTags.length !== storedTags.length) {
      console.log('Cleaned invalid tags from storage');
      storageLocal().setItem(
        `${responsiveStorageNameSpace()}tags`,
        cleanedTags
      );
    }

    return {
      // 存储标签页信息（路由信息）
      multiTags: cleanedTags,
      multiTagsCache: storageLocal().getItem<StorageConfigs>(
        `${responsiveStorageNameSpace()}configure`
      )?.multiTagsCache
    };
  },
  getters: {
    getMultiTagsCache(state) {
      return state.multiTagsCache;
    }
  },
  actions: {
    multiTagsCacheChange(multiTagsCache: boolean) {
      this.multiTagsCache = multiTagsCache;
      if (multiTagsCache) {
        storageLocal().setItem(
          `${responsiveStorageNameSpace()}tags`,
          this.multiTags
        );
      } else {
        storageLocal().removeItem(`${responsiveStorageNameSpace()}tags`);
      }
    },
    tagsCache(multiTags) {
      this.getMultiTagsCache &&
        storageLocal().setItem(
          `${responsiveStorageNameSpace()}tags`,
          multiTags
        );
    },
    handleTags<T>(
      mode: string,
      value?: T | multiType,
      position?: positionType
    ): T {
      switch (mode) {
        case "equal":
          this.multiTags = value;
          this.tagsCache(this.multiTags);
          break;
        case "push":
          {
            const tagVal = value as multiType;

            // 基本验证
            if (!tagVal || typeof tagVal !== 'object') {
              console.warn('Invalid tag value:', tagVal);
              return;
            }

            // 不添加到标签页
            if (tagVal?.meta?.hiddenTag) return;
            // 如果是外链无需添加信息到标签页
            if (isUrl(tagVal?.name)) return;
            // 如果title为空拒绝添加空信息到标签页
            if (tagVal?.meta?.title.length === 0) return;
            // showLink:false 不添加到标签页
            if (isBoolean(tagVal?.meta?.showLink) && !tagVal?.meta?.showLink)
              return;

            // 验证路由参数的有效性
            if (tagVal?.params && typeof tagVal.params === 'object') {
              // 特别检查 IP 参数
              if (tagVal.params.ip !== undefined) {
                const ipParam = tagVal.params.ip;
                if (!ipParam ||
                    typeof ipParam !== 'string' ||
                    ipParam.trim() === '' ||
                    ipParam === 'undefined' ||
                    ipParam === 'null') {
                  console.warn('Invalid IP parameter detected in multiTags, skipping tag creation:', ipParam);
                  return;
                }
              }

              // 检查其他可能的无效参数
              for (const [key, value] of Object.entries(tagVal.params)) {
                if (value === 'undefined' || value === 'null' || (typeof value === 'string' && value.trim() === '')) {
                  console.warn(`Invalid parameter ${key} detected in multiTags:`, value);
                  // 清理无效参数
                  delete tagVal.params[key];
                }
              }
            }

            // 验证路径的有效性
            if (tagVal.path && typeof tagVal.path === 'string') {
              // 检查路径中是否包含无效的参数占位符
              if (tagVal.path.includes('undefined') || tagVal.path.includes('null')) {
                console.warn('Invalid path detected in multiTags:', tagVal.path);
                return;
              }
            }
            const tagPath = tagVal.path;
            // 判断tag是否已存在
            const tagHasExits = this.multiTags.some(tag => {
              return tag.path === tagPath;
            });

            // 判断tag中的query键值是否相等
            const tagQueryHasExits = this.multiTags.some(tag => {
              return isEqual(tag?.query, tagVal?.query);
            });

            // 判断tag中的params键值是否相等
            const tagParamsHasExits = this.multiTags.some(tag => {
              return isEqual(tag?.params, tagVal?.params);
            });

            if (tagHasExits && tagQueryHasExits && tagParamsHasExits) return;

            // 动态路由可打开的最大数量
            const dynamicLevel = tagVal?.meta?.dynamicLevel ?? -1;
            if (dynamicLevel > 0) {
              if (
                this.multiTags.filter(e => e?.path === tagPath).length >=
                dynamicLevel
              ) {
                // 如果当前已打开的动态路由数大于dynamicLevel，替换第一个动态路由标签
                const index = this.multiTags.findIndex(
                  item => item?.path === tagPath
                );
                index !== -1 && this.multiTags.splice(index, 1);
              }
            }
            this.multiTags.push(value);
            this.tagsCache(this.multiTags);
            if (
              getConfig()?.MaxTagsLevel &&
              isNumber(getConfig().MaxTagsLevel)
            ) {
              if (this.multiTags.length > getConfig().MaxTagsLevel) {
                this.multiTags.splice(1, 1);
              }
            }
          }
          break;
        case "splice":
          if (!position) {
            const index = this.multiTags.findIndex(v => v.path === value);
            if (index === -1) return;
            this.multiTags.splice(index, 1);
          } else {
            this.multiTags.splice(position?.startIndex, position?.length);
          }
          this.tagsCache(this.multiTags);
          return this.multiTags;
        case "slice":
          return this.multiTags.slice(-1);
      }
    }
  }
});

export function useMultiTagsStoreHook() {
  return useMultiTagsStore(store);
}
