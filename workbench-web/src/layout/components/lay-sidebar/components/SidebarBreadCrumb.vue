<script setup lang="ts">
import { isEqual } from "@pureadmin/utils";
import { transformI18n } from "@/plugins/i18n";
import { useRoute, useRouter } from "vue-router";
import { ref, watch, onMounted, toRaw } from "vue";
import { getParentPaths, findRouteByPath } from "@/router/utils";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";

const route = useRoute();
const levelList = ref([]);
const router = useRouter();
const routes: any = router.options.routes;
const multiTags: any = useMultiTagsStoreHook().multiTags;

const getBreadcrumb = (): void => {
  // 当前路由信息
  let currentRoute;

  if (Object.keys(route.query).length > 0) {
    multiTags.forEach(item => {
      if (isEqual(route.query, item?.query)) {
        currentRoute = toRaw(item);
      }
    });
  } else if (Object.keys(route.params).length > 0) {
    multiTags.forEach(item => {
      if (isEqual(route.params, item?.params)) {
        currentRoute = toRaw(item);
      }
    });
  } else {
    currentRoute = findRouteByPath(router.currentRoute.value.path, routes);
  }

  // 当前路由的父级路径组成的数组
  const parentRoutes = getParentPaths(
    router.currentRoute.value.name as string,
    routes,
    "name"
  );
  // 存放组成面包屑的数组
  const matched = [];

  // 获取每个父级路径对应的路由信息
  parentRoutes.forEach(path => {
    if (path !== "/") matched.push(findRouteByPath(path, routes));
  });

  matched.push(currentRoute);

  matched.forEach((item, index) => {
    if (currentRoute?.query || currentRoute?.params) return;
    if (item?.children) {
      item.children.forEach(v => {
        if (v?.meta?.title === item?.meta?.title) {
          matched.splice(index, 1);
        }
      });
    }
  });

  levelList.value = matched.filter(
    item => item?.meta && item?.meta.title !== false
  );
};

// 验证路由参数的有效性
const validateRouteParams = (params: any): boolean => {
  if (!params || typeof params !== 'object') return true;

  // 特别检查 IP 参数
  if (params.ip !== undefined) {
    const ipParam = params.ip;
    if (!ipParam ||
        typeof ipParam !== 'string' ||
        ipParam.trim() === '' ||
        ipParam === 'undefined' ||
        ipParam === 'null') {
      console.warn('Invalid IP parameter in breadcrumb navigation:', ipParam);
      return false;
    }
  }

  return true;
};

const handleLink = item => {
  // 检查组件是否仍然挂载
  if (!item || typeof item !== 'object') {
    console.warn('Invalid breadcrumb item:', item);
    return;
  }

  const { redirect, name, path } = item;

  try {
    if (redirect) {
      router.push(redirect as any);
    } else {
      if (name) {
        if (item.query) {
          router.push({
            name,
            query: item.query
          });
        } else if (item.params) {
          // 验证参数有效性
          if (!validateRouteParams(item.params)) {
            console.error('Invalid route parameters detected in breadcrumb, skipping navigation');
            return;
          }

          router.push({
            name,
            params: item.params
          });
        } else {
          router.push({ name });
        }
      } else if (path) {
        router.push({ path });
      }
    }
  } catch (error) {
    console.error('Breadcrumb navigation error:', error);
    // 降级处理：尝试使用路径导航
    if (path) {
      try {
        router.push({ path });
      } catch (fallbackError) {
        console.error('Fallback navigation also failed:', fallbackError);
      }
    }
  }
};

onMounted(() => {
  getBreadcrumb();
});

watch(
  () => route.path,
  () => {
    getBreadcrumb();
  },
  {
    deep: true
  }
);
</script>

<template>
  <el-breadcrumb class="leading-[50px]! select-none" separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item
        v-for="item in levelList"
        :key="item.path"
        class="inline! items-stretch!"
      >
        <a @click.prevent="handleLink(item)">
          {{ transformI18n(item.meta.title) }}
        </a>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>
