<template>
  <div class="resource-monitor">
    <el-card class="monitor-card">
      <template #header>
        <div class="card-header">
          <span>系统资源监控</span>
          <div class="header-actions">
            <el-button
              :icon="Refresh"
              @click="refreshData"
              :loading="loading"
              size="small"
            >
              刷新
            </el-button>
            <el-button
              :icon="Delete"
              @click="performGC"
              :loading="gcLoading"
              size="small"
              type="warning"
            >
              执行GC
            </el-button>
          </div>
        </div>
      </template>

      <!-- 连接状态指示器 -->
      <div class="connection-status">
        <el-tag :type="wsConnected ? 'success' : 'danger'" size="small">
          <el-icon><Connection /></el-icon>
          {{ wsConnected ? 'WebSocket已连接' : 'WebSocket未连接' }}
        </el-tag>
        <span class="last-update">最后更新: {{ lastUpdateTime }}</span>
      </div>

      <!-- 内存使用情况 -->
      <div class="memory-section">
        <h4>内存使用情况</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="memory-item">
              <div class="memory-header">
                <span>堆内存 (Heap)</span>
                <el-tag :type="getMemoryStatusType(heapUsageRatio)">
                  {{ (heapUsageRatio * 100).toFixed(1) }}%
                </el-tag>
              </div>
              <el-progress
                :percentage="heapUsageRatio * 100"
                :color="getMemoryColor(heapUsageRatio)"
                :stroke-width="8"
              />
              <div class="memory-details">
                <span>已用: {{ formatBytes(memoryInfo?.heap.used || 0) }}</span>
                <span>最大: {{ formatBytes(memoryInfo?.heap.max || 0) }}</span>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="memory-item">
              <div class="memory-header">
                <span>非堆内存 (Non-Heap)</span>
                <el-tag :type="getMemoryStatusType(nonHeapUsageRatio)">
                  {{ (nonHeapUsageRatio * 100).toFixed(1) }}%
                </el-tag>
              </div>
              <el-progress
                :percentage="nonHeapUsageRatio * 100"
                :color="getMemoryColor(nonHeapUsageRatio)"
                :stroke-width="8"
              />
              <div class="memory-details">
                <span>已用: {{ formatBytes(memoryInfo?.nonHeap.used || 0) }}</span>
                <span>最大: {{ formatBytes(memoryInfo?.nonHeap.max || 0) }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 系统信息 -->
      <div class="system-section">
        <h4>系统信息</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-statistic
              title="CPU核心数"
              :value="runtimeInfo?.runtime.availableProcessors || 0"
              suffix="核"
            />
          </el-col>
          <el-col :span="8">
            <el-statistic
              title="浏览器实例"
              :value="browserCount"
              suffix="个"
            />
          </el-col>
          <el-col :span="8">
            <el-statistic
              title="系统状态"
              :value="systemStatus"
              :value-style="{ color: getStatusColor(systemStatus) }"
            />
          </el-col>
        </el-row>
      </div>

      <!-- JVM信息 -->
      <div class="jvm-section" v-if="runtimeInfo">
        <h4>JVM信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="Java版本">
            {{ runtimeInfo.jvm.version }}
          </el-descriptions-item>
          <el-descriptions-item label="JVM厂商">
            {{ runtimeInfo.jvm.vendor }}
          </el-descriptions-item>
          <el-descriptions-item label="虚拟机名称">
            {{ runtimeInfo.jvm.vmName }}
          </el-descriptions-item>
          <el-descriptions-item label="虚拟机版本">
            {{ runtimeInfo.jvm.vmVersion }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 实时监控图表 -->
      <div class="chart-section">
        <h4>内存使用趋势</h4>
        <div ref="chartContainer" class="chart-container"></div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Delete, Connection } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import {
  getMemoryUsage,
  getRuntimeInfo,
  performGarbageCollection,
  type MemoryInfo,
  type RuntimeInfo
} from '@/api/task'
import { webSocketService, type ResourceMonitor } from '@/utils/websocket'

// 响应式数据
const loading = ref(false)
const gcLoading = ref(false)
const wsConnected = ref(false)
const lastUpdateTime = ref('')

const memoryInfo = ref<MemoryInfo | null>(null)
const runtimeInfo = ref<RuntimeInfo | null>(null)
const heapUsageRatio = ref(0)
const nonHeapUsageRatio = ref(0)
const browserCount = ref(0)
const systemStatus = ref('正常')

const chartContainer = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

// 图表数据
const chartData = ref({
  times: [] as string[],
  heapUsage: [] as number[],
  nonHeapUsage: [] as number[]
})

// 生命周期
onMounted(() => {
  initializeMonitor()
  initChart()
})

onUnmounted(() => {
  if (chart) {
    chart.dispose()
  }
  // 取消WebSocket监听
})

// 初始化监控
const initializeMonitor = async () => {
  await refreshData()
  
  // 监听WebSocket连接状态
  const unsubscribeConnection = webSocketService.onConnectionStatus((connected) => {
    wsConnected.value = connected
  })
  
  // 监听资源监控数据
  const unsubscribeResource = webSocketService.onResourceMonitor((resource: ResourceMonitor) => {
    heapUsageRatio.value = resource.memoryUsageRatio
    browserCount.value = resource.browserCount
    systemStatus.value = resource.status
    lastUpdateTime.value = new Date().toLocaleTimeString()
    
    // 更新图表数据
    updateChartData()
  })
  
  wsConnected.value = webSocketService.isConnected()
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    const [memoryResponse, runtimeResponse] = await Promise.all([
      getMemoryUsage(),
      getRuntimeInfo()
    ])
    
    memoryInfo.value = memoryResponse.data
    runtimeInfo.value = runtimeResponse.data
    
    heapUsageRatio.value = memoryResponse.data.heap.usageRatio
    nonHeapUsageRatio.value = memoryResponse.data.nonHeap.usageRatio
    
    lastUpdateTime.value = new Date().toLocaleTimeString()
    updateChartData()
  } catch (error) {
    ElMessage.error('获取监控数据失败')
  } finally {
    loading.value = false
  }
}

// 执行垃圾回收
const performGC = async () => {
  gcLoading.value = true
  try {
    await performGarbageCollection()
    ElMessage.success('垃圾回收执行成功')
    // 延迟刷新数据，等待GC完成
    setTimeout(refreshData, 2000)
  } catch (error) {
    ElMessage.error('执行垃圾回收失败')
  } finally {
    gcLoading.value = false
  }
}

// 初始化图表
const initChart = async () => {
  await nextTick()
  if (chartContainer.value) {
    chart = echarts.init(chartContainer.value)
    
    const option = {
      title: {
        text: '内存使用趋势',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          let result = params[0].name + '<br/>'
          params.forEach((param: any) => {
            result += `${param.seriesName}: ${param.value.toFixed(1)}%<br/>`
          })
          return result
        }
      },
      legend: {
        data: ['堆内存', '非堆内存'],
        bottom: 0
      },
      xAxis: {
        type: 'category',
        data: chartData.value.times,
        axisLabel: { fontSize: 10 }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}%',
          fontSize: 10
        }
      },
      series: [
        {
          name: '堆内存',
          type: 'line',
          data: chartData.value.heapUsage,
          smooth: true,
          lineStyle: { color: '#409EFF' },
          areaStyle: { color: 'rgba(64, 158, 255, 0.1)' }
        },
        {
          name: '非堆内存',
          type: 'line',
          data: chartData.value.nonHeapUsage,
          smooth: true,
          lineStyle: { color: '#67C23A' },
          areaStyle: { color: 'rgba(103, 194, 58, 0.1)' }
        }
      ]
    }
    
    chart.setOption(option)
  }
}

// 更新图表数据
const updateChartData = () => {
  const now = new Date().toLocaleTimeString()
  const maxDataPoints = 20
  
  chartData.value.times.push(now)
  chartData.value.heapUsage.push(heapUsageRatio.value * 100)
  chartData.value.nonHeapUsage.push(nonHeapUsageRatio.value * 100)
  
  // 保持最多20个数据点
  if (chartData.value.times.length > maxDataPoints) {
    chartData.value.times.shift()
    chartData.value.heapUsage.shift()
    chartData.value.nonHeapUsage.shift()
  }
  
  // 更新图表
  if (chart) {
    chart.setOption({
      xAxis: { data: chartData.value.times },
      series: [
        { data: chartData.value.heapUsage },
        { data: chartData.value.nonHeapUsage }
      ]
    })
  }
}

// 工具方法
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getMemoryStatusType = (ratio: number): string => {
  if (ratio >= 0.9) return 'danger'
  if (ratio >= 0.8) return 'warning'
  return 'success'
}

const getMemoryColor = (ratio: number): string => {
  if (ratio >= 0.9) return '#F56C6C'
  if (ratio >= 0.8) return '#E6A23C'
  return '#67C23A'
}

const getStatusColor = (status: string): string => {
  switch (status) {
    case '正常': return '#67C23A'
    case '警告': return '#E6A23C'
    case '严重': return '#F56C6C'
    default: return '#909399'
  }
}
</script>

<style scoped>
.resource-monitor {
  width: 100%;
}

.monitor-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.connection-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
}

.last-update {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.memory-section,
.system-section,
.jvm-section,
.chart-section {
  margin-bottom: 24px;
}

.memory-section h4,
.system-section h4,
.jvm-section h4,
.chart-section h4 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 500;
}

.memory-item {
  padding: 16px;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  background: var(--el-bg-color);
}

.memory-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.memory-details {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.chart-container {
  height: 300px;
  width: 100%;
}
</style>
