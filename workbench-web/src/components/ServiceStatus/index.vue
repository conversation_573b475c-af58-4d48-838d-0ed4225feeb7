<template>
  <div class="service-status-indicator">
    <el-tooltip
      :content="tooltipContent"
      placement="right"
      :show-after="500"
    >
      <div
        :class="[
          'status-dot',
          statusClass
        ]"
        @click="showDetailDialog = true"
      >
        <i :class="statusIcon"></i>
      </div>
    </el-tooltip>

    <!-- 详细状态对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="服务状态详情"
      width="500px"
      :before-close="handleClose"
    >
      <div class="service-list">
        <div
          v-for="service in services"
          :key="service.name"
          class="service-item"
        >
          <div class="service-info">
            <div class="service-name">{{ service.name }}</div>
            <div class="service-url">{{ service.url }}</div>
          </div>
          <div class="service-status">
            <el-tag
              :type="service.available ? 'success' : 'danger'"
              size="small"
            >
              {{ service.available ? '在线' : '离线' }}
            </el-tag>
            <div
              v-if="!service.available && service.error"
              class="error-message"
            >
              {{ service.error }}
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="refreshStatus" :loading="refreshing">
            刷新状态
          </el-button>
          <el-button type="primary" @click="showDetailDialog = false">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { checkAllServices, type ServiceStatus } from '@/utils/serviceChecker';
import { createSmartPolling, type PollingManager } from '@/utils/pageVisibility';

const showDetailDialog = ref(false);
const services = ref<ServiceStatus[]>([]);
const refreshing = ref(false);
let pollingManager: PollingManager | null = null;

// 计算整体状态
const overallStatus = computed(() => {
  if (services.value.length === 0) return 'unknown';

  const availableCount = services.value.filter(s => s.available).length;
  const totalCount = services.value.length;

  if (availableCount === totalCount) return 'online';
  if (availableCount === 0) return 'offline';
  return 'partial';
});

// 状态样式类
const statusClass = computed(() => {
  switch (overallStatus.value) {
    case 'online': return 'status-online';
    case 'offline': return 'status-offline';
    case 'partial': return 'status-partial';
    default: return 'status-unknown';
  }
});

// 状态图标
const statusIcon = computed(() => {
  switch (overallStatus.value) {
    case 'online': return 'ep:check';
    case 'offline': return 'ep:close';
    case 'partial': return 'ep:warning';
    default: return 'ep:question-filled';
  }
});

// 提示内容
const tooltipContent = computed(() => {
  const availableCount = services.value.filter(s => s.available).length;
  const totalCount = services.value.length;

  switch (overallStatus.value) {
    case 'online': return '所有服务运行正常';
    case 'offline': return '所有服务离线';
    case 'partial': return `${availableCount}/${totalCount} 服务在线`;
    default: return '服务状态未知';
  }
});

// 刷新服务状态
async function refreshStatus() {
  refreshing.value = true;
  try {
    services.value = await checkAllServices();
    ElMessage.success('服务状态已刷新');
  } catch (error) {
    console.error('刷新服务状态失败:', error);
    ElMessage.error('刷新失败');
  } finally {
    refreshing.value = false;
  }
}

// 初始化服务状态
async function initStatus() {
  // 先从全局状态获取
  const globalStatus = (window as any).__SERVICE_STATUS__;
  if (globalStatus && Array.isArray(globalStatus)) {
    services.value = globalStatus;
  } else {
    // 如果没有全局状态，主动检查
    await refreshStatus();
  }
}

// 智能状态检查
async function checkServicesStatus() {
  try {
    services.value = await checkAllServices();
    // 更新全局状态
    (window as any).__SERVICE_STATUS__ = services.value;
  } catch (error) {
    console.error('定期状态检查失败:', error);
  }
}

function startStatusCheck() {
  // 创建智能轮询管理器
  pollingManager = createSmartPolling(checkServicesStatus, {
    visibleInterval: 30000,     // 页面可见时30秒检查一次
    hiddenInterval: 120000,     // 页面隐藏时2分钟检查一次
    stopAfterHidden: 600000,    // 页面隐藏10分钟后停止检查
    stopImmediately: false      // 不立即停止，给一些缓冲时间
  });

  pollingManager.start();
  console.log('[ServiceStatus] 智能状态检查已启动');
}

function stopStatusCheck() {
  if (pollingManager) {
    pollingManager.destroy();
    pollingManager = null;
    console.log('[ServiceStatus] 智能状态检查已停止');
  }
}

function handleClose() {
  showDetailDialog.value = false;
}

onMounted(() => {
  initStatus();
  startStatusCheck();
});

onUnmounted(() => {
  stopStatusCheck();
});
</script>

<style scoped lang="scss">
.service-status-indicator {
  position: fixed;
  bottom: 20px;
  left: 20px;
  z-index: 1000;
}

.status-dot {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  i {
    font-size: 16px;
    color: white;
  }
}

.status-online {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.status-offline {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.status-partial {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.status-unknown {
  background: linear-gradient(135deg, #909399, #a6a9ad);
}

.service-list {
  max-height: 400px;
  overflow-y: auto;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;

  &:last-child {
    border-bottom: none;
  }
}

.service-info {
  flex: 1;

  .service-name {
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
  }

  .service-url {
    font-size: 12px;
    color: #909399;
    font-family: monospace;
  }
}

.service-status {
  text-align: right;

  .error-message {
    font-size: 12px;
    color: #f56c6c;
    margin-top: 4px;
    max-width: 150px;
    word-break: break-word;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
