<template>
  <div class="task-priority">
    <el-select
      v-model="selectedPriority"
      :placeholder="placeholder"
      :size="size"
      :disabled="disabled"
      @change="handlePriorityChange"
      class="priority-select"
    >
      <el-option
        v-for="priority in priorityOptions"
        :key="priority.value"
        :label="priority.label"
        :value="priority.value"
        :class="getPriorityClass(priority.value)"
      >
        <div class="priority-option">
          <el-tag
            :type="getPriorityTagType(priority.value)"
            size="small"
            class="priority-tag"
          >
            {{ priority.value }}
          </el-tag>
          <span class="priority-name">{{ priority.label }}</span>
          <span class="priority-desc">{{ priority.description }}</span>
        </div>
      </el-option>
    </el-select>
    
    <!-- 优先级显示模式 -->
    <div v-if="displayMode" class="priority-display">
      <el-tag
        :type="getPriorityTagType(selectedPriority)"
        :size="size"
        class="priority-display-tag"
      >
        {{ getPriorityLabel(selectedPriority) }}
      </el-tag>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElSelect, ElOption, ElTag } from 'element-plus'

interface PriorityOption {
  value: number
  label: string
  description: string
  color: string
}

interface Props {
  modelValue?: number
  placeholder?: string
  size?: 'large' | 'default' | 'small'
  disabled?: boolean
  displayMode?: boolean // 是否为显示模式（只显示，不可编辑）
}

interface Emits {
  (e: 'update:modelValue', value: number): void
  (e: 'change', value: number): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: 5,
  placeholder: '选择优先级',
  size: 'default',
  disabled: false,
  displayMode: false
})

const emit = defineEmits<Emits>()

const selectedPriority = ref(props.modelValue)

// 优先级选项配置
const priorityOptions: PriorityOption[] = [
  { value: 1, label: '紧急', description: '需要立即执行的任务', color: '#F56C6C' },
  { value: 2, label: '高', description: '重要且紧急的任务', color: '#E6A23C' },
  { value: 3, label: '较高', description: '比正常优先级稍高的任务', color: '#E6A23C' },
  { value: 5, label: '正常', description: '常规任务，默认优先级', color: '#409EFF' },
  { value: 7, label: '较低', description: '比正常优先级稍低的任务', color: '#909399' },
  { value: 8, label: '低', description: '不紧急的任务', color: '#909399' },
  { value: 10, label: '最低', description: '可以延后执行的任务', color: '#C0C4CC' }
]

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedPriority.value = newValue || 5
}, { immediate: true })

// 处理优先级变更
const handlePriorityChange = (value: number) => {
  emit('update:modelValue', value)
  emit('change', value)
}

// 获取优先级标签类型
const getPriorityTagType = (priority: number): string => {
  if (priority <= 2) return 'danger'
  if (priority <= 3) return 'warning'
  if (priority <= 5) return 'primary'
  if (priority <= 8) return 'info'
  return ''
}

// 获取优先级CSS类
const getPriorityClass = (priority: number): string => {
  if (priority <= 2) return 'priority-urgent'
  if (priority <= 3) return 'priority-high'
  if (priority <= 5) return 'priority-normal'
  if (priority <= 8) return 'priority-low'
  return 'priority-lowest'
}

// 获取优先级标签文本
const getPriorityLabel = (priority: number): string => {
  const option = priorityOptions.find(p => p.value === priority)
  return option ? `${option.value} - ${option.label}` : `${priority} - 未知`
}
</script>

<style scoped>
.task-priority {
  display: inline-block;
}

.priority-select {
  min-width: 120px;
}

.priority-option {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.priority-tag {
  min-width: 24px;
  text-align: center;
}

.priority-name {
  font-weight: 500;
  min-width: 40px;
}

.priority-desc {
  color: var(--el-text-color-secondary);
  font-size: 12px;
  flex: 1;
}

.priority-display {
  display: inline-block;
}

.priority-display-tag {
  cursor: default;
}

/* 优先级选项样式 */
:deep(.el-select-dropdown__item.priority-urgent) {
  border-left: 3px solid #F56C6C;
}

:deep(.el-select-dropdown__item.priority-high) {
  border-left: 3px solid #E6A23C;
}

:deep(.el-select-dropdown__item.priority-normal) {
  border-left: 3px solid #409EFF;
}

:deep(.el-select-dropdown__item.priority-low) {
  border-left: 3px solid #909399;
}

:deep(.el-select-dropdown__item.priority-lowest) {
  border-left: 3px solid #C0C4CC;
}
</style>
