<template>
  <div v-if="isValidRoute" class="main">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-button @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
      <h2 class="page-title">IP资产详情</h2>
    </div>

    <!-- IP基本信息卡片 -->
    <el-card v-if="ipAsset" class="info-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>基本信息</span>
          <el-tag
            :type="ipAsset.status === 'ACTIVE' ? 'success' : 'info'"
            size="large"
          >
            {{ ipAsset.status === 'ACTIVE' ? '活跃' : '非活跃' }}
          </el-tag>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="IP地址">
              <div class="ip-info">
                <span class="ip-address">{{ ipAsset.ipAddress }}</span>
                <el-tag v-if="ipAsset.isIpv6" type="info" size="small" class="ml-2">
                  IPv6
                </el-tag>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="资产数量">
              <el-statistic :value="ipAsset.assetCount" :precision="0" />
            </el-descriptions-item>
            <el-descriptions-item label="首次发现">
              {{ formatDateTime(ipAsset.firstDiscovered) }}
            </el-descriptions-item>
            <el-descriptions-item label="最后发现">
              {{ formatDateTime(ipAsset.lastSeen) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
        <el-col :span="12">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="地理位置">
              <div v-if="ipAsset.locationCountry">
                {{ ipAsset.locationCountry }}
                <span v-if="ipAsset.locationProvince">{{ ipAsset.locationProvince }}</span>
                <span v-if="ipAsset.locationCity">{{ ipAsset.locationCity }}</span>
              </div>
              <span v-else class="text-placeholder">未知</span>
            </el-descriptions-item>
            <el-descriptions-item label="ISP">
              <span v-if="ipAsset.isp">{{ ipAsset.isp }}</span>
              <span v-else class="text-placeholder">未知</span>
            </el-descriptions-item>
            <el-descriptions-item label="ASN">
              <span v-if="ipAsset.asn">{{ ipAsset.asn }}</span>
              <span v-else class="text-placeholder">-</span>
            </el-descriptions-item>
            <el-descriptions-item label="组织">
              <span v-if="ipAsset.organization">{{ ipAsset.organization }}</span>
              <span v-else class="text-placeholder">未知</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>

      <!-- 端口和域名信息 -->
      <el-row :gutter="20" class="mt-4">
        <el-col :span="12">
          <div class="info-section">
            <h4>常见端口</h4>
            <div v-if="commonPorts.length > 0" class="ports-container">
              <el-tag
                v-for="port in commonPorts"
                :key="port"
                class="port-tag"
                size="small"
              >
                {{ port }}
              </el-tag>
            </div>
            <span v-else class="text-placeholder">无端口信息</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-section">
            <h4>关联域名</h4>
            <div v-if="associatedDomains.length > 0" class="domains-container">
              <el-tag
                v-for="domain in associatedDomains.slice(0, 5)"
                :key="domain"
                class="domain-tag"
                size="small"
                type="info"
              >
                {{ domain }}
              </el-tag>
              <span v-if="associatedDomains.length > 5" class="more-info">
                +{{ associatedDomains.length - 5 }} 个
              </span>
            </div>
            <span v-else class="text-placeholder">无域名信息</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 关联资产操作区域 -->
    <el-card class="assets-card mt-4" shadow="never">
      <template #header>
        <div class="card-header">
          <span>关联资产 ({{ assetsPagination.total }})</span>
        </div>
      </template>

      <!-- 资产操作按钮区域 -->
      <div class="assets-actions">
        <div class="assets-summary">
          <el-statistic
            :value="assetsPagination.total"
            title="资产总数"
            class="assets-stat"
          />
          <div class="assets-description">
            <p>该IP地址下共发现 <strong>{{ assetsPagination.total }}</strong> 个资产</p>
            <p class="text-muted">点击下方按钮进行资产管理操作</p>
          </div>
        </div>

        <div class="action-buttons">
          <el-button
            type="primary"
            size="large"
            @click="goToAssetManagement"
          >
            <el-icon><Setting /></el-icon>
            管理资产
          </el-button>

          <el-button
            size="large"
            @click="refreshAssets"
          >
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>

          <el-button
            size="large"
            @click="exportAssets"
          >
            <el-icon><Download /></el-icon>
            导出资产
          </el-button>

          <el-button
            size="large"
            @click="viewAssetStats"
          >
            <el-icon><DataAnalysis /></el-icon>
            资产统计
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import {
  ArrowLeft,
  Refresh,
  Setting,
  Download,
  DataAnalysis
} from "@element-plus/icons-vue";

// 路由
const route = useRoute();
const router = useRouter();

// 响应式数据
const ipAsset = ref<any>(null);
const assetsLoading = ref(false);

const assetsPagination = reactive({
  total: 0
});

// 计算属性
const isValidRoute = computed((): boolean => {
  const ipAddress = route.params.ip as string;
  return !!(ipAddress &&
           typeof ipAddress === 'string' &&
           ipAddress.trim() !== '' &&
           ipAddress !== 'undefined' &&
           ipAddress !== 'null' &&
           ipAddress.length > 0);
});

const commonPorts = computed((): string[] => {
  if (!ipAsset.value?.commonPorts) return [];
  try {
    return JSON.parse(ipAsset.value.commonPorts) as string[];
  } catch {
    return [];
  }
});

const associatedDomains = computed((): string[] => {
  if (!ipAsset.value?.associatedDomains) return [];
  try {
    return JSON.parse(ipAsset.value.associatedDomains) as string[];
  } catch {
    return [];
  }
});

// 增强的路由参数验证
function validateRouteParams(): string | null {
  const ipAddress = route.params.ip as string;

  // 多重验证条件
  if (!ipAddress ||
      typeof ipAddress !== 'string' ||
      ipAddress.trim() === '' ||
      ipAddress === 'undefined' ||
      ipAddress === 'null' ||
      ipAddress.length === 0) {

    console.error('Invalid IP parameter detected:', ipAddress);
    ElMessage.error('IP地址参数无效，正在返回列表页面');

    // 使用 replace 避免在历史记录中留下无效页面
    router.replace('/ip/index');
    return null;
  }

  return ipAddress;
}

// 方法定义
async function fetchIpAsset() {
  try {
    const ipAddress = validateRouteParams();
    if (!ipAddress) return;

    const response = await fetch(`/api/ips/by-address/${encodeURIComponent(ipAddress)}`);
    const result = await response.json();

    if (result.success && result.data) {
      ipAsset.value = result.data;
    } else {
      ElMessage.error(result.message || "获取IP资产信息失败");
      goBack();
    }
  } catch (error) {
    ElMessage.error("获取IP资产信息失败");
    console.error("获取IP资产信息失败:", error);
    goBack();
  }
}

async function fetchAssets() {
  assetsLoading.value = true;
  try {
    const ipAddress = validateRouteParams();
    if (!ipAddress) {
      assetsLoading.value = false;
      return;
    }

    // 只获取第一页数据来获取总数
    const params = new URLSearchParams({
      page: "0",
      size: "1",
      sortBy: "createdAt",
      sortDir: "desc"
    });

    const response = await fetch(`/api/ips/${encodeURIComponent(ipAddress)}/assets?${params}`);
    const result = await response.json();

    if (result.success && result.data) {
      assetsPagination.total = result.data.assets.total || 0;
    } else {
      ElMessage.error(result.message || "获取关联资产数量失败");
      assetsPagination.total = 0;
    }
  } catch (error) {
    ElMessage.error("获取关联资产数量失败");
    console.error("获取关联资产数量失败:", error);
  } finally {
    assetsLoading.value = false;
  }
}



function goBack() {
  // 安全的返回方式，避免使用 router.back() 可能导致的问题
  router.replace('/ip/index');
}

function refreshAssets() {
  fetchAssets();
}

function goToAssetManagement() {
  const ipAddress = validateRouteParams();
  if (!ipAddress) {
    ElMessage.error("IP地址参数无效");
    return;
  }

  // 跳转到资产列表页面，并传递IP过滤参数
  router.push({
    path: '/asset/index',
    query: {
      ip: ipAddress,
      from: 'ip-detail'
    }
  });
}

function exportAssets() {
  const ipAddress = validateRouteParams();
  if (!ipAddress) {
    ElMessage.error("IP地址参数无效");
    return;
  }

  ElMessage.info("导出功能开发中，敬请期待");
  // TODO: 实现资产导出功能
}

function viewAssetStats() {
  const ipAddress = validateRouteParams();
  if (!ipAddress) {
    ElMessage.error("IP地址参数无效");
    return;
  }

  ElMessage.info("资产统计功能开发中，敬请期待");
  // TODO: 实现资产统计功能
}

// 生命周期
onMounted(() => {
  // 在组件挂载时再次验证路由参数
  const ipParam = validateRouteParams();
  if (!ipParam) {
    // 如果参数无效，不执行后续操作
    return;
  }

  fetchIpAsset();
  fetchAssets();
});
</script>

<style scoped>
.main {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.back-button {
  margin-right: 16px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.info-card {
  margin-bottom: 20px;
}

.ip-info {
  display: flex;
  align-items: center;
}

.ip-address {
  font-family: monospace;
  font-weight: 500;
  font-size: 16px;
}

.ml-2 {
  margin-left: 8px;
}

.mt-4 {
  margin-top: 16px;
}

.info-section {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.info-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.ports-container,
.domains-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.port-tag,
.domain-tag {
  font-size: 11px;
  height: 20px;
  line-height: 18px;
}

.more-info {
  color: #909399;
  font-size: 12px;
}

.assets-card {
  margin-top: 20px;
}

.assets-actions {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 20px 0;
}

.assets-summary {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.assets-stat {
  flex-shrink: 0;
}

.assets-description {
  flex: 1;
}

.assets-description p {
  margin: 0 0 8px 0;
  font-size: 14px;
  line-height: 1.5;
}

.assets-description .text-muted {
  color: #909399;
  font-size: 13px;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  min-width: 140px;
  height: 48px;
  font-size: 14px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.action-buttons .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-buttons .el-button--primary {
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  border: none;
}

.action-buttons .el-button--primary:hover {
  background: linear-gradient(135deg, #337ecc 0%, #5aa3e6 100%);
}
</style>
