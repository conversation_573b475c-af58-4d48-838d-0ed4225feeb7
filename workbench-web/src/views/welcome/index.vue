<template>
  <div class="welcome-container">

    <!-- 数据刷新控制区域 -->
    <div class="refresh-control-area">
      <div class="refresh-controls">
        <div class="auto-refresh-control">
          <el-switch
            v-model="autoRefreshEnabled"
            :disabled="isRefreshing"
            @change="handleAutoRefreshToggle"
            active-text="自动刷新"
            inactive-text="手动模式"
            active-color="#409EFF"
            inactive-color="#C0C4CC"
          />
          <span class="refresh-interval-text">
            {{ getRefreshStatusText() }}
          </span>
        </div>

        <el-button
          type="primary"
          :loading="isRefreshing"
          @click="handleManualRefresh"
          size="default"
        >
          <IconifyIconOffline icon="ep:refresh" class="mr-1" />
          {{ isRefreshing ? '刷新中...' : '刷新' }}
        </el-button>
      </div>

      <!-- 刷新状态提示 -->
      <div v-if="refreshStatus" class="refresh-status">
        <el-alert
          :title="refreshStatus.message"
          :type="refreshStatus.type"
          :closable="true"
          @close="refreshStatus = null"
          show-icon
        />
      </div>
    </div>

    <!-- 主要布局：四方格布局 (2行2列) -->
    <div class="grid-container">
          <!-- 第1格：System系统信息 -->
          <div class="grid-item grid-item-1">
            <el-card class="grid-card system-card" shadow="hover">
              <div class="card-header">
                <div class="card-title">
                  <div class="title-icon system-icon">
                    <IconifyIconOffline icon="ep:monitor" width="20" height="20" />
                  </div>
                  <div class="title-text">
                    <h3>System</h3>
                    <p>workbench-system 模块监控</p>
                  </div>
                </div>
                <el-tag
                  :type="getStatusType(mapStatusToChinese(systemStatus.status))"
                  size="small"
                  effect="dark"
                  class="status-badge"
                >
                  {{ mapStatusToChinese(systemStatus.status) }}
                </el-tag>
              </div>
              <div class="card-content">
                <div class="metric-grid system-metrics">
                  <div class="metric-item">
                    <div class="metric-label">JVM版本</div>
                    <div class="metric-value">{{ systemStatus.jvm.version || 'N/A' }}</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">JVM厂商</div>
                    <div class="metric-value">{{ systemStatus.jvm.vendor || 'N/A' }}</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">运行时长</div>
                    <div class="metric-value">{{ formatUptime(systemStatus.uptime) }}</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">内存使用率</div>
                    <div class="metric-value">{{ (systemStatus.memory.heapUsageRatio * 100).toFixed(1) }}%</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">堆内存使用</div>
                    <div class="metric-value">{{ formatMemory(systemStatus.memory.heapUsed) }}</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">堆内存最大</div>
                    <div class="metric-value">{{ formatMemory(systemStatus.memory.heapMax) }}</div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 第2格：Scanner扫描器信息 -->
          <div class="grid-item grid-item-2">
            <el-card class="grid-card scanner-card" shadow="hover">
              <div class="card-header">
                <div class="card-title">
                  <div class="title-icon scanner-icon">
                    <IconifyIconOffline icon="ep:search" width="20" height="20" />
                  </div>
                  <div class="title-text">
                    <h3>Scanner</h3>
                    <p>workbench-scanner 模块监控</p>
                  </div>
                </div>
                <el-tag type="success" size="small" effect="dark" class="status-badge">
                  {{ scannerStatus.status }}
                </el-tag>
              </div>
              <div class="card-content">
                <div class="metric-grid scanner-metrics">
                  <div class="metric-item">
                    <div class="metric-label">当前任务</div>
                    <div class="metric-value highlight-success">{{ scannerStatus.currentTaskName || '无' }}</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">浏览器实例</div>
                    <div class="metric-value">{{ scannerStatus.browserInstances || 0 }}</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">内存占用</div>
                    <div class="metric-value">{{ scannerStatus.memoryUsage || 'N/A' }}</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">处理速度</div>
                    <div class="metric-value">{{ scannerStatus.processingRate || 0 }}/min</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">最后执行</div>
                    <div class="metric-value">{{ scannerStatus.lastTaskTime || '无' }}</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">队列长度</div>
                    <div class="metric-value" :class="getQueueLengthClass(scannerStatus.queueSize)">
                      {{ scannerStatus.queueSize || 0 }}
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 第3格：资产统计 -->
          <div class="grid-item grid-item-3">
            <el-card class="grid-card asset-card" shadow="hover">
              <div class="card-header">
                <div class="card-title">
                  <div class="title-icon asset-icon">
                    <IconifyIconOffline icon="ep:box" width="20" height="20" />
                  </div>
                  <div class="title-text">
                    <h3>资产统计</h3>
                    <p>资产数量变化与增长趋势</p>
                  </div>
                </div>
              </div>
              <div class="card-content">
                <div class="metric-grid asset-metrics">
                  <div class="metric-item">
                    <div class="metric-label">总资产数</div>
                    <div class="metric-value">{{ assetStats.totalAssets || 0 }}</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">今日新增</div>
                    <div class="metric-value">{{ assetStats.todayNewAssets || 0 }}</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">昨日资产</div>
                    <div class="metric-value">{{ assetStats.yesterdayAssets || 0 }}</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">增长率</div>
                    <div class="metric-value">{{ assetStats.growthRate ? assetStats.growthRate.toFixed(1) + '%' : '0%' }}</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">本周新增</div>
                    <div class="metric-value">{{ assetStats.weeklyNewAssets || 0 }}</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">本月新增</div>
                    <div class="metric-value">{{ assetStats.monthlyNewAssets || 0 }}</div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 第4格：任务管理统计 -->
          <div class="grid-item grid-item-4">
            <el-card class="grid-card task-card" shadow="hover">
              <div class="card-header">
                <div class="card-title">
                  <div class="title-icon task-icon">
                    <IconifyIconOffline icon="ep:list" width="20" height="20" />
                  </div>
                  <div class="title-text">
                    <h3>任务管理统计</h3>
                    <p>任务状态分布与执行历史</p>
                  </div>
                </div>
              </div>
              <div class="card-content">
                <div class="metric-grid task-metrics">
                  <div class="metric-item">
                    <div class="metric-label">任务总数</div>
                    <div class="metric-value">{{ taskStats.totalTasks || 0 }}</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">已完成</div>
                    <div class="metric-value highlight-success">{{ taskStats.completedTasks || 0 }}</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">运行中</div>
                    <div class="metric-value highlight-warning">{{ taskStats.runningTasks || 0 }}</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">等待中</div>
                    <div class="metric-value">{{ taskStats.pendingTasks || 0 }}</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">执行次数</div>
                    <div class="metric-value">{{ taskStats.totalExecutions || 0 }}</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-label">成功率</div>
                    <div class="metric-value">{{ taskStats.successRate || 0 }}%</div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </div>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { IconifyIconOffline } from "@/components/ReIcon";
import { ElMessage } from "element-plus";

// 系统状态数据
const systemStatus = ref({
  status: "运行中",
  uptime: 0,
  memory: {
    heapUsed: 0,
    heapMax: 0,
    heapUsageRatio: 0
  },
  jvm: {
    version: "",
    vendor: ""
  }
});

// Scanner服务状态
const scannerStatus = ref({
  status: "运行中",
  currentTaskName: "无",
  activeTasks: 0,
  queueSize: 0,
  browserInstances: 0,
  lastTaskTime: "无",
  memoryUsage: "N/A",
  processingRate: 0
});

// 任务统计数据
const taskStats = ref({
  totalTasks: 0,
  completedTasks: 0,
  pendingTasks: 0,
  runningTasks: 0,
  failedTasks: 0
});

// 资产统计数据
const assetStats = ref({
  totalAssets: 0,
  todayNewAssets: 0,
  yesterdayAssets: 0,
  growthRate: 0,
  weeklyNewAssets: 0,
  monthlyNewAssets: 0
});

// 队列统计数据
const queueStats = ref({
  totalWaitingTasks: 0,
  healthy: true,
  taskQueue: {
    messageCount: 0,
    consumerCount: 0,
    available: true
  },
  updateQueue: {
    messageCount: 0,
    consumerCount: 0,
    available: true
  },
  deadLetterQueue: {
    messageCount: 0,
    consumerCount: 0,
    available: true
  }
});

// 时间选项
const timeOptions = [
  { value: "today", label: "今日" },
  { value: "yesterday", label: "昨日" },
  { value: "week", label: "本周" }
];

// 刷新控制状态
const isRefreshing = ref(false);
const autoRefreshEnabled = ref(false);
const refreshStatus = ref<{
  message: string;
  type: 'success' | 'warning' | 'error' | 'info';
} | null>(null);

// 倒计时状态
const countdown = ref(10);

// 定时器
let refreshTimer: NodeJS.Timeout | null = null;
let autoRefreshTimer: NodeJS.Timeout | null = null;
let countdownTimer: NodeJS.Timeout | null = null;

// 清理定时器
onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
  if (autoRefreshTimer) {
    clearInterval(autoRefreshTimer);
  }
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
});

// API调用函数
async function apiCall(url: string, options = {}) {
  const defaultOptions = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    ...options
  };

  try {
    const response = await fetch(url, defaultOptions);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    const result = await response.json();
    return result;
  } catch (error) {
    console.error(`API调用失败 [${url}]:`, error);
    return {
      success: false,
      message: error instanceof Error ? error.message : '未知错误',
      data: null
    };
  }
}

// 加载系统状态
async function loadSystemStatus() {
  try {
    console.log('开始加载系统状态...');
    const result = await apiCall('/api/system/status');
    console.log('系统状态API响应:', result);

    if (result.success && result.data) {
      const newSystemStatus = {
        status: result.data.status || "运行中",
        uptime: result.data.uptime || 0,
        memory: {
          heapUsed: result.data.memory?.heapUsed || 0,
          heapMax: result.data.memory?.heapMax || 0,
          heapUsageRatio: result.data.memory?.heapUsageRatio || 0
        },
        jvm: {
          version: result.data.jvm?.version || "",
          vendor: result.data.jvm?.vendor || ""
        }
      };

      console.log('更新系统状态数据:', newSystemStatus);
      systemStatus.value = newSystemStatus;
      console.log('系统状态更新完成，当前值:', systemStatus.value);
    } else {
      console.warn('系统状态API返回失败或无数据:', result);
      // 使用默认值
      systemStatus.value = {
        status: "离线",
        uptime: 0,
        memory: { heapUsed: 0, heapMax: 0, heapUsageRatio: 0 },
        jvm: { version: "N/A", vendor: "N/A" }
      };
    }
  } catch (error) {
    console.error('加载系统状态失败:', error);
    // 设置离线状态的默认值
    systemStatus.value = {
      status: "离线",
      uptime: 0,
      memory: { heapUsed: 0, heapMax: 0, heapUsageRatio: 0 },
      jvm: { version: "N/A", vendor: "N/A" }
    };
  }
}

// 加载Scanner状态
async function loadScannerStatus() {
  try {
    console.log('开始加载Scanner状态...');
    const result = await apiCall('/api/scanner/status');
    console.log('Scanner状态API响应:', result);

    if (result.success && result.data) {
      const newScannerStatus = {
        status: result.data.status || "运行中",
        currentTaskName: result.data.currentTaskName || "无",
        activeTasks: result.data.activeTasks || 0,
        queueSize: result.data.queueSize || 0,
        browserInstances: result.data.browserInstances || 0,
        lastTaskTime: result.data.lastTaskTime || "无",
        memoryUsage: result.data.memoryUsage || "N/A",
        processingRate: result.data.processingRate || 0
      };

      console.log('更新Scanner状态数据:', newScannerStatus);
      scannerStatus.value = newScannerStatus;
      console.log('Scanner状态更新完成，当前值:', scannerStatus.value);
    } else {
      console.warn('Scanner状态API返回失败或无数据:', result);
      // 使用默认值
      scannerStatus.value = {
        status: "离线",
        activeTasks: 0,
        queueSize: 0,
        browserInstances: 0,
        lastTaskTime: "无",
        memoryUsage: "N/A",
        processingRate: 0
      };
    }
  } catch (error) {
    console.error('加载Scanner状态失败:', error);
    // 设置离线状态的默认值
    scannerStatus.value = {
      status: "离线",
      activeTasks: 0,
      queueSize: 0,
      browserInstances: 0,
      lastTaskTime: "无",
      memoryUsage: "N/A",
      processingRate: 0
    };
  }
}

// 加载任务统计
async function loadTaskStats() {
  try {
    const result = await apiCall('/api/task/stats');
    if (result.success) {
      taskStats.value = result.data;
    } else {
      // 使用默认值
      taskStats.value = {
        totalTasks: 0,
        completedTasks: 0,
        pendingTasks: 0,
        runningTasks: 0,
        failedTasks: 0
      };
    }
  } catch (error) {
    console.error('加载任务统计失败:', error);
    // 设置默认值
    taskStats.value = {
      totalTasks: 0,
      completedTasks: 0,
      pendingTasks: 0,
      runningTasks: 0,
      failedTasks: 0
    };
  }
}

// 加载队列统计
async function loadQueueStats() {
  try {
    const result = await apiCall('/api/queue/stats');
    if (result.success) {
      queueStats.value = result.data;
      // 更新Scanner状态中的队列长度
      scannerStatus.value.queueSize = result.data.totalWaitingTasks;
    } else {
      // 使用默认值
      queueStats.value = {
        totalWaitingTasks: 0,
        healthy: false,
        taskQueue: { messageCount: 0, consumerCount: 0, available: false },
        updateQueue: { messageCount: 0, consumerCount: 0, available: false },
        deadLetterQueue: { messageCount: 0, consumerCount: 0, available: false }
      };
    }
  } catch (error) {
    console.error('加载队列统计失败:', error);
    // 设置默认值
    queueStats.value = {
      totalWaitingTasks: 0,
      healthy: false,
      taskQueue: { messageCount: 0, consumerCount: 0, available: false },
      updateQueue: { messageCount: 0, consumerCount: 0, available: false },
      deadLetterQueue: { messageCount: 0, consumerCount: 0, available: false }
    };
  }
}

// 获取队列长度的样式类
function getQueueLengthClass(queueLength: number) {
  if (queueLength === 0) {
    return 'highlight-success'; // 绿色 - 无等待任务
  } else if (queueLength <= 5) {
    return 'highlight-info'; // 蓝色 - 少量等待任务
  } else if (queueLength <= 20) {
    return 'highlight-warning'; // 橙色 - 中等等待任务
  } else {
    return 'highlight-danger'; // 红色 - 大量等待任务
  }
}

// 加载资产统计
async function loadAssetStats() {
  try {
    const result = await apiCall('/api/asset/stats');
    if (result.success) {
      const { totalAssets, todayNewAssets, yesterdayAssets } = result.data;
      assetStats.value = {
        totalAssets,
        todayNewAssets,
        yesterdayAssets,
        growthRate: yesterdayAssets > 0 ? ((todayNewAssets / yesterdayAssets) * 100) : 0,
        weeklyNewAssets: result.data.weeklyNewAssets || todayNewAssets * 7,
        monthlyNewAssets: result.data.monthlyNewAssets || todayNewAssets * 30
      };
    } else {
      // 使用默认值
      assetStats.value = {
        totalAssets: 0,
        todayNewAssets: 0,
        yesterdayAssets: 0,
        growthRate: 0,
        weeklyNewAssets: 0,
        monthlyNewAssets: 0
      };
    }
  } catch (error) {
    console.error('加载资产统计失败:', error);
    // 设置默认值
    assetStats.value = {
      totalAssets: 0,
      todayNewAssets: 0,
      yesterdayAssets: 0,
      growthRate: 0,
      weeklyNewAssets: 0,
      monthlyNewAssets: 0
    };
  }
}

// 加载所有数据
async function loadAllData() {
  console.log('开始加载所有数据...');
  try {
    await Promise.all([
      loadSystemStatus(),
      loadScannerStatus(),
      loadTaskStats(),
      loadAssetStats(),
      loadQueueStats()
    ]);
    console.log('所有数据加载完成');
    return true;
  } catch (error) {
    console.error('加载数据时发生错误:', error);
    // 即使部分失败，也要确保页面能正常显示
    return false;
  }
}

// 格式化运行时间
function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  let result = "";
  if (days > 0) result += `${days}天`;
  if (hours > 0) result += `${hours}小时`;
  if (minutes > 0) result += `${minutes}分钟`;
  if (secs > 0 || result === "") result += `${secs}秒`;

  return result;
}

// 格式化内存大小
function formatMemory(bytes: number): string {
  const mb = bytes / (1024 * 1024);
  return `${mb.toFixed(1)} MB`;
}

// 手动刷新处理函数
async function handleManualRefresh() {
  if (isRefreshing.value) return;

  isRefreshing.value = true;
  refreshStatus.value = null;

  try {
    const success = await loadAllData();

    if (success) {
      refreshStatus.value = {
        message: '数据刷新成功',
        type: 'success'
      };
      ElMessage.success('数据已刷新');
    } else {
      refreshStatus.value = {
        message: '数据刷新部分失败，请检查网络连接',
        type: 'warning'
      };
      ElMessage.warning('数据刷新部分失败');
    }
  } catch (error) {
    console.error('手动刷新失败:', error);
    refreshStatus.value = {
      message: '数据刷新失败，请稍后重试',
      type: 'error'
    };
    ElMessage.error('数据刷新失败');
  } finally {
    isRefreshing.value = false;

    // 3秒后自动隐藏状态提示
    setTimeout(() => {
      refreshStatus.value = null;
    }, 3000);
  }
}

// 自动刷新开关处理函数
function handleAutoRefreshToggle(enabled: boolean) {
  if (enabled) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
}

// 启动倒计时
function startCountdown() {
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }

  countdown.value = 10;
  countdownTimer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      countdown.value = 10;
      // 触发自动刷新
      if (autoRefreshEnabled.value && !isRefreshing.value) {
        executeAutoRefresh();
      }
    }
  }, 1000);
}

// 停止倒计时
function stopCountdown() {
  if (countdownTimer) {
    clearInterval(countdownTimer);
    countdownTimer = null;
  }
}

// 执行自动刷新
async function executeAutoRefresh() {
  console.log('执行自动刷新...');
  try {
    await loadAllData();
    console.log('自动刷新完成');
  } catch (error) {
    console.error('自动刷新失败:', error);
  }
}

// 启动自动刷新
function startAutoRefresh() {
  startCountdown();
  console.log('自动刷新已启动，间隔10秒');
}

// 停止自动刷新
function stopAutoRefresh() {
  stopCountdown();
  console.log('自动刷新已停止');
}

// 状态映射函数：将英文状态转换为中文
function mapStatusToChinese(status: string): string {
  const statusMap: Record<string, string> = {
    'running': '运行中',
    'stopped': '已停止',
    'offline': '离线',
    'maintenance': '维护中',
    'error': '错误',
    'unknown': '未知'
  };
  return statusMap[status.toLowerCase()] || status;
}

// 获取刷新状态文本
function getRefreshStatusText(): string {
  if (!autoRefreshEnabled.value) {
    return '';
  }

  if (isRefreshing.value) {
    return '(刷新中...)';
  }

  return `(${countdown.value}秒后)`;
}

// 获取状态类型
function getStatusType(status: string): string {
  switch (status) {
    case "运行中":
    case "running":
      return "success";
    case "离线": return "danger";
    case "维护中": return "warning";
    default: return "info"; // 使用有效的Element Plus类型
  }
}

// 页面挂载时加载数据
onMounted(async () => {
  console.log('Welcome页面已挂载，开始加载数据...');
  try {
    await loadAllData();
    console.log('所有数据加载完成');
  } catch (error) {
    console.error('数据加载失败:', error);
  }
});

// 页面卸载时清理
onUnmounted(() => {
  console.log('Welcome页面已卸载');
  // 确保清理所有定时器
  stopAutoRefresh();
});
</script>

<style lang="scss" scoped>
/* PureAdmin风格四分屏布局 */

/* 数据刷新控制区域样式 */
.refresh-control-area {
  margin: 2px 0 8px 0; /* 最小化上边距，紧贴页面顶部 */
  flex-shrink: 0; /* 防止被压缩 */

  .refresh-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 16px; /* 进一步减少内边距 */
    background: var(--el-bg-color-page);
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px; /* 减少圆角 */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* 减少阴影 */

    .auto-refresh-control {
      display: flex;
      align-items: center;
      gap: 8px;

      .refresh-interval-text {
        font-size: 12px;
        color: var(--el-text-color-regular);
        margin-left: 4px;
        font-weight: 500;
      }
    }


  }

  .refresh-status {
    margin-top: 12px;

    :deep(.el-alert) {
      border-radius: 6px;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .refresh-control-area {
    .refresh-controls {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .auto-refresh-control {
        justify-content: center;
      }
    }
  }
}

/* Element Plus组件样式重置 */
:deep(.el-card) {
  --el-card-border-color: none;

  .el-progress--line {
    width: 85%;
  }

  .el-progress-bar__innerText {
    font-size: 15px;
  }

  .el-scrollbar__bar {
    display: none;
  }
}

/* 页面容器 - 桌面端精确适配 */
.welcome-container {
  padding: 4px 20px 0; /* 最小化顶部内边距 */
  height: 100vh; /* 使用全视窗高度 */
  overflow: hidden; /* 移除滚动条 */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* 四方格布局 - 桌面端精确填充 */
.grid-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 12px; /* 进一步减少间距 */
  flex: 1; /* 占用剩余所有空间 */
  min-height: 0; /* 允许收缩 */
  max-width: 100%;
  margin-bottom: 8px; /* 底部留少量空间 */
}

.grid-item {
  display: flex;
  flex-direction: column;
}

/* 卡片样式 - 桌面端自适应高度 */
.grid-card {
  height: 100%; /* 填满网格单元格 */
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  background: #ffffff;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  &:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
    border-color: #d1d5db;
  }
}

/* 卡片头部 - 优化空间利用 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f3f4f6;
  background: #fafafa;
  min-height: 64px;
  flex-shrink: 0;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.title-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title-text {
  flex: 1;

  h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 2px 0;
    color: #111827;
    line-height: 1.25;
    letter-spacing: -0.025em;
  }

  p {
    font-size: 13px;
    margin: 0;
    color: #6b7280;
    line-height: 1.4;
    font-weight: 400;
  }
}

.status-badge {
  flex-shrink: 0;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

/* 卡片内容 - 桌面端优化布局 */
.card-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center; /* 内容垂直居中 */
  background: #ffffff;
}

.metric-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  align-items: stretch;
}

.system-metrics,
.scanner-metrics,
.asset-metrics,
.task-metrics {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: repeat(3, auto);
  gap: 12px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  padding: 12px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  min-height: 65px;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    transform: translateY(-1px);
  }

  .metric-label {
    font-size: 12px;
    color: #64748b;
    margin-bottom: 4px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }

  .metric-value {
    font-size: 16px;
    font-weight: 700;
    color: #0f172a;
    word-break: break-word;
    line-height: 1.2;

    &.highlight-success {
      font-size: 18px;
      font-weight: 800;
      color: #059669;
    }

    &.highlight-info {
      font-size: 18px;
      font-weight: 800;
      color: #0284c7;
    }

    &.highlight-warning {
      font-size: 18px;
      font-weight: 800;
      color: #d97706;
    }

    &.highlight-danger {
      font-size: 18px;
      font-weight: 800;
      color: #dc2626;
    }
  }
}

/* 图标样式 */
.system-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.scanner-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.asset-icon {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: white;
}

.task-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

/* 桌面端专用布局 - 移除响应式媒体查询 */
</style>
