/**
 * 客户端心跳服务
 * 向后端发送页面活跃状态，帮助后端优化资源使用
 */

import { onVisibilityChange, isPageVisible } from './pageVisibility';

class ClientHeartbeatService {
  private clientId: string;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private visibilityUnsubscribe: (() => void) | null = null;
  private isRunning = false;
  private currentPageUrl = '';

  // 心跳配置
  private readonly HEARTBEAT_INTERVAL = 60000; // 1分钟发送一次心跳
  private readonly VISIBILITY_CHANGE_IMMEDIATE_HEARTBEAT = true; // 可见性变化时立即发送心跳

  constructor() {
    // 生成唯一的客户端ID
    this.clientId = this.generateClientId();
    this.currentPageUrl = window.location.href;
    
    console.log(`[ClientHeartbeat] 客户端心跳服务初始化, clientId: ${this.clientId}`);
  }

  /**
   * 生成唯一的客户端ID
   */
  private generateClientId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 15);
    return `client_${timestamp}_${random}`;
  }

  /**
   * 发送心跳到后端
   */
  private async sendHeartbeat(isVisible?: boolean): Promise<void> {
    try {
      const visible = isVisible !== undefined ? isVisible : isPageVisible();
      const currentUrl = window.location.href;
      
      // 更新当前页面URL
      if (currentUrl !== this.currentPageUrl) {
        this.currentPageUrl = currentUrl;
        console.log(`[ClientHeartbeat] 页面URL变化: ${currentUrl}`);
      }

      const response = await fetch('/api/client/heartbeat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          clientId: this.clientId,
          pageUrl: this.currentPageUrl,
          isVisible: visible.toString()
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`[ClientHeartbeat] 心跳发送成功, 活跃客户端: ${result.activeClients}, 页面可见: ${visible}`);
      } else {
        console.warn(`[ClientHeartbeat] 心跳发送失败: HTTP ${response.status}`);
      }
    } catch (error) {
      console.error('[ClientHeartbeat] 心跳发送出错:', error);
    }
  }

  /**
   * 通知后端客户端断开连接
   */
  private async notifyDisconnect(): Promise<void> {
    try {
      const response = await fetch(`/api/client/disconnect/${this.clientId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        console.log('[ClientHeartbeat] 断开连接通知发送成功');
      } else {
        console.warn(`[ClientHeartbeat] 断开连接通知失败: HTTP ${response.status}`);
      }
    } catch (error) {
      console.error('[ClientHeartbeat] 断开连接通知出错:', error);
    }
  }

  /**
   * 启动心跳服务
   */
  public start(): void {
    if (this.isRunning) {
      console.warn('[ClientHeartbeat] 心跳服务已在运行');
      return;
    }

    this.isRunning = true;
    console.log('[ClientHeartbeat] 启动心跳服务');

    // 立即发送一次心跳
    this.sendHeartbeat();

    // 设置定期心跳
    this.heartbeatInterval = setInterval(() => {
      this.sendHeartbeat();
    }, this.HEARTBEAT_INTERVAL);

    // 监听页面可见性变化
    this.visibilityUnsubscribe = onVisibilityChange((visible) => {
      console.log(`[ClientHeartbeat] 页面可见性变化: ${visible ? '可见' : '隐藏'}`);
      
      if (this.VISIBILITY_CHANGE_IMMEDIATE_HEARTBEAT) {
        // 可见性变化时立即发送心跳
        this.sendHeartbeat(visible);
      }
    });

    // 监听页面卸载事件
    window.addEventListener('beforeunload', this.handleBeforeUnload);
    window.addEventListener('unload', this.handleUnload);

    // 监听页面隐藏事件（移动端兼容）
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
  }

  /**
   * 停止心跳服务
   */
  public stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    console.log('[ClientHeartbeat] 停止心跳服务');

    // 清理定时器
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    // 清理可见性监听器
    if (this.visibilityUnsubscribe) {
      this.visibilityUnsubscribe();
      this.visibilityUnsubscribe = null;
    }

    // 清理事件监听器
    window.removeEventListener('beforeunload', this.handleBeforeUnload);
    window.removeEventListener('unload', this.handleUnload);
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);

    // 通知后端断开连接
    this.notifyDisconnect();
  }

  /**
   * 处理页面即将卸载事件
   */
  private handleBeforeUnload = () => {
    console.log('[ClientHeartbeat] 页面即将卸载');
    // 使用 sendBeacon 发送最后的心跳，确保在页面卸载时也能发送
    this.sendBeaconHeartbeat(false);
  };

  /**
   * 处理页面卸载事件
   */
  private handleUnload = () => {
    console.log('[ClientHeartbeat] 页面已卸载');
    this.stop();
  };

  /**
   * 处理页面可见性变化事件
   */
  private handleVisibilityChange = () => {
    if (document.hidden) {
      console.log('[ClientHeartbeat] 页面隐藏，发送隐藏状态心跳');
      this.sendBeaconHeartbeat(false);
    }
  };

  /**
   * 使用 sendBeacon 发送心跳（适用于页面卸载时）
   */
  private sendBeaconHeartbeat(isVisible: boolean): void {
    try {
      const data = new URLSearchParams({
        clientId: this.clientId,
        pageUrl: this.currentPageUrl,
        isVisible: isVisible.toString()
      });

      if (navigator.sendBeacon) {
        const success = navigator.sendBeacon('/api/client/heartbeat', data);
        console.log(`[ClientHeartbeat] Beacon心跳发送${success ? '成功' : '失败'}, 页面可见: ${isVisible}`);
      } else {
        // 降级到同步请求
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/api/client/heartbeat', false); // 同步请求
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        xhr.send(data);
        console.log(`[ClientHeartbeat] 同步心跳发送完成, 页面可见: ${isVisible}`);
      }
    } catch (error) {
      console.error('[ClientHeartbeat] Beacon心跳发送出错:', error);
    }
  }

  /**
   * 获取客户端ID
   */
  public getClientId(): string {
    return this.clientId;
  }

  /**
   * 检查服务是否运行中
   */
  public isServiceRunning(): boolean {
    return this.isRunning;
  }

  /**
   * 手动发送心跳
   */
  public async sendManualHeartbeat(): Promise<void> {
    if (this.isRunning) {
      await this.sendHeartbeat();
    }
  }
}

// 创建全局单例
const clientHeartbeatService = new ClientHeartbeatService();

// 自动启动心跳服务
if (typeof window !== 'undefined') {
  // 页面加载完成后启动
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      clientHeartbeatService.start();
    });
  } else {
    // 如果页面已经加载完成，立即启动
    clientHeartbeatService.start();
  }
}

export default clientHeartbeatService;

/**
 * 获取客户端心跳服务实例
 */
export function getClientHeartbeatService(): ClientHeartbeatService {
  return clientHeartbeatService;
}

/**
 * 启动客户端心跳服务
 */
export function startClientHeartbeat(): void {
  clientHeartbeatService.start();
}

/**
 * 停止客户端心跳服务
 */
export function stopClientHeartbeat(): void {
  clientHeartbeatService.stop();
}
