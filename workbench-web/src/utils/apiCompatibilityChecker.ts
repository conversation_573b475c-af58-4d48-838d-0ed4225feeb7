/**
 * API兼容性检查工具
 * 用于检查前端API调用是否与后端新格式兼容
 */

import { ElMessage } from "element-plus";

// API兼容性检查结果
interface CompatibilityCheckResult {
  isCompatible: boolean;
  issues: string[];
  suggestions: string[];
}

/**
 * 检查API响应格式兼容性
 * @param response API响应数据
 * @param expectedFormat 期望的格式类型
 * @returns 兼容性检查结果
 */
export function checkApiResponseCompatibility(
  response: any,
  expectedFormat: "standard" | "page" = "standard"
): CompatibilityCheckResult {
  const result: CompatibilityCheckResult = {
    isCompatible: true,
    issues: [],
    suggestions: []
  };

  // 检查是否为新的ApiResponse格式
  if (typeof response.success !== "boolean") {
    result.isCompatible = false;
    result.issues.push("响应缺少success字段");
    result.suggestions.push("后端应返回包含success字段的ApiResponse格式");
  }

  if (typeof response.message !== "string") {
    result.issues.push("响应缺少message字段");
    result.suggestions.push("后端应返回包含message字段的ApiResponse格式");
  }

  if (expectedFormat === "page" && response.success && response.data) {
    // 检查分页格式
    const data = response.data;
    if (!Array.isArray(data.list)) {
      result.isCompatible = false;
      result.issues.push("分页响应缺少list字段或list不是数组");
      result.suggestions.push("后端应返回PageResponse格式，包含list数组");
    }

    if (typeof data.total !== "number") {
      result.issues.push("分页响应缺少total字段或total不是数字");
      result.suggestions.push("后端应返回PageResponse格式，包含total数字");
    }

    if (typeof data.currentPage !== "number") {
      result.issues.push("分页响应缺少currentPage字段");
      result.suggestions.push(
        "后端应返回PageResponse格式，包含currentPage字段"
      );
    }
  }

  return result;
}

/**
 * 检查API请求参数兼容性
 * @param params 请求参数
 * @param apiEndpoint API端点
 * @returns 兼容性检查结果
 */
export function checkApiRequestCompatibility(
  params: any,
  apiEndpoint: string
): CompatibilityCheckResult {
  const result: CompatibilityCheckResult = {
    isCompatible: true,
    issues: [],
    suggestions: []
  };

  // 检查任务启动接口
  if (apiEndpoint.includes("/start")) {
    if (!params.timeRange) {
      result.isCompatible = false;
      result.issues.push("任务启动请求缺少timeRange参数");
      result.suggestions.push("请确保发送TaskStartRequest格式的数据");
    }
  }

  // 检查任务CRUD接口
  if (
    apiEndpoint.includes("/tasks") &&
    (apiEndpoint.includes("POST") || apiEndpoint.includes("PUT"))
  ) {
    if (!params.name || !params.rule) {
      result.isCompatible = false;
      result.issues.push("任务创建/更新请求缺少必要字段");
      result.suggestions.push("请确保包含name和rule字段");
    }
  }

  return result;
}

/**
 * 显示兼容性检查结果
 * @param result 检查结果
 * @param showSuccessMessage 是否显示成功消息
 */
export function showCompatibilityResult(
  result: CompatibilityCheckResult,
  showSuccessMessage: boolean = false
): void {
  if (result.isCompatible) {
    if (showSuccessMessage) {
      ElMessage.success("API格式兼容性检查通过");
    }
  } else {
    console.warn("API兼容性问题:", result.issues);
    console.info("建议:", result.suggestions);

    if (result.issues.length > 0) {
      ElMessage.warning(`API兼容性问题: ${result.issues[0]}`);
    }
  }
}

/**
 * 自动修复API响应格式
 * @param response 原始响应
 * @returns 修复后的响应
 */
export function autoFixApiResponse(response: any): any {
  // 如果已经是新格式，直接返回
  if (typeof response.success === "boolean") {
    return response;
  }

  // 尝试转换旧格式到新格式
  if (response.data || Array.isArray(response)) {
    return {
      success: true,
      message: "操作成功",
      data: response.data || response,
      timestamp: new Date().toISOString()
    };
  }

  // 如果是错误响应
  if (response.error || response.message) {
    return {
      success: false,
      message: response.error || response.message || "操作失败",
      errorCode: response.errorCode || "UNKNOWN_ERROR",
      timestamp: new Date().toISOString()
    };
  }

  // 默认成功响应
  return {
    success: true,
    message: "操作成功",
    data: response,
    timestamp: new Date().toISOString()
  };
}

/**
 * 自动修复分页响应格式
 * @param response 原始分页响应
 * @returns 修复后的分页响应
 */
export function autoFixPageResponse(response: any): any {
  const fixedResponse = autoFixApiResponse(response);

  if (fixedResponse.success && fixedResponse.data) {
    const data = fixedResponse.data;

    // 如果已经是PageResponse格式
    if (data.list && Array.isArray(data.list)) {
      return fixedResponse;
    }

    // 转换旧的分页格式
    if (Array.isArray(data.data) || Array.isArray(data)) {
      fixedResponse.data = {
        list: data.data || data,
        total: data.total || 0,
        currentPage: data.currentPage || data.page || 1,
        pageSize: data.pageSize || data.size || 10,
        totalPages: Math.ceil(
          (data.total || 0) / (data.pageSize || data.size || 10)
        ),
        hasNext: false,
        hasPrevious: false,
        isFirst: true,
        isLast: true
      };
    }
  }

  return fixedResponse;
}

/**
 * 开发模式下的API调试工具
 */
export class ApiDebugger {
  private static instance: ApiDebugger;
  private debugMode: boolean = false;

  static getInstance(): ApiDebugger {
    if (!ApiDebugger.instance) {
      ApiDebugger.instance = new ApiDebugger();
    }
    return ApiDebugger.instance;
  }

  enableDebug(): void {
    this.debugMode = true;
    console.log("API调试模式已启用");
  }

  disableDebug(): void {
    this.debugMode = false;
  }

  logApiCall(url: string, method: string, params?: any): void {
    if (this.debugMode) {
      console.group(`API调用: ${method} ${url}`);
      if (params) {
        console.log("请求参数:", params);
      }
      console.groupEnd();
    }
  }

  logApiResponse(url: string, response: any): void {
    if (this.debugMode) {
      console.group(`API响应: ${url}`);
      console.log("响应数据:", response);

      const compatibilityResult = checkApiResponseCompatibility(response);
      if (!compatibilityResult.isCompatible) {
        console.warn("兼容性问题:", compatibilityResult.issues);
        console.info("建议:", compatibilityResult.suggestions);
      }

      console.groupEnd();
    }
  }
}

// 导出调试器实例
export const apiDebugger = ApiDebugger.getInstance();

// 在开发环境下自动启用调试
if (import.meta.env.DEV) {
  apiDebugger.enableDebug();
}
