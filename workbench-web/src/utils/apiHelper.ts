/**
 * API响应处理工具
 * 提供统一的API响应格式处理和错误处理
 */

// API响应格式类型定义
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errorCode?: string;
  timestamp: string;
}

// 分页响应格式类型定义
export interface PageResponse<T = any> {
  list: Array<T>;
  total: number;
  currentPage: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
  isFirst: boolean;
  isLast: boolean;
}

/**
 * 处理API响应
 * @param response fetch响应对象
 * @returns 解析后的API响应数据
 */
export async function handleApiResponse<T = any>(
  response: Response
): Promise<ApiResponse<T>> {
  try {
    const result = await response.json();

    // 如果是新的ApiResponse格式
    if (typeof result.success === "boolean") {
      return result as ApiResponse<T>;
    }

    // 兼容旧格式，转换为新格式
    if (response.ok) {
      return {
        success: true,
        message: "操作成功",
        data: result,
        timestamp: new Date().toISOString()
      };
    } else {
      return {
        success: false,
        message: result.message || result.error || "操作失败",
        errorCode: result.errorCode || "UNKNOWN_ERROR",
        timestamp: new Date().toISOString()
      };
    }
  } catch {
    // JSON解析失败或其他错误
    return {
      success: false,
      message: "响应解析失败",
      errorCode: "PARSE_ERROR",
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 处理分页API响应
 * @param response fetch响应对象
 * @returns 解析后的分页响应数据
 */
export async function handlePageApiResponse<T = any>(
  response: Response
): Promise<ApiResponse<PageResponse<T>>> {
  const result = await handleApiResponse<PageResponse<T>>(response);

  // 如果是旧格式的分页响应，进行转换
  if (
    result.success &&
    result.data &&
    !("list" in result.data) &&
    Array.isArray((result.data as any).data)
  ) {
    const oldData = result.data as any;
    result.data = {
      list: oldData.data || oldData,
      total: oldData.total || 0,
      currentPage: oldData.currentPage || 1,
      pageSize: oldData.pageSize || 10,
      totalPages: Math.ceil((oldData.total || 0) / (oldData.pageSize || 10)),
      hasNext: false,
      hasPrevious: false,
      isFirst: true,
      isLast: true
    };
  }

  return result;
}

/**
 * 发送API请求的通用方法
 * @param url 请求URL
 * @param options fetch选项
 * @returns API响应数据
 */
export async function apiRequest<T = any>(
  url: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/json",
        ...options.headers
      },
      ...options
    });

    return await handleApiResponse<T>(response);
  } catch (error) {
    console.error("API请求失败:", error);
    return {
      success: false,
      message: "网络请求失败",
      errorCode: "NETWORK_ERROR",
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 发送分页API请求的通用方法
 * @param url 请求URL
 * @param options fetch选项
 * @returns 分页API响应数据
 */
export async function apiPageRequest<T = any>(
  url: string,
  options: RequestInit = {}
): Promise<ApiResponse<PageResponse<T>>> {
  try {
    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/json",
        ...options.headers
      },
      ...options
    });

    return await handlePageApiResponse<T>(response);
  } catch (error) {
    console.error("API请求失败:", error);
    return {
      success: false,
      message: "网络请求失败",
      errorCode: "NETWORK_ERROR",
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 构建查询参数字符串
 * @param params 参数对象
 * @returns 查询参数字符串
 */
export function buildQueryString(params: Record<string, any>): string {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== "") {
      if (Array.isArray(value)) {
        value.forEach(item => searchParams.append(key, String(item)));
      } else {
        searchParams.append(key, String(value));
      }
    }
  });

  const queryString = searchParams.toString();
  return queryString ? `?${queryString}` : "";
}

/**
 * 错误代码映射
 */
export const ERROR_CODE_MAP: Record<string, string> = {
  TASK_NOT_FOUND: "任务不存在",
  INVALID_PARAMETER: "参数错误",
  VALIDATION_FAILED: "数据验证失败",
  DATA_NOT_FOUND: "数据不存在",
  DUPLICATE_DATA: "数据已存在",
  NETWORK_ERROR: "网络连接失败",
  PARSE_ERROR: "数据解析失败",
  UNKNOWN_ERROR: "未知错误"
};

/**
 * 获取友好的错误消息
 * @param errorCode 错误代码
 * @param defaultMessage 默认消息
 * @returns 友好的错误消息
 */
export function getFriendlyErrorMessage(
  errorCode?: string,
  defaultMessage?: string
): string {
  if (errorCode && ERROR_CODE_MAP[errorCode]) {
    return ERROR_CODE_MAP[errorCode];
  }
  return defaultMessage || "操作失败";
}
