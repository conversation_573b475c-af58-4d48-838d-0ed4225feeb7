/**
 * 页面可见性管理工具
 * 用于智能管理状态轮询和资源使用，根据页面可见性调整行为
 */

export interface VisibilityConfig {
  /** 页面可见时的轮询间隔（毫秒） */
  visibleInterval: number;
  /** 页面隐藏时的轮询间隔（毫秒） */
  hiddenInterval: number;
  /** 页面隐藏多久后停止轮询（毫秒），0表示不停止 */
  stopAfterHidden: number;
  /** 是否在页面隐藏时立即停止轮询 */
  stopImmediately: boolean;
}

export interface PollingManager {
  /** 开始轮询 */
  start: () => void;
  /** 停止轮询 */
  stop: () => void;
  /** 是否正在轮询 */
  isRunning: () => boolean;
  /** 更新配置 */
  updateConfig: (config: Partial<VisibilityConfig>) => void;
  /** 销毁管理器 */
  destroy: () => void;
}

class PageVisibilityManager {
  private listeners: Set<(visible: boolean) => void> = new Set();
  private isVisible: boolean = !document.hidden;
  private initialized: boolean = false;

  constructor() {
    this.init();
  }

  private init() {
    if (this.initialized) return;

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
    
    // 监听页面卸载事件
    window.addEventListener('beforeunload', this.handleBeforeUnload);
    window.addEventListener('unload', this.handleUnload);
    
    // 监听页面焦点变化（作为可见性的补充）
    window.addEventListener('focus', this.handleFocus);
    window.addEventListener('blur', this.handleBlur);

    this.initialized = true;
    console.log('[PageVisibility] 页面可见性管理器已初始化');
  }

  private handleVisibilityChange = () => {
    const wasVisible = this.isVisible;
    this.isVisible = !document.hidden;
    
    if (wasVisible !== this.isVisible) {
      console.log(`[PageVisibility] 页面可见性变化: ${this.isVisible ? '可见' : '隐藏'}`);
      this.notifyListeners(this.isVisible);
    }
  };

  private handleBeforeUnload = () => {
    console.log('[PageVisibility] 页面即将卸载');
    this.notifyListeners(false);
  };

  private handleUnload = () => {
    console.log('[PageVisibility] 页面已卸载');
    this.cleanup();
  };

  private handleFocus = () => {
    if (!this.isVisible) {
      console.log('[PageVisibility] 页面获得焦点（补充可见性检测）');
      this.isVisible = true;
      this.notifyListeners(true);
    }
  };

  private handleBlur = () => {
    // 注意：blur事件不一定意味着页面不可见，所以这里只是记录日志
    console.log('[PageVisibility] 页面失去焦点');
  };

  private notifyListeners(visible: boolean) {
    this.listeners.forEach(listener => {
      try {
        listener(visible);
      } catch (error) {
        console.error('[PageVisibility] 监听器执行出错:', error);
      }
    });
  }

  /**
   * 添加可见性变化监听器
   */
  public addListener(listener: (visible: boolean) => void): () => void {
    this.listeners.add(listener);
    
    // 立即通知当前状态
    listener(this.isVisible);
    
    // 返回取消监听的函数
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * 获取当前页面是否可见
   */
  public getVisibility(): boolean {
    return this.isVisible;
  }

  /**
   * 创建智能轮询管理器
   */
  public createPollingManager(
    pollingFunction: () => void | Promise<void>,
    config: VisibilityConfig
  ): PollingManager {
    let timer: NodeJS.Timeout | null = null;
    let isRunning = false;
    let currentConfig = { ...config };
    let hiddenStartTime: number | null = null;
    let removeVisibilityListener: (() => void) | null = null;

    const executePolling = async () => {
      if (!isRunning) return;

      try {
        await pollingFunction();
      } catch (error) {
        console.error('[PollingManager] 轮询函数执行出错:', error);
      }

      // 安排下次轮询
      scheduleNext();
    };

    const scheduleNext = () => {
      if (!isRunning) return;

      const isVisible = this.getVisibility();
      let interval: number;

      if (isVisible) {
        interval = currentConfig.visibleInterval;
        hiddenStartTime = null;
      } else {
        // 页面隐藏时的处理
        if (hiddenStartTime === null) {
          hiddenStartTime = Date.now();
        }

        // 检查是否应该停止轮询
        if (currentConfig.stopImmediately) {
          console.log('[PollingManager] 页面隐藏，立即停止轮询');
          stop();
          return;
        }

        if (currentConfig.stopAfterHidden > 0) {
          const hiddenDuration = Date.now() - hiddenStartTime;
          if (hiddenDuration >= currentConfig.stopAfterHidden) {
            console.log(`[PollingManager] 页面隐藏超过${currentConfig.stopAfterHidden}ms，停止轮询`);
            stop();
            return;
          }
        }

        interval = currentConfig.hiddenInterval;
      }

      timer = setTimeout(executePolling, interval);
    };

    const start = () => {
      if (isRunning) return;
      
      isRunning = true;
      console.log('[PollingManager] 开始智能轮询');
      
      // 监听页面可见性变化
      removeVisibilityListener = this.addListener((visible) => {
        if (visible && hiddenStartTime !== null) {
          console.log('[PollingManager] 页面重新可见，恢复正常轮询频率');
          hiddenStartTime = null;
          
          // 如果当前没有定时器，立即开始轮询
          if (!timer && isRunning) {
            executePolling();
          }
        }
      });
      
      // 立即执行一次
      executePolling();
    };

    const stop = () => {
      if (!isRunning) return;
      
      isRunning = false;
      console.log('[PollingManager] 停止轮询');
      
      if (timer) {
        clearTimeout(timer);
        timer = null;
      }
      
      if (removeVisibilityListener) {
        removeVisibilityListener();
        removeVisibilityListener = null;
      }
      
      hiddenStartTime = null;
    };

    const updateConfig = (newConfig: Partial<VisibilityConfig>) => {
      currentConfig = { ...currentConfig, ...newConfig };
      console.log('[PollingManager] 配置已更新:', currentConfig);
    };

    const destroy = () => {
      stop();
      console.log('[PollingManager] 轮询管理器已销毁');
    };

    return {
      start,
      stop,
      isRunning: () => isRunning,
      updateConfig,
      destroy
    };
  }

  /**
   * 清理资源
   */
  public cleanup() {
    if (!this.initialized) return;

    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    window.removeEventListener('beforeunload', this.handleBeforeUnload);
    window.removeEventListener('unload', this.handleUnload);
    window.removeEventListener('focus', this.handleFocus);
    window.removeEventListener('blur', this.handleBlur);

    this.listeners.clear();
    this.initialized = false;
    console.log('[PageVisibility] 页面可见性管理器已清理');
  }
}

// 创建全局单例
const pageVisibilityManager = new PageVisibilityManager();

export default pageVisibilityManager;

/**
 * 创建智能轮询管理器的便捷函数
 */
export function createSmartPolling(
  pollingFunction: () => void | Promise<void>,
  config: Partial<VisibilityConfig> = {}
): PollingManager {
  const defaultConfig: VisibilityConfig = {
    visibleInterval: 5000,      // 页面可见时5秒轮询一次
    hiddenInterval: 30000,      // 页面隐藏时30秒轮询一次
    stopAfterHidden: 300000,    // 页面隐藏5分钟后停止轮询
    stopImmediately: false      // 不立即停止
  };

  const finalConfig = { ...defaultConfig, ...config };
  return pageVisibilityManager.createPollingManager(pollingFunction, finalConfig);
}

/**
 * 添加页面可见性监听器的便捷函数
 */
export function onVisibilityChange(listener: (visible: boolean) => void): () => void {
  return pageVisibilityManager.addListener(listener);
}

/**
 * 获取当前页面是否可见
 */
export function isPageVisible(): boolean {
  return pageVisibilityManager.getVisibility();
}
