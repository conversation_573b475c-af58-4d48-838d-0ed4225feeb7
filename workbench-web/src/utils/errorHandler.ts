/**
 * 错误处理工具
 * 统一处理前端错误，提供友好的用户提示
 */

import { ElMessage, ElNotification } from "element-plus";

export interface ErrorInfo {
  code?: string | number;
  message: string;
  details?: any;
}

/**
 * 处理API错误 - 增强版本，支持更详细的错误信息
 */
export const handleApiError = (error: any): void => {
  console.error("API错误:", error);

  let errorMessage = "请求失败，请稍后重试";
  let errorDetails = null;

  if (error.response) {
    // 服务器返回错误状态码
    const { status, data } = error.response;

    // 提取详细错误信息
    if (data && typeof data === 'object') {
      errorMessage = data.message || errorMessage;
      errorDetails = data.details || data.data;

      // 如果有错误类型信息，添加到消息中
      if (data.errorType) {
        console.warn(`错误类型: ${data.errorType}`, errorDetails);
      }
    }

    switch (status) {
      case 400:
        errorMessage = data?.message || "请求参数错误";
        break;
      case 401:
        errorMessage = "未授权，请重新登录";
        break;
      case 403:
        errorMessage = "权限不足";
        break;
      case 404:
        errorMessage = "请求的资源不存在";
        break;
      case 500:
        errorMessage = data?.message || "服务器内部错误";
        // 对于500错误，显示更详细的错误信息
        if (data?.errorType) {
          errorMessage += ` (${data.errorType})`;
        }
        break;
      case 503:
        errorMessage = "服务暂时不可用，请稍后重试";
        break;
      default:
        errorMessage = data?.message || `请求失败 (${status})`;
    }
  } else if (error.request) {
    // 网络错误
    errorMessage = "网络连接失败，请检查网络设置";
  } else {
    // 其他错误
    errorMessage = error.message || "未知错误";
  }

  // 根据错误严重程度选择不同的显示方式
  if (error.response?.status >= 500) {
    // 服务器错误使用通知形式，提供更多信息
    ElNotification({
      title: "服务器错误",
      message: errorMessage,
      type: "error",
      duration: 8000,
      showClose: true
    });

    // 如果有详细错误信息，在控制台输出
    if (errorDetails) {
      console.error("详细错误信息:", errorDetails);
    }
  } else {
    // 客户端错误使用消息形式
    ElMessage.error(errorMessage);
  }
};

/**
 * 处理业务逻辑错误
 */
export const handleBusinessError = (errorInfo: ErrorInfo): void => {
  console.warn("业务错误:", errorInfo);

  ElNotification({
    title: "操作失败",
    message: errorInfo.message,
    type: "warning",
    duration: 5000
  });
};

/**
 * 处理系统错误
 */
export const handleSystemError = (error: Error): void => {
  console.error("系统错误:", error);

  ElNotification({
    title: "系统错误",
    message: "系统出现异常，请联系管理员",
    type: "error",
    duration: 0 // 不自动关闭
  });
};

/**
 * 显示成功消息
 */
export const showSuccess = (message: string): void => {
  ElMessage.success(message);
};

/**
 * 显示警告消息
 */
export const showWarning = (message: string): void => {
  ElMessage.warning(message);
};

/**
 * 显示信息消息
 */
export const showInfo = (message: string): void => {
  ElMessage.info(message);
};
