import { Client, IMessage } from '@stomp/stompjs'
import SockJS from 'sockjs-client'
import { onVisibilityChange, isPageVisible } from './pageVisibility'
import { ElMessage } from 'element-plus'

export interface TaskStatusMessage {
  type: 'STATUS_CHANGE' | 'PROGRESS_UPDATE' | 'LOG_MESSAGE' | 'SYSTEM_NOTIFICATION'
  taskId?: number
  taskName?: string
  oldStatus?: string
  newStatus?: string
  progress?: TaskProgress
  level?: string
  message?: string
  timestamp: string
}

export interface TaskProgress {
  taskId: number
  currentStep: number
  totalSteps: number
  stepName: string
  description: string
  percentage: number
  timestamp: string
}

export interface SystemNotification {
  type: string
  title: string
  content: string
  level: 'info' | 'warning' | 'error'
  timestamp: string
}

export interface ResourceMonitor {
  memoryUsageRatio: number
  browserCount: number
  status: string
  timestamp: string
}

class WebSocketService {
  private client: Client | null = null
  private connected = false
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 5000
  private subscriptions = new Map<string, any>()
  private visibilityUnsubscribe: (() => void) | null = null
  private isPageVisible = true

  // 事件监听器
  private listeners = {
    taskStatus: new Set<(message: TaskStatusMessage) => void>(),
    taskProgress: new Set<(progress: TaskProgress) => void>(),
    taskLogs: new Set<(log: any) => void>(),
    systemNotifications: new Set<(notification: SystemNotification) => void>(),
    resourceMonitor: new Set<(resource: ResourceMonitor) => void>(),
    connectionStatus: new Set<(connected: boolean) => void>()
  }

  constructor() {
    this.initializeClient()
    this.setupVisibilityListener()
  }

  private setupVisibilityListener() {
    this.isPageVisible = isPageVisible()

    this.visibilityUnsubscribe = onVisibilityChange((visible) => {
      const wasVisible = this.isPageVisible
      this.isPageVisible = visible

      console.log(`[WebSocket] 页面可见性变化: ${visible ? '可见' : '隐藏'}`)

      if (!wasVisible && visible) {
        // 页面从隐藏变为可见，尝试重新连接
        console.log('[WebSocket] 页面重新可见，检查连接状态')
        if (!this.connected) {
          this.connect()
        }
      } else if (wasVisible && !visible) {
        // 页面从可见变为隐藏，可以考虑降低心跳频率或暂停某些订阅
        console.log('[WebSocket] 页面隐藏，保持连接但降低活跃度')
        // 这里可以实现更精细的控制，比如暂停某些非关键订阅
      }
    })
  }

  private initializeClient() {
    this.client = new Client({
      webSocketFactory: () => new SockJS('/api/ws'),
      connectHeaders: {},
      debug: (str) => {
        console.log('[WebSocket Debug]', str)
      },
      reconnectDelay: this.reconnectInterval,
      heartbeatIncoming: 4000,
      heartbeatOutgoing: 4000,
      onConnect: () => {
        console.log('[WebSocket] 连接成功')
        this.connected = true
        this.reconnectAttempts = 0
        this.notifyConnectionStatus(true)
        this.setupSubscriptions()
      },
      onDisconnect: () => {
        console.log('[WebSocket] 连接断开')
        this.connected = false
        this.notifyConnectionStatus(false)
      },
      onStompError: (frame) => {
        console.error('[WebSocket] STOMP错误:', frame.headers['message'])
        console.error('[WebSocket] 错误详情:', frame.body)
        this.handleConnectionError()
      },
      onWebSocketError: (error) => {
        console.error('[WebSocket] WebSocket错误:', error)
        this.handleConnectionError()
      }
    })
  }

  private handleConnectionError() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`[WebSocket] 尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      setTimeout(() => {
        this.connect()
      }, this.reconnectInterval * this.reconnectAttempts)
    } else {
      console.error('[WebSocket] 重连失败，已达到最大重试次数')
      ElMessage.error('WebSocket连接失败，实时功能不可用')
    }
  }

  private setupSubscriptions() {
    if (!this.client || !this.connected) return

    // 订阅任务状态变更
    this.subscriptions.set('taskStatus',
      this.client.subscribe('/topic/task/status', (message: IMessage) => {
        const data: TaskStatusMessage = JSON.parse(message.body)
        this.notifyTaskStatus(data)
      })
    )

    // 订阅任务进度更新
    this.subscriptions.set('taskProgress',
      this.client.subscribe('/topic/task/progress', (message: IMessage) => {
        const data: TaskStatusMessage = JSON.parse(message.body)
        if (data.progress) {
          this.notifyTaskProgress(data.progress)
        }
      })
    )

    // 订阅系统通知
    this.subscriptions.set('systemNotifications',
      this.client.subscribe('/topic/system/notifications', (message: IMessage) => {
        const data: SystemNotification = JSON.parse(message.body)
        this.notifySystemNotification(data)
      })
    )

    // 订阅资源监控
    this.subscriptions.set('resourceMonitor',
      this.client.subscribe('/topic/system/resources', (message: IMessage) => {
        const data: ResourceMonitor = JSON.parse(message.body)
        this.notifyResourceMonitor(data)
      })
    )
  }

  public connect() {
    if (this.client && !this.connected) {
      this.client.activate()
    }
  }

  public disconnect() {
    if (this.client && this.connected) {
      this.client.deactivate()
    }

    // 清理可见性监听器
    if (this.visibilityUnsubscribe) {
      this.visibilityUnsubscribe()
      this.visibilityUnsubscribe = null
    }
  }

  public subscribeToTask(taskId: number) {
    if (!this.client || !this.connected) return

    const statusKey = `task-${taskId}-status`
    const progressKey = `task-${taskId}-progress`
    const logsKey = `task-${taskId}-logs`

    // 订阅特定任务的状态
    if (!this.subscriptions.has(statusKey)) {
      this.subscriptions.set(statusKey,
        this.client.subscribe(`/topic/task/${taskId}/status`, (message: IMessage) => {
          const data: TaskStatusMessage = JSON.parse(message.body)
          this.notifyTaskStatus(data)
        })
      )
    }

    // 订阅特定任务的进度
    if (!this.subscriptions.has(progressKey)) {
      this.subscriptions.set(progressKey,
        this.client.subscribe(`/topic/task/${taskId}/progress`, (message: IMessage) => {
          const data: TaskStatusMessage = JSON.parse(message.body)
          if (data.progress) {
            this.notifyTaskProgress(data.progress)
          }
        })
      )
    }

    // 订阅特定任务的日志
    if (!this.subscriptions.has(logsKey)) {
      this.subscriptions.set(logsKey,
        this.client.subscribe(`/topic/task/${taskId}/logs`, (message: IMessage) => {
          const data = JSON.parse(message.body)
          this.notifyTaskLogs(data)
        })
      )
    }
  }

  public unsubscribeFromTask(taskId: number) {
    const keys = [`task-${taskId}-status`, `task-${taskId}-progress`, `task-${taskId}-logs`]
    keys.forEach(key => {
      const subscription = this.subscriptions.get(key)
      if (subscription) {
        subscription.unsubscribe()
        this.subscriptions.delete(key)
      }
    })
  }

  // 事件监听器管理
  public onTaskStatus(callback: (message: TaskStatusMessage) => void) {
    this.listeners.taskStatus.add(callback)
    return () => this.listeners.taskStatus.delete(callback)
  }

  public onTaskProgress(callback: (progress: TaskProgress) => void) {
    this.listeners.taskProgress.add(callback)
    return () => this.listeners.taskProgress.delete(callback)
  }

  public onTaskLogs(callback: (log: any) => void) {
    this.listeners.taskLogs.add(callback)
    return () => this.listeners.taskLogs.delete(callback)
  }

  public onSystemNotification(callback: (notification: SystemNotification) => void) {
    this.listeners.systemNotifications.add(callback)
    return () => this.listeners.systemNotifications.delete(callback)
  }

  public onResourceMonitor(callback: (resource: ResourceMonitor) => void) {
    this.listeners.resourceMonitor.add(callback)
    return () => this.listeners.resourceMonitor.delete(callback)
  }

  public onConnectionStatus(callback: (connected: boolean) => void) {
    this.listeners.connectionStatus.add(callback)
    return () => this.listeners.connectionStatus.delete(callback)
  }

  // 通知方法
  private notifyTaskStatus(message: TaskStatusMessage) {
    this.listeners.taskStatus.forEach(callback => callback(message))
  }

  private notifyTaskProgress(progress: TaskProgress) {
    this.listeners.taskProgress.forEach(callback => callback(progress))
  }

  private notifyTaskLogs(log: any) {
    this.listeners.taskLogs.forEach(callback => callback(log))
  }

  private notifySystemNotification(notification: SystemNotification) {
    this.listeners.systemNotifications.forEach(callback => callback(notification))
  }

  private notifyResourceMonitor(resource: ResourceMonitor) {
    this.listeners.resourceMonitor.forEach(callback => callback(resource))
  }

  private notifyConnectionStatus(connected: boolean) {
    this.listeners.connectionStatus.forEach(callback => callback(connected))
  }

  public isConnected(): boolean {
    return this.connected
  }
}

// 单例实例
export const webSocketService = new WebSocketService()

// 自动连接
webSocketService.connect()
