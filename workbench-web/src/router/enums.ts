// 完整版菜单比较多，将 rank 抽离出来，在此方便维护

const home = 0, // 平台规定只有 home 路由的 rank 才能为 0 ，所以后端在返回 rank 的时候需要从非 0 开始
  task = 1, // 任务列表
  ip = 2, // IP列表
  asset = 3, // 资产列表
  examples = 4, // 示例展示分组
  result = 5, // 保留原结果页面（隐藏）
  // 示例模块区间 (10-99) - 已隐藏，归集到examples分组
  table = 10,
  form = 11,
  list = 12,
  nested = 13,
  editor = 14,
  components = 15,
  able = 16,
  chatai = 17,
  vueflow = 18,
  ganttastic = 19,
  flowchart = 20,
  formdesign = 21,
  board = 22,
  ppt = 23,
  about = 24,
  codemirror = 25,
  markdown = 26,
  mind = 27,
  guide = 28,
  menuoverflow = 29,
  // 动态路由区间 (100-199)
  frame = 100,
  permission = 101,
  system = 102,
  monitor = 103,
  tabs = 104,
  // 其他模块
  error = 200;

export {
  home,
  task,
  ip,
  asset,
  result,
  examples,
  chatai,
  vueflow,
  ganttastic,
  components,
  able,
  table,
  form,
  list,
  error,
  frame,
  nested,
  permission,
  system,
  monitor,
  tabs,
  about,
  codemirror,
  markdown,
  editor,
  flowchart,
  formdesign,
  board,
  ppt,
  mind,
  guide,
  menuoverflow
};
