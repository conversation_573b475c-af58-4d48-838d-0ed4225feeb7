@font-face {
  font-family: "iconfont"; /* Project id 2208059 */
  src:
    url("iconfont.woff2?t=1671895108120") format("woff2"),
    url("iconfont.woff?t=1671895108120") format("woff"),
    url("iconfont.ttf?t=1671895108120") format("truetype");
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.pure-iconfont-tabs:before {
  content: "\e63e";
}

.pure-iconfont-logo:before {
  content: "\e620";
}

.pure-iconfont-new:before {
  content: "\e615";
}
