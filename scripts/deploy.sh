#!/bin/bash

# Workbench 项目一键部署脚本
# 使用 Nginx 反向代理方案

set -e

echo "🚀 开始部署 Workbench 项目..."

# 配置变量
DOMAIN=${DOMAIN:-"localhost"}
SYSTEM_HOST=${SYSTEM_HOST:-"**********"}
SYSTEM_PORT=${SYSTEM_PORT:-"38889"}
SCANNER_HOST=${SCANNER_HOST:-"**********"}
SCANNER_PORT=${SCANNER_PORT:-"38888"}

echo "📋 部署配置："
echo "  - 域名: $DOMAIN"
echo "  - System服务: $SYSTEM_HOST:$SYSTEM_PORT"
echo "  - Scanner服务: $SCANNER_HOST:$SCANNER_PORT"

# 1. 构建前端
echo "🔨 构建前端项目..."
cd workbench-web
npm run build
cd ..

# 2. 生成 Nginx 配置
echo "⚙️ 生成 Nginx 配置..."
cat > nginx.conf << EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    # 前端静态资源
    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files \$uri \$uri/ /index.html;
    }
    
    # API代理到System模块
    location /api/ {
        proxy_pass http://$SYSTEM_HOST:$SYSTEM_PORT/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}
EOF

# 3. 构建前端 Docker 镜像
echo "🐳 构建前端 Docker 镜像..."
cat > Dockerfile.web << EOF
FROM nginx:alpine
COPY workbench-web/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
EOF

docker build -f Dockerfile.web -t workbench-web:latest .

# 4. 构建后端镜像
echo "🐳 构建后端镜像..."
cd backend
mvn clean package -DskipTests

# System 模块
docker build -t workbench-system:latest ./workbench-system

# Scanner 模块  
docker build -t workbench-scanner:latest ./workbench-scanner

cd ..

# 5. 生成环境变量文件
echo "📝 生成环境变量文件..."
cat > .env << EOF
# 数据库配置
DB_PASSWORD=your_db_password

# RabbitMQ配置
RABBITMQ_PASSWORD=your_rabbitmq_password

# 服务配置
SYSTEM_HOST=$SYSTEM_HOST
SCANNER_HOST=$SCANNER_HOST
EOF

# 6. 启动服务
echo "🚀 启动所有服务..."
docker-compose -f docker-compose.production.yml up -d

# 7. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 8. 健康检查
echo "🔍 执行健康检查..."
if curl -f http://localhost/api/system/status > /dev/null 2>&1; then
    echo "✅ 系统启动成功！"
    echo "🌐 访问地址: http://$DOMAIN"
else
    echo "❌ 系统启动失败，请检查日志"
    docker-compose -f docker-compose.production.yml logs
    exit 1
fi

echo "🎉 部署完成！"
