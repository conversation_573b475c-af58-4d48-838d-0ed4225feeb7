# 项目上下文信息

- 项目结构已优化：删除了重复的依赖目录(frontend/, src/, logs/, node_modules/)、清理了构建产物和日志文件、移除了不必要的模板文件，并生成了详细的README.md文档
- Workbench项目搜索框定位问题：实际HTML结构中textarea在.search-input-wrapper-right下有额外的div容器，需要使用更灵活的选择器策略
- Workbench前端数据列表优化：已创建optimized-table.vue组件，正在集成到现有页面中，优化了字段显示、操作按钮布局和响应式设计
- IP管理功能改进：1. IP列表需要添加备注功能，支持内联编辑 2. IP详情页面需要添加跳转到资产管理的功能 3. IpAsset实体已包含notes字段，无需数据库变更
- 项目架构：Spring Boot多模块项目，包含workbench-system（主控API服务，端口38889）、workbench-scanner（采集服务，端口38888）、workbench-common（公共模块）。使用RabbitMQ消息队列、MySQL数据库、Playwright自动化。任务执行流程：TaskPublisher -> RabbitMQ -> TaskConsumer -> TaskService。已启用@EnableScheduling，有ResourceMonitorScheduler定时任务示例。
