-- 手动添加定时任务字段到tasks表
-- 兼容MySQL 5.7+版本的脚本

-- 检查当前tasks表结构
DESCRIBE tasks;

-- 添加定时任务字段（逐个添加，忽略已存在的错误）
-- 如果字段已存在，会报错但不影响执行

-- 添加 schedule_enabled 字段
ALTER TABLE tasks ADD COLUMN schedule_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用定时执行';

-- 添加 schedule_time 字段
ALTER TABLE tasks ADD COLUMN schedule_time VARCHAR(20) COMMENT '定时执行时间(HH:mm格式)';

-- 添加 schedule_cron 字段
ALTER TABLE tasks ADD COLUMN schedule_cron VARCHAR(50) COMMENT 'Cron表达式(可选)';

-- 添加 time_window_strategy 字段
ALTER TABLE tasks ADD COLUMN time_window_strategy VARCHAR(20) DEFAULT 'SMART' COMMENT '时间窗口策略';

-- 添加 last_scheduled_at 字段
ALTER TABLE tasks ADD COLUMN last_scheduled_at TIMESTAMP NULL COMMENT '上次定时执行时间';

-- 添加 time_window_config 字段
ALTER TABLE tasks ADD COLUMN time_window_config TEXT COMMENT '时间窗口配置JSON';

-- 添加 schedule_status 字段
ALTER TABLE tasks ADD COLUMN schedule_status VARCHAR(20) DEFAULT 'INACTIVE' COMMENT '定时任务状态';

-- 添加索引（如果索引已存在会报错，但不影响执行）
CREATE INDEX idx_tasks_schedule_enabled ON tasks(schedule_enabled);
CREATE INDEX idx_tasks_schedule_time ON tasks(schedule_time);
CREATE INDEX idx_tasks_last_scheduled_at ON tasks(last_scheduled_at);
CREATE INDEX idx_tasks_schedule_status ON tasks(schedule_status);

-- 更新现有记录的默认值
UPDATE tasks SET
    schedule_enabled = FALSE,
    time_window_strategy = 'SMART',
    schedule_status = 'INACTIVE'
WHERE schedule_enabled IS NULL OR time_window_strategy IS NULL OR schedule_status IS NULL;

-- 验证字段添加成功
DESCRIBE tasks;

SELECT 'Fields added successfully!' as message;
