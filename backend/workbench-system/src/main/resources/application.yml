server:
  port: 38889
# 模块间通信配置
workbench:
  services:
    # Scanner模块配置 - System需要调用Scanner的API
    scanner:
      host: **********
      port: 38888
      base-url: http://**********:38888
  # 定时任务调度器配置
  scheduler:
    enabled: true
    check-interval: 60000  # 检查间隔(毫秒)，默认60秒
    max-concurrent-tasks: 5  # 最大并发任务数
spring:
  application:
    name: workbench-system
  
  # 数据库配置
  datasource:
    url: ${DB_URL:**************************************************************************************************************************************}
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:Z3ride@0322}
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  # Jackson配置 - 支持Java 8时间类型
  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
  
  # RabbitMQ配置
  rabbitmq:
    host: ${RABBITMQ_HOST:**********}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:admin}
    password: ${RABBITMQ_PASSWORD:Z3rd0@0322!}
    listener:
      simple:
        concurrency: 1
        max-concurrency: 1
        prefetch: 1
        retry:
          enabled: true
          initial-interval: 5000
          max-attempts: 3
          multiplier: 1.5

# 日志配置
logging:
  level:
    root: INFO
    com.z3rd0.workbench: DEBUG
    org.hibernate.SQL: DEBUG
  file:
    name: logs/workbench-system.log

# 系统配置
system:
  task:
    enabled: true

# 天眼查API配置
tianyancha:
  # API Token（请替换为实际的Token）
  token: ""
  # 天眼查ID（请替换为实际的ID）
  tycId: ""
  # API版本
  version: "TYC-Web"

#仅用于供应链公司排除
companyName: 国家电网,国网,供电公司,保险,传媒,奇安信,阿里云,腾讯云,建設股份,發有限公司,升级,租赁,投标,谈判,化工有,2022,电缆,集团,電纜,环境,中国,林业,安装,勘测,机电,
  变压器,投资,测评,装饰,线缆,通讯,土地,检测,规划,钢管
#用于搜索结果排除
searchIcpName: 大学,学院,学校,幼儿园,电动汽车服务,第三方信用服务机构,妇幼保健院,商场,小区,医院,事务所,街道办事处,经营部,改造升级,修理厂,印刷,彩印,厂,气象台,实验室,
  回收,党校,工作室,勘测局,测绘院,客运有限公司,服务部,传播中心,返厂修理,铁塔有限公司,城市规划,广告,人民政府,二期A区,服装有限公司,罗蒙,九牧王,用品,商贸,贸易,花卉,物流,药房,品牌运营,
  江苏虎豹,海澜之家,房地产,平高,家具,质量,紫光软件系统,中国计量,许继,专利代理,劳动保障,中华人民共和国,消防,建设,车辆,苏宁易购,中国外运,出版社,计量,建筑,商务,学会,工会,互感器,
  中铁,企业管理,文化,房地产,物业,刻字社,制作社,设计社,报社,安保,园林,图文,公路,汽车,家政,事务,清洗,公共资源,代理,护理,疾病,泛微,帆软,科大讯飞,人力资源,食品,照明,委员,厨业,浙江大华,服装,
  制衣,服饰,纺织,消毒,置业,维修,房产,医学,医疗,搬家,混凝土,水泥,会展,企业咨询,酒店,资质,园艺,轮胎,航空,合作社,制品,环保,再生,绿化,装修,建材,餐饮,超市,茶叶,咨询服务,保洁,电器,协会,
  开关,中铁,空调,中共,管理局,粮食,批发,药业,中国电信,中国移动,中国联通,电梯,水务,仪器,制造,景观,市政,水利,苏州国网电子科技有限公司,银行,保安,机械,起重
