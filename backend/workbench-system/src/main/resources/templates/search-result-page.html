<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>搜索结果分页查询</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <style>
        body { background: #f6f8fa; margin: 0; padding: 0; }
        .container { max-width: 1200px; margin: 40px auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #eee; padding: 32px; }
        .search-bar { margin-bottom: 24px; }
    </style>
</head>
<body>
<div id="app">
    <div class="container">
        <h2>搜索结果分页查询</h2>
        <div class="search-bar">
            <el-input v-model="ip" placeholder="输入IP搜索" style="width: 240px; margin-right: 12px;" clearable></el-input>
            <el-button type="primary" @click="fetchData">搜索</el-button>
        </div>
        <el-table :data="tableData" border style="width: 100%">
            <el-table-column prop="id" label="ID" width="60"/>
            <el-table-column prop="ip" label="IP" width="140"/>
            <el-table-column prop="port" label="端口" width="80"/>
            <el-table-column prop="domain" label="域名" width="180"/>
            <el-table-column prop="title" label="标题" width="200"/>
            <el-table-column prop="taskName" label="任务名称" width="120"/>
            <el-table-column prop="locationIsp" label="ISP" width="120"/>
            <el-table-column prop="locationScene" label="场景" width="120"/>
            <el-table-column prop="time" label="采集时间" width="160"/>
            <el-table-column prop="note" label="备注" width="120"/>
        </el-table>
        <div style="margin-top: 20px; text-align: right;">
            <el-pagination
                background
                layout="prev, pager, next, jumper, ->, total"
                :total="total"
                :page-size="size"
                :current-page="page+1"
                @current-change="handlePageChange"
            />
        </div>
    </div>
</div>
<script>
const { createApp, ref } = Vue;
createApp({
    setup() {
        const tableData = ref([]);
        const total = ref(0);
        const page = ref(0);
        const size = ref(10);
        const ip = ref("");
        const fetchData = async () => {
            const params = new URLSearchParams({ page: page.value, size: size.value, ip: ip.value });
            const resp = await fetch(`/api/search/results?${params}`);
            const data = await resp.json();
            tableData.value = data.data;
            total.value = data.total;
            page.value = data.page;
            size.value = data.size;
        };
        const handlePageChange = (val) => {
            page.value = val - 1;
            fetchData();
        };
        fetchData();
        return { tableData, total, page, size, ip, fetchData, handlePageChange };
    }
}).use(ElementPlus).mount('#app');
</script>
</body>
</html> 