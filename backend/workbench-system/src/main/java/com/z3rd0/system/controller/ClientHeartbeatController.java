package com.z3rd0.system.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 客户端心跳控制器
 * 用于跟踪前端页面的活跃状态，优化资源使用
 */
@RestController
@RequestMapping("/api/client")
@Slf4j
public class ClientHeartbeatController {

    // 存储客户端心跳信息
    private static final Map<String, ClientHeartbeat> clientHeartbeats = new ConcurrentHashMap<>();
    
    // 心跳超时时间（毫秒）
    private static final long HEARTBEAT_TIMEOUT = 300000; // 5分钟

    /**
     * 客户端心跳信息
     */
    public static class ClientHeartbeat {
        private String clientId;
        private String pageUrl;
        private boolean isVisible;
        private LocalDateTime lastHeartbeat;
        private String userAgent;

        public ClientHeartbeat(String clientId, String pageUrl, boolean isVisible, String userAgent) {
            this.clientId = clientId;
            this.pageUrl = pageUrl;
            this.isVisible = isVisible;
            this.userAgent = userAgent;
            this.lastHeartbeat = LocalDateTime.now();
        }

        // Getters and Setters
        public String getClientId() { return clientId; }
        public void setClientId(String clientId) { this.clientId = clientId; }
        
        public String getPageUrl() { return pageUrl; }
        public void setPageUrl(String pageUrl) { this.pageUrl = pageUrl; }
        
        public boolean isVisible() { return isVisible; }
        public void setVisible(boolean visible) { isVisible = visible; }
        
        public LocalDateTime getLastHeartbeat() { return lastHeartbeat; }
        public void setLastHeartbeat(LocalDateTime lastHeartbeat) { this.lastHeartbeat = lastHeartbeat; }
        
        public String getUserAgent() { return userAgent; }
        public void setUserAgent(String userAgent) { this.userAgent = userAgent; }

        public void updateHeartbeat() {
            this.lastHeartbeat = LocalDateTime.now();
        }
    }

    /**
     * 接收客户端心跳
     */
    @PostMapping("/heartbeat")
    public ResponseEntity<Map<String, Object>> receiveHeartbeat(
            @RequestParam String clientId,
            @RequestParam(required = false) String pageUrl,
            @RequestParam(defaultValue = "true") boolean isVisible,
            @RequestHeader(value = "User-Agent", required = false) String userAgent) {
        
        try {
            ClientHeartbeat heartbeat = clientHeartbeats.get(clientId);
            
            if (heartbeat == null) {
                // 新客户端
                heartbeat = new ClientHeartbeat(clientId, pageUrl, isVisible, userAgent);
                clientHeartbeats.put(clientId, heartbeat);
                log.info("新客户端心跳注册: clientId={}, pageUrl={}, isVisible={}", 
                        clientId, pageUrl, isVisible);
            } else {
                // 更新现有客户端
                boolean visibilityChanged = heartbeat.isVisible() != isVisible;
                heartbeat.setPageUrl(pageUrl);
                heartbeat.setVisible(isVisible);
                heartbeat.updateHeartbeat();
                
                if (visibilityChanged) {
                    log.info("客户端可见性变化: clientId={}, isVisible={}", clientId, isVisible);
                }
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "心跳接收成功");
            response.put("serverTime", LocalDateTime.now());
            response.put("activeClients", getActiveClientsCount());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("处理客户端心跳失败: clientId={}, error={}", clientId, e.getMessage(), e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "心跳处理失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 获取活跃客户端统计
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getClientStats() {
        try {
            // 清理过期的心跳
            cleanupExpiredHeartbeats();
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalClients", clientHeartbeats.size());
            stats.put("activeClients", getActiveClientsCount());
            stats.put("visibleClients", getVisibleClientsCount());
            stats.put("hiddenClients", getHiddenClientsCount());
            
            // 按页面URL分组统计
            Map<String, Integer> pageStats = new HashMap<>();
            clientHeartbeats.values().forEach(heartbeat -> {
                String pageUrl = heartbeat.getPageUrl();
                if (pageUrl != null) {
                    pageStats.put(pageUrl, pageStats.getOrDefault(pageUrl, 0) + 1);
                }
            });
            stats.put("pageStats", pageStats);
            
            return ResponseEntity.ok(stats);
            
        } catch (Exception e) {
            log.error("获取客户端统计失败: {}", e.getMessage(), e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取统计失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 客户端断开连接
     */
    @DeleteMapping("/disconnect/{clientId}")
    public ResponseEntity<Map<String, Object>> clientDisconnect(@PathVariable String clientId) {
        try {
            ClientHeartbeat removed = clientHeartbeats.remove(clientId);
            
            Map<String, Object> response = new HashMap<>();
            if (removed != null) {
                log.info("客户端断开连接: clientId={}", clientId);
                response.put("success", true);
                response.put("message", "客户端断开连接成功");
            } else {
                response.put("success", false);
                response.put("message", "客户端不存在");
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("处理客户端断开连接失败: clientId={}, error={}", clientId, e.getMessage(), e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "断开连接处理失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 清理过期的心跳记录
     */
    private void cleanupExpiredHeartbeats() {
        LocalDateTime cutoffTime = LocalDateTime.now().minusNanos(HEARTBEAT_TIMEOUT * 1_000_000);
        
        clientHeartbeats.entrySet().removeIf(entry -> {
            boolean expired = entry.getValue().getLastHeartbeat().isBefore(cutoffTime);
            if (expired) {
                log.info("清理过期客户端心跳: clientId={}", entry.getKey());
            }
            return expired;
        });
    }

    /**
     * 获取活跃客户端数量
     */
    private int getActiveClientsCount() {
        cleanupExpiredHeartbeats();
        return clientHeartbeats.size();
    }

    /**
     * 获取可见客户端数量
     */
    private int getVisibleClientsCount() {
        return (int) clientHeartbeats.values().stream()
                .filter(ClientHeartbeat::isVisible)
                .count();
    }

    /**
     * 获取隐藏客户端数量
     */
    private int getHiddenClientsCount() {
        return (int) clientHeartbeats.values().stream()
                .filter(heartbeat -> !heartbeat.isVisible())
                .count();
    }

    /**
     * 检查是否有活跃的可见客户端
     */
    public static boolean hasActiveVisibleClients() {
        LocalDateTime cutoffTime = LocalDateTime.now().minusNanos(HEARTBEAT_TIMEOUT * 1_000_000);
        
        return clientHeartbeats.values().stream()
                .anyMatch(heartbeat -> 
                    heartbeat.isVisible() && 
                    heartbeat.getLastHeartbeat().isAfter(cutoffTime)
                );
    }

    /**
     * 获取活跃客户端数量（静态方法，供其他服务调用）
     */
    public static int getActiveClientCount() {
        LocalDateTime cutoffTime = LocalDateTime.now().minusNanos(HEARTBEAT_TIMEOUT * 1_000_000);
        
        return (int) clientHeartbeats.values().stream()
                .filter(heartbeat -> heartbeat.getLastHeartbeat().isAfter(cutoffTime))
                .count();
    }
}
