package com.z3rd0.system.service;

import com.z3rd0.common.model.IpAsset;
import com.z3rd0.common.model.SearchResult;
import com.z3rd0.system.repository.SearchResultRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * IP资产同步服务
 * 负责将搜索结果中的IP信息同步到IP资产表
 */
@Service
public class IpAssetSyncService {
    
    private static final Logger logger = LoggerFactory.getLogger(IpAssetSyncService.class);
    
    @Autowired
    private IpAssetService ipAssetService;
    
    @Autowired
    private SearchResultRepository searchResultRepository;
    
    /**
     * 异步同步单个搜索结果的IP资产
     */
    @Async
    @Transactional
    public void syncIpAssetAsync(SearchResult searchResult) {
        try {
            syncIpAsset(searchResult);
        } catch (Exception e) {
            logger.error("异步同步IP资产失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 同步单个搜索结果的IP资产
     */
    @Transactional
    public void syncIpAsset(SearchResult searchResult) {
        if (searchResult == null || searchResult.getIp() == null || searchResult.getIp().trim().isEmpty()) {
            return;
        }
        
        try {
            String ipAddress = searchResult.getIp().trim();
            
            // 创建或更新IP资产
            IpAsset ipAsset = ipAssetService.createOrUpdateIpAsset(ipAddress, searchResult);
            
            if (ipAsset != null && searchResult.getIpAssetId() == null) {
                // 更新搜索结果的IP资产关联
                searchResult.setIpAssetId(ipAsset.getId());
                searchResultRepository.save(searchResult);
                
                logger.debug("已关联搜索结果到IP资产: {} -> {}", searchResult.getId(), ipAsset.getId());
            }
            
        } catch (Exception e) {
            logger.error("同步IP资产失败 - 搜索结果ID: {}, IP: {}, 错误: {}", 
                        searchResult.getId(), searchResult.getIp(), e.getMessage(), e);
        }
    }
    
    /**
     * 批量同步IP资产
     */
    @Transactional
    public void batchSyncIpAssets(List<SearchResult> searchResults) {
        if (searchResults == null || searchResults.isEmpty()) {
            return;
        }
        
        logger.info("开始批量同步IP资产，数量: {}", searchResults.size());
        
        int successCount = 0;
        int errorCount = 0;
        
        for (SearchResult searchResult : searchResults) {
            try {
                syncIpAsset(searchResult);
                successCount++;
            } catch (Exception e) {
                errorCount++;
                logger.error("批量同步IP资产失败 - 搜索结果ID: {}", searchResult.getId(), e);
            }
        }
        
        logger.info("批量同步IP资产完成 - 成功: {}, 失败: {}", successCount, errorCount);
    }
    
    /**
     * 定时任务：同步未关联IP资产的搜索结果
     * 每小时执行一次，处理最近1小时内的新增搜索结果
     */
    @Scheduled(fixedRate = 3600000) // 1小时 = 3600000毫秒
    @Transactional
    public void syncUnlinkedSearchResults() {
        try {
            logger.info("开始执行IP资产同步定时任务");
            
            // 查找最近1小时内未关联IP资产的搜索结果
            LocalDateTime since = LocalDateTime.now().minusHours(1);
            List<SearchResult> unlinkedResults = searchResultRepository.findUnlinkedIpAssets(since);
            
            if (unlinkedResults.isEmpty()) {
                logger.info("没有需要同步的搜索结果");
                return;
            }
            
            logger.info("找到 {} 条需要同步IP资产的搜索结果", unlinkedResults.size());
            
            // 批量同步
            batchSyncIpAssets(unlinkedResults);
            
            logger.info("IP资产同步定时任务完成");
            
        } catch (Exception e) {
            logger.error("IP资产同步定时任务执行失败", e);
        }
    }
    
    /**
     * 手动触发全量同步
     * 同步所有未关联IP资产的搜索结果
     */
    @Transactional
    public void fullSync() {
        try {
            logger.info("开始执行IP资产全量同步");
            
            // 查找所有未关联IP资产的搜索结果
            List<SearchResult> unlinkedResults = searchResultRepository.findAllUnlinkedIpAssets();
            
            if (unlinkedResults.isEmpty()) {
                logger.info("没有需要同步的搜索结果");
                return;
            }
            
            logger.info("找到 {} 条需要同步IP资产的搜索结果", unlinkedResults.size());
            
            // 分批处理，避免内存溢出
            int batchSize = 1000;
            int totalBatches = (unlinkedResults.size() + batchSize - 1) / batchSize;
            
            for (int i = 0; i < totalBatches; i++) {
                int start = i * batchSize;
                int end = Math.min(start + batchSize, unlinkedResults.size());
                List<SearchResult> batch = unlinkedResults.subList(start, end);
                
                logger.info("处理第 {}/{} 批，数量: {}", i + 1, totalBatches, batch.size());
                batchSyncIpAssets(batch);
            }
            
            logger.info("IP资产全量同步完成");
            
        } catch (Exception e) {
            logger.error("IP资产全量同步失败", e);
            throw new RuntimeException("IP资产全量同步失败", e);
        }
    }
    
    /**
     * 重新计算IP资产统计信息
     */
    @Transactional
    public void recalculateIpAssetStats() {
        try {
            logger.info("开始重新计算IP资产统计信息");
            
            // 获取所有IP资产
            List<IpAsset> allIpAssets = ipAssetService.findAll();
            
            for (IpAsset ipAsset : allIpAssets) {
                // 重新计算资产数量
                long assetCount = searchResultRepository.countByIp(ipAsset.getIpAddress());
                ipAsset.setAssetCount((int) assetCount);
                
                // 更新最后发现时间
                LocalDateTime lastSeen = searchResultRepository.findLatestCreatedAtByIp(ipAsset.getIpAddress());
                if (lastSeen != null) {
                    ipAsset.setLastSeen(lastSeen);
                }
                
                // 更新状态
                ipAsset.updateStatus();
            }
            
            // 批量保存
            ipAssetService.saveAll(allIpAssets);
            
            logger.info("IP资产统计信息重新计算完成，处理了 {} 个IP资产", allIpAssets.size());
            
        } catch (Exception e) {
            logger.error("重新计算IP资产统计信息失败", e);
            throw new RuntimeException("重新计算IP资产统计信息失败", e);
        }
    }
    
    /**
     * 清理孤立的IP资产
     * 删除没有关联任何搜索结果的IP资产
     */
    @Transactional
    public void cleanupOrphanedIpAssets() {
        try {
            logger.info("开始清理孤立的IP资产");
            
            List<IpAsset> orphanedAssets = ipAssetService.findOrphanedIpAssets();
            
            if (orphanedAssets.isEmpty()) {
                logger.info("没有发现孤立的IP资产");
                return;
            }
            
            logger.info("发现 {} 个孤立的IP资产，准备删除", orphanedAssets.size());
            
            for (IpAsset asset : orphanedAssets) {
                ipAssetService.deleteIpAsset(asset.getId());
                logger.debug("删除孤立IP资产: {}", asset.getIpAddress());
            }
            
            logger.info("孤立IP资产清理完成，删除了 {} 个", orphanedAssets.size());
            
        } catch (Exception e) {
            logger.error("清理孤立IP资产失败", e);
            throw new RuntimeException("清理孤立IP资产失败", e);
        }
    }
}
