package com.z3rd0.system.controller;

import com.z3rd0.system.dto.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * Scanner服务代理控制器
 * 将Scanner相关的API请求代理到workbench-scanner模块
 */
@Slf4j
@RestController
@RequestMapping("/api/scanner")
@RequiredArgsConstructor
public class ScannerProxyController {

    private final RestTemplate restTemplate;

    // 从配置文件读取Scanner模块的基础URL
    @Value("${workbench.services.scanner.base-url:http://localhost:38888}")
    private String scannerBaseUrl;

    /**
     * 代理Scanner状态请求
     * 将请求转发到workbench-scanner模块的/api/scanner/status接口
     * @return Scanner状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getScannerStatus() {
        try {
            log.debug("代理Scanner状态请求到workbench-scanner模块");
            
            // 构建目标URL
            String targetUrl = scannerBaseUrl + "/api/scanner/status";
            
            // 发送请求到scanner模块
            ResponseEntity<Map> response = restTemplate.getForEntity(targetUrl, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.debug("Scanner状态请求代理成功");
                return ResponseEntity.ok(response.getBody());
            } else {
                log.warn("Scanner模块返回非成功状态: {}", response.getStatusCode());
                return createErrorResponse("Scanner模块响应异常", HttpStatus.BAD_GATEWAY);
            }
            
        } catch (Exception e) {
            log.error("代理Scanner状态请求失败: {}", e.getMessage(), e);
            
            // 如果scanner模块不可用，返回默认状态
            return createFallbackResponse(e);
        }
    }
    
    /**
     * 创建错误响应
     * @param message 错误消息
     * @param status HTTP状态码
     * @return 错误响应
     */
    private ResponseEntity<Map<String, Object>> createErrorResponse(String message, HttpStatus status) {
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("message", message);
        errorResponse.put("data", null);
        
        return ResponseEntity.status(status).body(errorResponse);
    }
    
    /**
     * 创建降级响应
     * 当scanner模块不可用时，返回默认状态信息
     * @param exception 异常信息
     * @return 降级响应
     */
    private ResponseEntity<Map<String, Object>> createFallbackResponse(Exception exception) {
        log.warn("Scanner模块不可用，返回降级状态: {}", exception.getMessage());
        
        Map<String, Object> fallbackData = new HashMap<>();
        fallbackData.put("service", "workbench-scanner");
        fallbackData.put("status", "不可用");
        fallbackData.put("timestamp", java.time.LocalDateTime.now().toString());
        
        // 浏览器状态信息
        Map<String, Object> browserInfo = new HashMap<>();
        browserInfo.put("initialized", false);
        browserInfo.put("status", "unknown");
        browserInfo.put("processing", false);
        browserInfo.put("instances", 0);
        fallbackData.put("browser", browserInfo);
        
        // 任务状态信息
        Map<String, Object> taskInfo = new HashMap<>();
        taskInfo.put("activeTasks", 0);
        taskInfo.put("queueSize", 0);
        taskInfo.put("lastTaskTime", "");
        fallbackData.put("tasks", taskInfo);
        
        // 健康状态
        fallbackData.put("health", "critical");
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Scanner模块不可用，返回降级状态");
        response.put("data", fallbackData);
        
        return ResponseEntity.ok(response);
    }
}
