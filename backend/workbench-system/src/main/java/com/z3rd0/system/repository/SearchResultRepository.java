package com.z3rd0.system.repository;

import com.z3rd0.common.model.SearchResult;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface SearchResultRepository extends JpaRepository<SearchResult, Long>, JpaSpecificationExecutor<SearchResult> {
    Page<SearchResult> findByIpContaining(String ip, Pageable pageable);

    // 统计查询方法
    @Query("SELECT COUNT(DISTINCT sr.ip) FROM SearchResult sr")
    Long countDistinctIp();

    @Query("SELECT COUNT(DISTINCT sr.domain) FROM SearchResult sr WHERE sr.domain IS NOT NULL")
    Long countDistinctDomain();

    @Query("SELECT COUNT(sr) FROM SearchResult sr WHERE sr.createdAt >= ?1")
    Long countByCreatedAtAfter(LocalDateTime date);

    /**
     * 统计指定时间范围内的搜索结果数量
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 搜索结果数量
     */
    @Query("SELECT COUNT(sr) FROM SearchResult sr WHERE sr.createdAt >= ?1 AND sr.createdAt <= ?2")
    Long countByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 统计本周新增的搜索结果数量
     * @param weekStart 本周开始时间（周一00:00:00）
     * @return 本周新增数量
     */
    @Query("SELECT COUNT(sr) FROM SearchResult sr WHERE sr.createdAt >= ?1")
    Long countThisWeekResults(LocalDateTime weekStart);

    /**
     * 统计本月新增的搜索结果数量
     * @param monthStart 本月开始时间（1日00:00:00）
     * @return 本月新增数量
     */
    @Query("SELECT COUNT(sr) FROM SearchResult sr WHERE sr.createdAt >= ?1")
    Long countThisMonthResults(LocalDateTime monthStart);

    @Query("SELECT sr.statusCode as statusCode, COUNT(sr) as count FROM SearchResult sr WHERE sr.statusCode IS NOT NULL GROUP BY sr.statusCode ORDER BY count DESC")
    List<Object[]> getStatusCodeStats();

    // 批量操作优化方法
    @Modifying
    @Query("UPDATE SearchResult sr SET sr.isRead = :value WHERE sr.id IN :ids")
    int batchUpdateIsRead(@Param("ids") List<Long> ids, @Param("value") Integer value);

    @Modifying
    @Query("UPDATE SearchResult sr SET sr.isExcluded = :value WHERE sr.id IN :ids")
    int batchUpdateIsExcluded(@Param("ids") List<Long> ids, @Param("value") Integer value);

    @Modifying
    @Query("DELETE FROM SearchResult sr WHERE sr.id IN :ids")
    int batchDeleteByIds(@Param("ids") List<Long> ids);

    @Query("SELECT COALESCE(sr.locationCountry, '未知') as location, COUNT(sr) as count FROM SearchResult sr GROUP BY sr.locationCountry ORDER BY count DESC")
    List<Object[]> getLocationStats();

    @Query("SELECT sr.port as port, COUNT(sr) as count FROM SearchResult sr WHERE sr.port IS NOT NULL GROUP BY sr.port ORDER BY count DESC")
    List<Object[]> getPortStats();

    @Query("SELECT DATE(sr.createdAt) as date, COUNT(sr) as count FROM SearchResult sr WHERE sr.createdAt >= ?1 GROUP BY DATE(sr.createdAt) ORDER BY date")
    List<Object[]> getTimeStats(LocalDateTime startDate);

    // IP资产相关查询方法
    Page<SearchResult> findByIp(String ip, Pageable pageable);

    @Query("SELECT sr FROM SearchResult sr WHERE sr.ipAssetId IS NULL AND sr.ip IS NOT NULL AND sr.createdAt >= :since")
    List<SearchResult> findUnlinkedIpAssets(@Param("since") LocalDateTime since);

    @Query("SELECT sr FROM SearchResult sr WHERE sr.ipAssetId IS NULL AND sr.ip IS NOT NULL")
    List<SearchResult> findAllUnlinkedIpAssets();

    @Query("SELECT COUNT(sr) FROM SearchResult sr WHERE sr.ip = :ip")
    long countByIp(@Param("ip") String ip);

    @Query("SELECT MAX(sr.createdAt) FROM SearchResult sr WHERE sr.ip = :ip")
    LocalDateTime findLatestCreatedAtByIp(@Param("ip") String ip);

    boolean existsByOriginalIdAndTitleAndPath(String originalId, String title, String path);
}