package com.z3rd0.system.repository;

import com.z3rd0.common.model.IpAsset;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * IP资产数据访问层
 */
@Repository
public interface IpAssetRepository extends JpaRepository<IpAsset, Long> {
    
    /**
     * 根据IP地址查找IP资产
     */
    Optional<IpAsset> findByIpAddress(String ipAddress);
    
    /**
     * 检查IP地址是否存在
     */
    boolean existsByIpAddress(String ipAddress);
    
    /**
     * 根据状态查找IP资产
     */
    Page<IpAsset> findByStatus(String status, Pageable pageable);
    
    /**
     * 根据IP地址模糊查询
     */
    @Query("SELECT i FROM IpAsset i WHERE i.ipAddress LIKE %:ipPattern%")
    Page<IpAsset> findByIpAddressContaining(@Param("ipPattern") String ipPattern, Pageable pageable);
    
    /**
     * 根据地理位置查询
     */
    Page<IpAsset> findByLocationCountryOrLocationProvinceOrLocationCity(
        String country, String province, String city, Pageable pageable);
    
    /**
     * 根据ISP查询
     */
    Page<IpAsset> findByIspContaining(String isp, Pageable pageable);
    
    /**
     * 根据ASN查询
     */
    Page<IpAsset> findByAsn(Integer asn, Pageable pageable);
    
    /**
     * 查找资产数量大于指定值的IP
     */
    @Query("SELECT i FROM IpAsset i WHERE i.assetCount >= :minCount ORDER BY i.assetCount DESC")
    Page<IpAsset> findByAssetCountGreaterThanEqual(@Param("minCount") Integer minCount, Pageable pageable);
    
    /**
     * 查找最近发现的IP资产
     */
    @Query("SELECT i FROM IpAsset i WHERE i.lastSeen >= :since ORDER BY i.lastSeen DESC")
    Page<IpAsset> findRecentlyDiscovered(@Param("since") LocalDateTime since, Pageable pageable);
    
    /**
     * 查找长时间未发现的IP资产
     */
    @Query("SELECT i FROM IpAsset i WHERE i.lastSeen < :before ORDER BY i.lastSeen ASC")
    Page<IpAsset> findNotSeenSince(@Param("before") LocalDateTime before, Pageable pageable);
    
    /**
     * 统计总IP数量
     */
    @Query("SELECT COUNT(i) FROM IpAsset i")
    long countTotalIps();
    
    /**
     * 统计活跃IP数量
     */
    @Query("SELECT COUNT(i) FROM IpAsset i WHERE i.status = 'ACTIVE'")
    long countActiveIps();
    
    /**
     * 统计非活跃IP数量
     */
    @Query("SELECT COUNT(i) FROM IpAsset i WHERE i.status = 'INACTIVE'")
    long countInactiveIps();
    
    /**
     * 统计IPv6地址数量
     */
    @Query("SELECT COUNT(i) FROM IpAsset i WHERE i.isIpv6 = true")
    long countIpv6Addresses();
    
    /**
     * 统计总资产数量
     */
    @Query("SELECT COALESCE(SUM(i.assetCount), 0) FROM IpAsset i")
    long sumTotalAssets();
    
    /**
     * 按国家统计IP数量
     */
    @Query("SELECT i.locationCountry, COUNT(i) FROM IpAsset i WHERE i.locationCountry IS NOT NULL GROUP BY i.locationCountry ORDER BY COUNT(i) DESC")
    List<Object[]> countByCountry();
    
    /**
     * 按ISP统计IP数量
     */
    @Query("SELECT i.isp, COUNT(i) FROM IpAsset i WHERE i.isp IS NOT NULL GROUP BY i.isp ORDER BY COUNT(i) DESC")
    List<Object[]> countByIsp();
    
    /**
     * 按ASN统计IP数量
     */
    @Query("SELECT i.asn, COUNT(i) FROM IpAsset i WHERE i.asn IS NOT NULL GROUP BY i.asn ORDER BY COUNT(i) DESC")
    List<Object[]> countByAsn();
    
    /**
     * 复合查询：支持多条件搜索
     */
    @Query("SELECT i FROM IpAsset i WHERE " +
           "(:ipPattern IS NULL OR i.ipAddress LIKE %:ipPattern%) AND " +
           "(:status IS NULL OR i.status = :status) AND " +
           "(:country IS NULL OR i.locationCountry = :country) AND " +
           "(:isp IS NULL OR i.isp LIKE %:isp%) AND " +
           "(:asn IS NULL OR i.asn = :asn) AND " +
           "(:minAssetCount IS NULL OR i.assetCount >= :minAssetCount)")
    Page<IpAsset> findByMultipleConditions(
        @Param("ipPattern") String ipPattern,
        @Param("status") String status,
        @Param("country") String country,
        @Param("isp") String isp,
        @Param("asn") Integer asn,
        @Param("minAssetCount") Integer minAssetCount,
        Pageable pageable);
    
    /**
     * 查找需要更新状态的IP（最后发现时间超过30天但状态仍为ACTIVE）
     */
    @Query("SELECT i FROM IpAsset i WHERE i.status = 'ACTIVE' AND i.lastSeen < :threshold")
    List<IpAsset> findActiveIpsNotSeenSince(@Param("threshold") LocalDateTime threshold);
    
    /**
     * 批量更新IP状态
     */
    @Modifying
    @Query("UPDATE IpAsset i SET i.status = :newStatus, i.updatedAt = :updateTime WHERE i.id IN :ids")
    int updateStatusByIds(@Param("ids") List<Long> ids, @Param("newStatus") String newStatus, @Param("updateTime") LocalDateTime updateTime);
}
