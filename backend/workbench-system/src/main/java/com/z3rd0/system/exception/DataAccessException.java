package com.z3rd0.system.exception;

/**
 * 数据访问异常
 * 用于处理数据库操作中的异常情况
 */
public class DataAccessException extends BusinessException {

    /**
     * 数据不存在异常
     */
    public static class DataNotFoundException extends DataAccessException {
        public DataNotFoundException(String entityType, Object id) {
            super(String.format("%s不存在: %s", entityType, id), 
                  "DATA_NOT_FOUND", 
                  new EntityInfo(entityType, id));
        }
    }

    /**
     * 数据重复异常
     */
    public static class DuplicateDataException extends DataAccessException {
        public DuplicateDataException(String entityType, String field, Object value) {
            super(String.format("%s的%s已存在: %s", entityType, field, value), 
                  "DUPLICATE_DATA", 
                  new DuplicateInfo(entityType, field, value));
        }
    }

    /**
     * 数据完整性异常
     */
    public static class DataIntegrityException extends DataAccessException {
        public DataIntegrityException(String reason, Throwable cause) {
            super("数据完整性约束违反: " + reason, "DATA_INTEGRITY_VIOLATION", reason, cause);
        }
    }

    /**
     * 数据库连接异常
     */
    public static class DatabaseConnectionException extends DataAccessException {
        public DatabaseConnectionException(String reason, Throwable cause) {
            super("数据库连接失败: " + reason, "DATABASE_CONNECTION_FAILED", reason, cause);
        }
    }

    /**
     * 查询超时异常
     */
    public static class QueryTimeoutException extends DataAccessException {
        public QueryTimeoutException(String query, long timeoutMs) {
            super(String.format("查询超时: %s (超时时间: %dms)", query, timeoutMs), 
                  "QUERY_TIMEOUT", 
                  new QueryInfo(query, timeoutMs));
        }
    }

    /**
     * 基础构造函数
     */
    public DataAccessException(String message) {
        super(message, "DATA_ACCESS_ERROR");
    }

    /**
     * 带错误代码的构造函数
     */
    public DataAccessException(String message, String errorCode) {
        super(message, errorCode);
    }

    /**
     * 带详细信息的构造函数
     */
    public DataAccessException(String message, String errorCode, Object details) {
        super(message, errorCode, details);
    }

    /**
     * 带原因异常的构造函数
     */
    public DataAccessException(String message, String errorCode, Object details, Throwable cause) {
        super(message, errorCode, details, cause);
    }

    /**
     * 实体信息
     */
    public static class EntityInfo {
        private final String entityType;
        private final Object id;

        public EntityInfo(String entityType, Object id) {
            this.entityType = entityType;
            this.id = id;
        }

        public String getEntityType() { return entityType; }
        public Object getId() { return id; }
    }

    /**
     * 重复数据信息
     */
    public static class DuplicateInfo {
        private final String entityType;
        private final String field;
        private final Object value;

        public DuplicateInfo(String entityType, String field, Object value) {
            this.entityType = entityType;
            this.field = field;
            this.value = value;
        }

        public String getEntityType() { return entityType; }
        public String getField() { return field; }
        public Object getValue() { return value; }
    }

    /**
     * 查询信息
     */
    public static class QueryInfo {
        private final String query;
        private final long timeoutMs;

        public QueryInfo(String query, long timeoutMs) {
            this.query = query;
            this.timeoutMs = timeoutMs;
        }

        public String getQuery() { return query; }
        public long getTimeoutMs() { return timeoutMs; }
    }
}
