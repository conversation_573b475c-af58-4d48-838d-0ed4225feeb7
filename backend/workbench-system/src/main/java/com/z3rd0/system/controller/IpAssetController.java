package com.z3rd0.system.controller;

import com.z3rd0.common.model.IpAsset;
import com.z3rd0.common.model.SearchResult;
import com.z3rd0.system.service.IpAssetService;
import com.z3rd0.system.service.IpAssetSyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * IP资产管理控制器
 */
@RestController
@RequestMapping("/api/ips")
@CrossOrigin(originPatterns = "*", allowCredentials = "true")
public class IpAssetController {
    
    private static final Logger logger = LoggerFactory.getLogger(IpAssetController.class);
    
    @Autowired
    private IpAssetService ipAssetService;

    @Autowired
    private IpAssetSyncService ipAssetSyncService;
    
    /**
     * 分页查询IP资产列表
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getIpAssets(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "lastSeen") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String ipPattern,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String country,
            @RequestParam(required = false) String isp,
            @RequestParam(required = false) Integer asn,
            @RequestParam(required = false) Integer minAssetCount) {
        
        try {
            // 创建排序对象
            Sort sort = Sort.by(sortDir.equalsIgnoreCase("desc") ? 
                Sort.Direction.DESC : Sort.Direction.ASC, sortBy);
            
            Pageable pageable = PageRequest.of(page, size, sort);
            
            // 查询IP资产
            Page<IpAsset> ipAssets = ipAssetService.findIpAssets(
                ipPattern, status, country, isp, asn, minAssetCount, pageable);
            
            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", Map.of(
                "list", ipAssets.getContent(),
                "total", ipAssets.getTotalElements(),
                "totalPages", ipAssets.getTotalPages(),
                "currentPage", ipAssets.getNumber(),
                "pageSize", ipAssets.getSize(),
                "hasNext", ipAssets.hasNext(),
                "hasPrevious", ipAssets.hasPrevious()
            ));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("查询IP资产列表失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "查询IP资产列表失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
    
    /**
     * 根据ID获取IP资产详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getIpAssetById(@PathVariable Long id) {
        try {
            Optional<IpAsset> ipAsset = ipAssetService.findById(id);
            
            Map<String, Object> response = new HashMap<>();
            if (ipAsset.isPresent()) {
                response.put("success", true);
                response.put("data", ipAsset.get());
            } else {
                response.put("success", false);
                response.put("message", "IP资产不存在");
                return ResponseEntity.status(404).body(response);
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取IP资产详情失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取IP资产详情失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
    
    /**
     * 根据IP地址获取IP资产详情
     */
    @GetMapping("/by-address/{ipAddress}")
    public ResponseEntity<Map<String, Object>> getIpAssetByAddress(@PathVariable String ipAddress) {
        try {
            Optional<IpAsset> ipAsset = ipAssetService.findByIpAddress(ipAddress);
            
            Map<String, Object> response = new HashMap<>();
            if (ipAsset.isPresent()) {
                response.put("success", true);
                response.put("data", ipAsset.get());
            } else {
                response.put("success", false);
                response.put("message", "IP资产不存在");
                return ResponseEntity.status(404).body(response);
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取IP资产详情失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取IP资产详情失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
    
    /**
     * 获取指定IP的所有关联资产
     */
    @GetMapping("/{ipAddress}/assets")
    public ResponseEntity<Map<String, Object>> getAssetsByIp(
            @PathVariable String ipAddress,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        try {
            // 创建排序对象
            Sort sort = Sort.by(sortDir.equalsIgnoreCase("desc") ? 
                Sort.Direction.DESC : Sort.Direction.ASC, sortBy);
            
            Pageable pageable = PageRequest.of(page, size, sort);
            
            // 查询关联资产
            Page<SearchResult> assets = ipAssetService.findAssetsByIpAddress(ipAddress, pageable);
            
            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", Map.of(
                "ipAddress", ipAddress,
                "assets", Map.of(
                    "list", assets.getContent(),
                    "total", assets.getTotalElements(),
                    "totalPages", assets.getTotalPages(),
                    "currentPage", assets.getNumber(),
                    "pageSize", assets.getSize(),
                    "hasNext", assets.hasNext(),
                    "hasPrevious", assets.hasPrevious()
                )
            ));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("查询IP关联资产失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "查询IP关联资产失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
    
    /**
     * 获取IP统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getIpStatistics() {
        try {
            Map<String, Object> stats = ipAssetService.getIpStatistics();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取IP统计信息失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取IP统计信息失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 更新IP资产备注
     */
    @PutMapping("/{id}/notes")
    public ResponseEntity<Map<String, Object>> updateNotes(
            @PathVariable Long id,
            @RequestBody Map<String, String> request) {

        try {
            String notes = request.get("notes");
            Optional<IpAsset> ipAssetOpt = ipAssetService.findById(id);

            if (ipAssetOpt.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "IP资产不存在");
                return ResponseEntity.status(404).body(response);
            }

            IpAsset ipAsset = ipAssetOpt.get();
            ipAsset.setNotes(notes);
            IpAsset savedAsset = ipAssetService.save(ipAsset);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "备注更新成功");
            response.put("data", savedAsset);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("更新IP备注失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "更新失败：" + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 删除IP资产
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteIpAsset(@PathVariable Long id) {
        try {
            ipAssetService.deleteIpAsset(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "IP资产删除成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("删除IP资产失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "删除IP资产失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
    
    /**
     * 批量删除IP资产
     */
    @DeleteMapping("/batch")
    public ResponseEntity<Map<String, Object>> batchDeleteIpAssets(@RequestBody List<Long> ids) {
        try {
            ipAssetService.deleteIpAssets(ids);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "批量删除IP资产成功，共删除 " + ids.size() + " 个");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("批量删除IP资产失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "批量删除IP资产失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
    
    /**
     * 手动触发IP状态更新
     */
    @PostMapping("/update-status")
    public ResponseEntity<Map<String, Object>> updateIpStatus() {
        try {
            ipAssetSyncService.fullSync();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "IP状态更新完成");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("更新IP状态失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "更新IP状态失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
}
