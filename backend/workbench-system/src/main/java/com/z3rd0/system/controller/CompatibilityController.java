package com.z3rd0.system.controller;

import com.z3rd0.common.model.SearchResult;
import com.z3rd0.common.model.Task;
import com.z3rd0.system.dto.ApiResponse;
import com.z3rd0.system.dto.PageResponse;
import com.z3rd0.system.service.SearchResultService;
import com.z3rd0.system.service.TaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 兼容性控制器
 * 提供与前端现有接口契约兼容的API端点
 * 在前端完成适配后，可以逐步迁移到标准化的ApiResponse格式
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class CompatibilityController {

    private final TaskService taskService;
    private final SearchResultService searchResultService;

    /**
     * 兼容性任务列表接口
     * 保持原有的Map响应格式，确保前端正常工作
     */
    @GetMapping("/tasks/legacy")
    public Map<String, Object> getTasksLegacy(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime
    ) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Task> taskPage = taskService.findByConditions(name, status, startTime, endTime, pageable);
        
        Map<String, Object> response = new HashMap<>();
        response.put("data", taskPage.getContent());
        response.put("total", taskPage.getTotalElements());
        response.put("currentPage", page + 1);
        response.put("pageSize", size);
        
        return response;
    }

    /**
     * 兼容性搜索结果接口
     * 保持原有的Map响应格式，确保前端正常工作
     */
    @GetMapping("/search/results/legacy")
    public Map<String, Object> getSearchResultsLegacy(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String ip,
            @RequestParam(required = false) String domain,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) Integer isRead,
            @RequestParam(required = false) Integer isExcluded,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime
    ) {
        // 创建按创建时间倒序排序的分页请求
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<SearchResult> resultPage = searchResultService.findByConditions(
            ip, domain, title, null, null, null, null, isRead, isExcluded, startTime, endTime, pageable);
        
        Map<String, Object> response = new HashMap<>();
        response.put("data", resultPage.getContent());
        response.put("total", resultPage.getTotalElements());
        response.put("page", resultPage.getNumber());
        response.put("size", resultPage.getSize());
        
        return response;
    }

    /**
     * 将新格式转换为旧格式的工具方法
     * 用于渐进式迁移
     */
    private Map<String, Object> convertToLegacyFormat(ApiResponse<PageResponse<?>> apiResponse) {
        Map<String, Object> legacyResponse = new HashMap<>();
        
        if (apiResponse.isSuccess() && apiResponse.getData() != null) {
            PageResponse<?> pageResponse = apiResponse.getData();
            legacyResponse.put("data", pageResponse.getList());
            legacyResponse.put("total", pageResponse.getTotal());
            legacyResponse.put("currentPage", pageResponse.getCurrentPage());
            legacyResponse.put("pageSize", pageResponse.getPageSize());
        } else {
            legacyResponse.put("data", null);
            legacyResponse.put("total", 0);
            legacyResponse.put("currentPage", 1);
            legacyResponse.put("pageSize", 10);
            legacyResponse.put("error", apiResponse.getMessage());
        }
        
        return legacyResponse;
    }
}
