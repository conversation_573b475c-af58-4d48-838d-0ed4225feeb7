package com.z3rd0.system;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Workbench System 主启动类
 * 只扫描system模块的包，避免与scanner模块的Bean冲突
 */
@SpringBootApplication
@EntityScan(basePackages = {"com.z3rd0.common.model", "com.z3rd0.system.model"})
@EnableScheduling
@EnableAsync
public class WorkbenchSystemApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(WorkbenchSystemApplication.class, args);
    }
} 