package com.z3rd0.system.service;

import com.z3rd0.common.model.Task;
import com.z3rd0.common.enums.ScheduleFrequency;
import com.z3rd0.system.repository.TaskRepository;
import com.z3rd0.system.service.TaskPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 任务调度器服务
 * 负责定时任务的调度和执行
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskScheduler {
    
    private final TaskRepository taskRepository;
    private final TaskPublisher taskPublisher;
    
    /**
     * 定时检查并执行定时任务
     * 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000) // 每60秒执行一次
    public void checkAndExecuteScheduledTasks() {
        try {
            log.debug("开始检查定时任务...");
            
            // 获取当前时间
            LocalTime currentTime = LocalTime.now();
            String currentTimeStr = currentTime.format(DateTimeFormatter.ofPattern("HH:mm"));
            
            // 查找需要在当前时间执行的定时任务
            List<Task> tasksToExecute = taskRepository.findByScheduleTime(currentTimeStr);
            
            if (tasksToExecute.isEmpty()) {
                log.debug("当前时间 {} 没有需要执行的定时任务", currentTimeStr);
                return;
            }
            
            log.info("发现 {} 个需要在 {} 执行的定时任务", tasksToExecute.size(), currentTimeStr);
            
            // 异步发送所有定时任务到消息队列
            for (Task task : tasksToExecute) {
                if (shouldExecuteTask(task)) {
                    sendTaskToQueue(task);
                }
            }
            
        } catch (Exception e) {
            log.error("检查定时任务时发生错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 判断任务是否应该执行
     */
    private boolean shouldExecuteTask(Task task) {
        // 检查任务是否启用定时功能
        if (!Boolean.TRUE.equals(task.getScheduleEnabled())) {
            log.debug("任务 {} 的定时功能未启用", task.getName());
            return false;
        }

        // 检查任务状态
        if (!"ACTIVE".equals(task.getScheduleStatus())) {
            log.debug("任务 {} 的定时状态不是ACTIVE: {}", task.getName(), task.getScheduleStatus());
            return false;
        }

        // 根据执行频率判断是否应该执行
        if (!shouldExecuteByFrequency(task)) {
            log.debug("任务 {} 根据执行频率判断不应该执行", task.getName());
            return false;
        }

        return true;
    }
    
    /**
     * 根据执行频率判断是否应该执行
     */
    private boolean shouldExecuteByFrequency(Task task) {
        if (task.getLastScheduledAt() == null) {
            // 从未执行过，应该执行
            return true;
        }

        ScheduleFrequency frequency;
        try {
            frequency = ScheduleFrequency.fromCode(task.getScheduleFrequency());
        } catch (Exception e) {
            log.warn("任务 {} 的执行频率配置无效: {}, 使用默认频率DAILY", task.getName(), task.getScheduleFrequency());
            frequency = ScheduleFrequency.DAILY;
        }

        LocalDateTime lastExecution = task.getLastScheduledAt();
        LocalDateTime now = LocalDateTime.now();

        // 计算距离上次执行的天数
        long daysSinceLastExecution = java.time.temporal.ChronoUnit.DAYS.between(
            lastExecution.toLocalDate(), now.toLocalDate());

        // 根据频率判断是否应该执行
        boolean shouldExecute = daysSinceLastExecution >= frequency.getIntervalDays();

        log.debug("任务 {} 上次执行时间: {}, 执行频率: {}, 距离上次执行天数: {}, 是否应该执行: {}",
                task.getName(), lastExecution, frequency.getName(), daysSinceLastExecution, shouldExecute);

        return shouldExecute;
    }
    
    /**
     * 异步发送任务到消息队列
     */
    private void sendTaskToQueue(Task task) {
        CompletableFuture.runAsync(() -> {
            try {
                log.info("开始发送定时任务到队列: {} (ID: {})", task.getName(), task.getId());

                // 计算时间范围
                String timeRange = calculateTimeRange(task);
                log.info("任务 {} 自动计算的时间范围: {}", task.getName(), timeRange);

                // 发布任务到消息队列，使用计算出的时间范围
                // TaskPublisher会处理任务状态更新和队列发送
                taskPublisher.publishSearchTaskWithId(task.getId(), task.getName(), task.getRule(), timeRange);

                // 更新最后定时执行时间（仅用于频率判断）
                task.setLastScheduledAt(LocalDateTime.now());
                taskRepository.save(task);

                log.info("定时任务已发送到队列: {} (ID: {}), 时间范围: {}", task.getName(), task.getId(), timeRange);

            } catch (Exception e) {
                log.error("发送定时任务到队列失败: {} (ID: {}), 错误: {}",
                        task.getName(), task.getId(), e.getMessage(), e);
            }
        });
    }

    /**
     * 计算任务的时间范围
     * @param task 任务对象
     * @return 时间范围字符串，格式为 "YYYY-MM-DD,YYYY-MM-DD"
     */
    private String calculateTimeRange(Task task) {
        ScheduleFrequency frequency;
        try {
            frequency = ScheduleFrequency.fromCode(task.getScheduleFrequency());
        } catch (Exception e) {
            log.warn("任务 {} 的执行频率配置无效: {}, 使用默认频率DAILY", task.getName(), task.getScheduleFrequency());
            frequency = ScheduleFrequency.DAILY;
        }

        LocalDate today = LocalDate.now();
        LocalDate startDate;
        LocalDate endDate;

        switch (frequency) {
            case DAILY:
                // 每日执行：时间范围为前一天（昨天）
                startDate = today.minusDays(1);
                endDate = today.minusDays(1);
                break;
            case EVERY_THREE_DAYS:
                // 每三日执行：时间范围为前三天
                startDate = today.minusDays(3);
                endDate = today.minusDays(1);
                break;
            default:
                // 默认为每日执行
                startDate = today.minusDays(1);
                endDate = today.minusDays(1);
                break;
        }

        // 格式化为字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String timeRange = startDate.format(formatter) + "," + endDate.format(formatter);

        log.debug("任务 {} 执行频率: {}, 计算的时间范围: {}", task.getName(), frequency.getName(), timeRange);

        return timeRange;
    }

    /**
     * 手动触发定时任务执行
     */
    public boolean triggerScheduledTask(Long taskId) {
        try {
            Task task = taskRepository.findById(taskId)
                    .orElseThrow(() -> new IllegalArgumentException("任务不存在: " + taskId));
            
            if (!Boolean.TRUE.equals(task.getScheduleEnabled())) {
                log.warn("尝试触发未启用定时功能的任务: {} (ID: {})", task.getName(), taskId);
                return false;
            }
            
            log.info("手动触发定时任务: {} (ID: {})", task.getName(), taskId);
            sendTaskToQueue(task);
            
            return true;
            
        } catch (Exception e) {
            log.error("手动触发定时任务失败: taskId={}, 错误: {}", taskId, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 暂停定时任务
     */
    public boolean pauseScheduledTask(Long taskId) {
        try {
            Task task = taskRepository.findById(taskId)
                    .orElseThrow(() -> new IllegalArgumentException("任务不存在: " + taskId));
            
            task.setScheduleStatus("PAUSED");
            taskRepository.save(task);
            
            log.info("定时任务已暂停: {} (ID: {})", task.getName(), taskId);
            return true;
            
        } catch (Exception e) {
            log.error("暂停定时任务失败: taskId={}, 错误: {}", taskId, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 恢复定时任务
     */
    public boolean resumeScheduledTask(Long taskId) {
        try {
            Task task = taskRepository.findById(taskId)
                    .orElseThrow(() -> new IllegalArgumentException("任务不存在: " + taskId));
            
            task.setScheduleStatus("ACTIVE");
            taskRepository.save(task);
            
            log.info("定时任务已恢复: {} (ID: {})", task.getName(), taskId);
            return true;
            
        } catch (Exception e) {
            log.error("恢复定时任务失败: taskId={}, 错误: {}", taskId, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 获取定时任务统计信息
     */
    public ScheduleStatistics getScheduleStatistics() {
        try {
            long totalScheduled = taskRepository.countByScheduleEnabled(true);
            long activeScheduled = taskRepository.countByScheduleEnabledAndScheduleStatus(true, "ACTIVE");
            long pausedScheduled = taskRepository.countByScheduleEnabledAndScheduleStatus(true, "PAUSED");
            
            return new ScheduleStatistics(totalScheduled, activeScheduled, pausedScheduled);
            
        } catch (Exception e) {
            log.error("获取定时任务统计信息失败: {}", e.getMessage(), e);
            return new ScheduleStatistics(0, 0, 0);
        }
    }
    
    /**
     * 定时任务统计信息
     */
    public static class ScheduleStatistics {
        private final long totalScheduled;
        private final long activeScheduled;
        private final long pausedScheduled;
        
        public ScheduleStatistics(long totalScheduled, long activeScheduled, long pausedScheduled) {
            this.totalScheduled = totalScheduled;
            this.activeScheduled = activeScheduled;
            this.pausedScheduled = pausedScheduled;
        }
        
        public long getTotalScheduled() { return totalScheduled; }
        public long getActiveScheduled() { return activeScheduled; }
        public long getPausedScheduled() { return pausedScheduled; }
    }
}
