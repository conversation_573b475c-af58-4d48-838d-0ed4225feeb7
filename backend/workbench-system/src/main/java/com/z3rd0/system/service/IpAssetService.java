package com.z3rd0.system.service;

import com.z3rd0.common.model.IpAsset;
import com.z3rd0.common.model.SearchResult;
import com.z3rd0.system.repository.IpAssetRepository;
import com.z3rd0.system.repository.SearchResultRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * IP资产管理服务
 */
@Service
public class IpAssetService {
    
    private static final Logger logger = LoggerFactory.getLogger(IpAssetService.class);
    
    @Autowired
    private IpAssetRepository ipAssetRepository;
    
    @Autowired
    private SearchResultRepository searchResultRepository;
    
    /**
     * 创建或更新IP资产
     */
    @Transactional
    public IpAsset createOrUpdateIpAsset(String ipAddress, SearchResult searchResult) {
        if (ipAddress == null || ipAddress.trim().isEmpty()) {
            return null;
        }
        
        Optional<IpAsset> existingAsset = ipAssetRepository.findByIpAddress(ipAddress);
        IpAsset ipAsset;
        
        if (existingAsset.isPresent()) {
            // 更新现有IP资产
            ipAsset = existingAsset.get();
            ipAsset.updateLastSeen();
            ipAsset.incrementAssetCount();
            
            // 更新地理位置信息（如果搜索结果中有更详细的信息）
            updateLocationInfo(ipAsset, searchResult);
            
            // 更新ISP和组织信息
            updateNetworkInfo(ipAsset, searchResult);
            
        } else {
            // 创建新的IP资产
            ipAsset = new IpAsset();
            ipAsset.setIpAddress(ipAddress);
            ipAsset.setAssetCount(1);
            
            // 设置地理位置信息
            updateLocationInfo(ipAsset, searchResult);
            
            // 设置网络信息
            updateNetworkInfo(ipAsset, searchResult);
            
            logger.info("创建新IP资产: {}", ipAddress);
        }
        
        // 更新状态
        ipAsset.updateStatus();
        
        return ipAssetRepository.save(ipAsset);
    }
    
    /**
     * 更新地理位置信息
     */
    private void updateLocationInfo(IpAsset ipAsset, SearchResult searchResult) {
        if (searchResult.getLocationCountry() != null && !searchResult.getLocationCountry().isEmpty()) {
            ipAsset.setLocationCountry(searchResult.getLocationCountry());
        }
        if (searchResult.getLocationProvince() != null && !searchResult.getLocationProvince().isEmpty()) {
            ipAsset.setLocationProvince(searchResult.getLocationProvince());
        }
        if (searchResult.getLocationCity() != null && !searchResult.getLocationCity().isEmpty()) {
            ipAsset.setLocationCity(searchResult.getLocationCity());
        }
    }
    
    /**
     * 更新网络信息
     */
    private void updateNetworkInfo(IpAsset ipAsset, SearchResult searchResult) {
        if (searchResult.getLocationIsp() != null && !searchResult.getLocationIsp().isEmpty()) {
            ipAsset.setIsp(searchResult.getLocationIsp());
        }
        if (searchResult.getAsn() != null) {
            ipAsset.setAsn(searchResult.getAsn());
        }
        if (searchResult.getOrg() != null && !searchResult.getOrg().isEmpty()) {
            ipAsset.setOrganization(searchResult.getOrg());
        }
        
        // 更新端口信息
        updatePortInfo(ipAsset, searchResult);
        
        // 更新域名信息
        updateDomainInfo(ipAsset, searchResult);
    }
    
    /**
     * 更新端口信息
     */
    private void updatePortInfo(IpAsset ipAsset, SearchResult searchResult) {
        if (searchResult.getPort() != null && !searchResult.getPort().isEmpty()) {
            Set<String> ports = parsePortsFromJson(ipAsset.getCommonPorts());
            ports.add(searchResult.getPort());
            ipAsset.setCommonPorts(portsToJson(ports));
        }
    }
    
    /**
     * 更新域名信息
     */
    private void updateDomainInfo(IpAsset ipAsset, SearchResult searchResult) {
        if (searchResult.getDomain() != null && !searchResult.getDomain().isEmpty()) {
            Set<String> domains = parseDomainsFromJson(ipAsset.getAssociatedDomains());
            domains.add(searchResult.getDomain());
            ipAsset.setAssociatedDomains(domainsToJson(domains));
        }
    }
    
    /**
     * 解析端口JSON
     */
    private Set<String> parsePortsFromJson(String portsJson) {
        // 简单实现，实际可以使用Jackson等JSON库
        Set<String> ports = new HashSet<>();
        if (portsJson != null && !portsJson.isEmpty()) {
            try {
                // 移除JSON格式字符，提取端口号
                String cleaned = portsJson.replaceAll("[\\[\\]\"\\s]", "");
                if (!cleaned.isEmpty()) {
                    ports.addAll(Arrays.asList(cleaned.split(",")));
                }
            } catch (Exception e) {
                logger.warn("解析端口JSON失败: {}", portsJson, e);
            }
        }
        return ports;
    }
    
    /**
     * 端口集合转JSON
     */
    private String portsToJson(Set<String> ports) {
        if (ports.isEmpty()) {
            return "[]";
        }
        return "[\"" + String.join("\",\"", ports) + "\"]";
    }
    
    /**
     * 解析域名JSON
     */
    private Set<String> parseDomainsFromJson(String domainsJson) {
        Set<String> domains = new HashSet<>();
        if (domainsJson != null && !domainsJson.isEmpty()) {
            try {
                String cleaned = domainsJson.replaceAll("[\\[\\]\"\\s]", "");
                if (!cleaned.isEmpty()) {
                    domains.addAll(Arrays.asList(cleaned.split(",")));
                }
            } catch (Exception e) {
                logger.warn("解析域名JSON失败: {}", domainsJson, e);
            }
        }
        return domains;
    }
    
    /**
     * 域名集合转JSON
     */
    private String domainsToJson(Set<String> domains) {
        if (domains.isEmpty()) {
            return "[]";
        }
        return "[\"" + String.join("\",\"", domains) + "\"]";
    }
    
    /**
     * 分页查询IP资产
     */
    public Page<IpAsset> findIpAssets(String ipPattern, String status, String country, 
                                     String isp, Integer asn, Integer minAssetCount, 
                                     Pageable pageable) {
        return ipAssetRepository.findByMultipleConditions(
            ipPattern, status, country, isp, asn, minAssetCount, pageable);
    }
    
    /**
     * 根据ID查找IP资产
     */
    public Optional<IpAsset> findById(Long id) {
        return ipAssetRepository.findById(id);
    }
    
    /**
     * 根据IP地址查找IP资产
     */
    public Optional<IpAsset> findByIpAddress(String ipAddress) {
        return ipAssetRepository.findByIpAddress(ipAddress);
    }
    
    /**
     * 获取指定IP的所有关联资产
     */
    public Page<SearchResult> findAssetsByIpAddress(String ipAddress, Pageable pageable) {
        return searchResultRepository.findByIp(ipAddress, pageable);
    }
    
    /**
     * 获取IP统计信息
     */
    public Map<String, Object> getIpStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("totalIps", ipAssetRepository.countTotalIps());
        stats.put("activeIps", ipAssetRepository.countActiveIps());
        stats.put("inactiveIps", ipAssetRepository.countInactiveIps());
        stats.put("ipv6Count", ipAssetRepository.countIpv6Addresses());
        stats.put("totalAssets", ipAssetRepository.sumTotalAssets());
        
        // 按国家统计
        List<Object[]> countryStats = ipAssetRepository.countByCountry();
        stats.put("countryDistribution", countryStats.stream()
            .limit(10) // 只取前10个
            .collect(Collectors.toMap(
                row -> row[0] != null ? row[0].toString() : "未知",
                row -> row[1]
            )));
        
        // 按ISP统计
        List<Object[]> ispStats = ipAssetRepository.countByIsp();
        stats.put("ispDistribution", ispStats.stream()
            .limit(10)
            .collect(Collectors.toMap(
                row -> row[0] != null ? row[0].toString() : "未知",
                row -> row[1]
            )));
        
        return stats;
    }
    
    /**
     * 定时任务：更新IP状态
     * 每天凌晨2点执行，将30天未发现的IP标记为非活跃
     */
    @Scheduled(cron = "0 0 2 * * ?")
    @Transactional
    public void updateIpStatus() {
        logger.info("开始执行IP状态更新任务");
        
        LocalDateTime threshold = LocalDateTime.now().minusDays(30);
        List<IpAsset> inactiveIps = ipAssetRepository.findActiveIpsNotSeenSince(threshold);
        
        for (IpAsset ip : inactiveIps) {
            ip.setStatus("INACTIVE");
            ip.setUpdatedAt(LocalDateTime.now());
        }
        
        if (!inactiveIps.isEmpty()) {
            ipAssetRepository.saveAll(inactiveIps);
            logger.info("更新了 {} 个IP的状态为非活跃", inactiveIps.size());
        }
        
        logger.info("IP状态更新任务完成");
    }
    
    /**
     * 删除IP资产
     */
    @Transactional
    public void deleteIpAsset(Long id) {
        ipAssetRepository.deleteById(id);
    }
    
    /**
     * 批量删除IP资产
     */
    @Transactional
    public void deleteIpAssets(List<Long> ids) {
        ipAssetRepository.deleteAllById(ids);
    }

    /**
     * 查找所有IP资产
     */
    public List<IpAsset> findAll() {
        return ipAssetRepository.findAll();
    }

    /**
     * 保存IP资产
     */
    @Transactional
    public IpAsset save(IpAsset ipAsset) {
        return ipAssetRepository.save(ipAsset);
    }

    /**
     * 批量保存IP资产
     */
    @Transactional
    public void saveAll(List<IpAsset> ipAssets) {
        ipAssetRepository.saveAll(ipAssets);
    }

    /**
     * 查找孤立的IP资产（没有关联任何搜索结果的IP资产）
     */
    public List<IpAsset> findOrphanedIpAssets() {
        List<IpAsset> allIpAssets = ipAssetRepository.findAll();
        return allIpAssets.stream()
            .filter(ipAsset -> searchResultRepository.countByIp(ipAsset.getIpAddress()) == 0)
            .collect(java.util.stream.Collectors.toList());
    }
}
