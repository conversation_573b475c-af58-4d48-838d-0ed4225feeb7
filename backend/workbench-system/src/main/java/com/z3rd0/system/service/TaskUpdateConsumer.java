package com.z3rd0.system.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.z3rd0.common.model.Task;
import com.z3rd0.system.constants.RabbitMQConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

/**
 * 任务更新消费者
 * 接收来自scanner模块的任务更新消息
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskUpdateConsumer {

    private final TaskService taskService;
    private final ObjectMapper objectMapper;

    /**
     * 处理任务更新消息
     * @param message RabbitMQ消息
     */
    @RabbitListener(queues = RabbitMQConstants.TASK_UPDATE_QUEUE_NAME)
    public void handleTaskUpdate(Message message) {
        try {
            // 解析消息内容
            String messageBody = new String(message.getBody(), "UTF-8");
            log.debug("收到任务更新消息: {}", messageBody);

            @SuppressWarnings("unchecked")
            Map<String, Object> messageData = objectMapper.readValue(messageBody, Map.class);

            String action = (String) messageData.get("action");
            if (!RabbitMQConstants.ACTION_UPDATE_EXECUTION_INFO.equals(action)) {
                log.warn("未知的任务更新动作: {}", action);
                return;
            }

            // 提取任务信息
            Long taskId = Long.valueOf(messageData.get("taskId").toString());
            String filterRange = (String) messageData.get("filterRange");

            log.info("处理任务执行信息更新: ID={}, 筛选范围={}", taskId, filterRange);

            // 更新任务执行信息
            updateTaskExecutionInfo(taskId, filterRange);

        } catch (Exception e) {
            log.error("处理任务更新消息失败: {}", e.getMessage(), e);
            throw new RuntimeException("处理任务更新消息失败", e);
        }
    }

    /**
     * 更新任务执行信息
     * @param taskId 任务ID
     * @param filterRange 筛选范围
     */
    private void updateTaskExecutionInfo(Long taskId, String filterRange) {
        try {
            Optional<Task> taskOpt = taskService.findById(taskId);
            if (!taskOpt.isPresent()) {
                log.error("找不到指定ID的任务: {}", taskId);
                return;
            }

            Task task = taskOpt.get();
            task.updateExecutionInfo(filterRange);
            taskService.save(task);

            log.info("任务执行信息更新成功: ID={}, 筛选范围={}", taskId, filterRange);
        } catch (Exception e) {
            log.error("更新任务执行信息失败: ID={}, 筛选范围={}, 错误={}",
                     taskId, filterRange, e.getMessage(), e);
            throw e;
        }
    }
}
