package com.z3rd0.system.exception;

import com.z3rd0.system.dto.ApiResponse;
import com.z3rd0.system.monitoring.ExceptionMonitor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.dao.QueryTimeoutException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理系统中的异常，提供友好的错误响应
 * 支持多种异常类型和统一的响应格式，集成异常监控功能
 */
@Slf4j
@RestControllerAdvice
@RequiredArgsConstructor
public class GlobalExceptionHandler {

    private final ExceptionMonitor exceptionMonitor;

    // ==================== 业务异常处理 ====================

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ApiResponse<Object>> handleBusinessException(BusinessException e, HttpServletRequest request) {
        log.warn("业务异常: {} - {}", e.getErrorCode(), e.getMessage());

        // 记录异常统计
        exceptionMonitor.recordException("BusinessException", e.getMessage(), e.getErrorCode());

        ApiResponse<Object> response = ApiResponse.error(e.getMessage(), e.getErrorCode(), e.getDetails());
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理任务异常
     */
    @ExceptionHandler(TaskException.class)
    public ResponseEntity<ApiResponse<Object>> handleTaskException(TaskException e, HttpServletRequest request) {
        log.warn("任务异常: {} - {}", e.getErrorCode(), e.getMessage());

        ApiResponse<Object> response = ApiResponse.error(e.getMessage(), e.getErrorCode(), e.getDetails());
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理数据访问异常
     */
    @ExceptionHandler(DataAccessException.class)
    public ResponseEntity<ApiResponse<Object>> handleDataAccessException(DataAccessException e, HttpServletRequest request) {
        log.error("数据访问异常: {} - {}", e.getErrorCode(), e.getMessage(), e);

        ApiResponse<Object> response = ApiResponse.error(e.getMessage(), e.getErrorCode(), e.getDetails());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    // ==================== 参数验证异常处理 ====================

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResponse<Object>> handleIllegalArgumentException(IllegalArgumentException e, HttpServletRequest request) {
        log.warn("参数验证失败: {}", e.getMessage());

        ApiResponse<Object> response = ApiResponse.error(e.getMessage(), "INVALID_PARAMETER");
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理方法参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Object>> handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
        log.warn("方法参数验证失败: {}", e.getMessage());

        Map<String, String> errors = e.getBindingResult()
                .getFieldErrors()
                .stream()
                .collect(Collectors.toMap(
                    FieldError::getField,
                    FieldError::getDefaultMessage,
                    (existing, replacement) -> existing
                ));

        ApiResponse<Object> response = ApiResponse.error("参数验证失败", "VALIDATION_FAILED", errors);
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<ApiResponse<Object>> handleBindException(BindException e, HttpServletRequest request) {
        log.warn("参数绑定失败: {}", e.getMessage());

        Map<String, String> errors = e.getBindingResult()
                .getFieldErrors()
                .stream()
                .collect(Collectors.toMap(
                    FieldError::getField,
                    FieldError::getDefaultMessage,
                    (existing, replacement) -> existing
                ));

        ApiResponse<Object> response = ApiResponse.error("参数绑定失败", "BINDING_FAILED", errors);
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<ApiResponse<Object>> handleMissingServletRequestParameterException(MissingServletRequestParameterException e, HttpServletRequest request) {
        log.warn("缺少请求参数: {}", e.getMessage());

        Map<String, String> details = new HashMap<>();
        details.put("parameterName", e.getParameterName());
        details.put("parameterType", e.getParameterType());

        ApiResponse<Object> response = ApiResponse.error(
            String.format("缺少必需的请求参数: %s", e.getParameterName()),
            "MISSING_PARAMETER",
            details
        );
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理方法参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ApiResponse<Object>> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        log.warn("方法参数类型不匹配: {}", e.getMessage());

        Map<String, Object> details = new HashMap<>();
        details.put("parameterName", e.getName());
        details.put("requiredType", e.getRequiredType() != null ? e.getRequiredType().getSimpleName() : "unknown");
        details.put("providedValue", e.getValue());

        ApiResponse<Object> response = ApiResponse.error(
            String.format("参数类型不匹配: %s", e.getName()),
            "TYPE_MISMATCH",
            details
        );
        return ResponseEntity.badRequest().body(response);
    }

    // ==================== HTTP相关异常处理 ====================

    /**
     * 处理HTTP请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<ApiResponse<Object>> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        log.warn("HTTP请求方法不支持: {}", e.getMessage());

        Map<String, Object> details = new HashMap<>();
        details.put("method", e.getMethod());
        details.put("supportedMethods", e.getSupportedMethods());
        details.put("requestUrl", request.getRequestURL().toString());

        ApiResponse<Object> response = ApiResponse.error(
            String.format("不支持的HTTP方法: %s", e.getMethod()),
            "METHOD_NOT_SUPPORTED",
            details
        );
        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).body(response);
    }

    /**
     * 处理HTTP消息不可读异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<ApiResponse<Object>> handleHttpMessageNotReadableException(HttpMessageNotReadableException e, HttpServletRequest request) {
        log.warn("HTTP消息不可读: {}", e.getMessage());

        ApiResponse<Object> response = ApiResponse.error("请求体格式错误或不可读", "MESSAGE_NOT_READABLE");
        return ResponseEntity.badRequest().body(response);
    }

    // ==================== 数据库相关异常处理 ====================

    /**
     * 处理实体未找到异常
     */
    @ExceptionHandler(EntityNotFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleEntityNotFoundException(EntityNotFoundException e, HttpServletRequest request) {
        log.warn("实体未找到: {}", e.getMessage());

        ApiResponse<Object> response = ApiResponse.error("请求的资源不存在", "ENTITY_NOT_FOUND");
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }

    /**
     * 处理数据完整性违反异常
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity<ApiResponse<Object>> handleDataIntegrityViolationException(DataIntegrityViolationException e, HttpServletRequest request) {
        log.error("数据完整性违反: {}", e.getMessage(), e);

        String message = "数据操作违反完整性约束";
        if (e.getCause() != null && e.getCause().getMessage() != null) {
            String causeMessage = e.getCause().getMessage().toLowerCase();
            if (causeMessage.contains("duplicate") || causeMessage.contains("unique")) {
                message = "数据已存在，不能重复添加";
            } else if (causeMessage.contains("foreign key")) {
                message = "数据存在关联关系，无法删除";
            }
        }

        ApiResponse<Object> response = ApiResponse.error(message, "DATA_INTEGRITY_VIOLATION");
        return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
    }

    /**
     * 处理重复键异常
     */
    @ExceptionHandler(DuplicateKeyException.class)
    public ResponseEntity<ApiResponse<Object>> handleDuplicateKeyException(DuplicateKeyException e, HttpServletRequest request) {
        log.warn("重复键异常: {}", e.getMessage());

        ApiResponse<Object> response = ApiResponse.error("数据已存在，不能重复添加", "DUPLICATE_KEY");
        return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
    }

    /**
     * 处理查询超时异常
     */
    @ExceptionHandler(QueryTimeoutException.class)
    public ResponseEntity<ApiResponse<Object>> handleQueryTimeoutException(QueryTimeoutException e, HttpServletRequest request) {
        log.error("查询超时: {}", e.getMessage(), e);

        ApiResponse<Object> response = ApiResponse.error("查询超时，请稍后重试", "QUERY_TIMEOUT");
        return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT).body(response);
    }

    // ==================== 系统异常处理 ====================

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    public ResponseEntity<ApiResponse<Object>> handleNullPointerException(NullPointerException e, HttpServletRequest request) {
        log.error("空指针异常: {}", e.getMessage(), e);

        ApiResponse<Object> response = ApiResponse.error("系统内部错误", "NULL_POINTER_ERROR");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ApiResponse<Object>> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        log.error("运行时异常: {}", e.getMessage(), e);

        ApiResponse<Object> response = ApiResponse.error("系统运行异常: " + e.getMessage(), "RUNTIME_ERROR");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理JSON序列化异常
     */
    @ExceptionHandler({com.fasterxml.jackson.core.JsonProcessingException.class,
                      com.fasterxml.jackson.databind.JsonMappingException.class})
    public ResponseEntity<ApiResponse<Object>> handleJsonException(Exception e, HttpServletRequest request) {
        log.error("JSON序列化异常: {}", e.getMessage(), e);

        Map<String, Object> details = new HashMap<>();
        details.put("requestUrl", request.getRequestURL().toString());
        details.put("exceptionType", e.getClass().getSimpleName());

        ApiResponse<Object> response = ApiResponse.error("数据序列化失败", "JSON_SERIALIZATION_ERROR", details);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理数据库连接异常
     */
    @ExceptionHandler({java.sql.SQLException.class,
                      org.springframework.dao.DataAccessResourceFailureException.class})
    public ResponseEntity<ApiResponse<Object>> handleDatabaseException(Exception e, HttpServletRequest request) {
        log.error("数据库连接异常: {}", e.getMessage(), e);

        Map<String, Object> details = new HashMap<>();
        details.put("requestUrl", request.getRequestURL().toString());
        details.put("exceptionType", e.getClass().getSimpleName());

        ApiResponse<Object> response = ApiResponse.error("数据库连接失败，请稍后重试", "DATABASE_CONNECTION_ERROR", details);
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
    }

    /**
     * 处理通用异常 - 增强版本，提供更详细的错误信息
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleException(Exception e, HttpServletRequest request) {
        log.error("未知异常: {}", e.getMessage(), e);

        // 记录请求信息用于调试
        Map<String, Object> debugInfo = new HashMap<>();
        debugInfo.put("requestUrl", request.getRequestURL().toString());
        debugInfo.put("requestMethod", request.getMethod());
        debugInfo.put("userAgent", request.getHeader("User-Agent"));
        debugInfo.put("remoteAddr", request.getRemoteAddr());
        debugInfo.put("timestamp", LocalDateTime.now());
        debugInfo.put("exceptionType", e.getClass().getSimpleName());

        // 获取根本原因
        Throwable rootCause = e;
        while (rootCause.getCause() != null && rootCause.getCause() != rootCause) {
            rootCause = rootCause.getCause();
        }
        debugInfo.put("rootCause", rootCause.getClass().getSimpleName());
        debugInfo.put("rootCauseMessage", rootCause.getMessage());

        log.error("异常详细信息: {}", debugInfo);

        // 记录异常统计
        exceptionMonitor.recordException("UnknownException", e.getMessage(), "INTERNAL_SERVER_ERROR");

        ApiResponse<Object> response = ApiResponse.error(
            "服务器内部错误，请联系管理员",
            "INTERNAL_SERVER_ERROR",
            debugInfo
        );
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}
