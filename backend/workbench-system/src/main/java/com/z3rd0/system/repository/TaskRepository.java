package com.z3rd0.system.repository;

import com.z3rd0.common.model.Task;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 任务数据访问层
 */
@Repository
public interface TaskRepository extends JpaRepository<Task, Long>, JpaSpecificationExecutor<Task> {
    
    /**
     * 根据任务状态查找任务
     * @param status 任务状态
     * @return 任务列表
     */
    List<Task> findByStatus(String status);
    
    /**
     * 根据任务名称查找任务
     * @param name 任务名称
     * @return 任务列表
     */
    List<Task> findByNameContaining(String name);
    
    /**
     * 查找所有未完成的任务（包括待处理和处理中的任务）
     * @return 未完成任务列表
     */
    List<Task> findByStatusIn(List<String> statusList);
    
    /**
     * 根据名称和状态查找任务
     * @param name 任务名称
     * @param status 任务状态
     * @return 任务列表
     */
    List<Task> findByNameAndStatus(String name, String status);

    /**
     * 根据状态查找任务，按优先级和创建时间排序
     * @param status 任务状态
     * @return 按优先级排序的任务列表（优先级数字越小越靠前）
     */
    List<Task> findByStatusOrderByPriorityAscCreatedAtAsc(String status);

    /**
     * 根据状态列表查找任务，按优先级和创建时间排序
     * @param statusList 任务状态列表
     * @return 按优先级排序的任务列表
     */
    List<Task> findByStatusInOrderByPriorityAscCreatedAtAsc(List<String> statusList);

    /**
     * 查找指定优先级范围内的待处理任务
     * @param minPriority 最小优先级（数字越小优先级越高）
     * @param maxPriority 最大优先级
     * @return 任务列表
     */
    List<Task> findByStatusAndPriorityBetweenOrderByPriorityAscCreatedAtAsc(
        String status, Integer minPriority, Integer maxPriority);

    // 定时任务相关查询方法

    /**
     * 根据定时任务启用状态查找任务
     * @param scheduleEnabled 是否启用定时任务
     * @return 任务列表
     */
    @Query("SELECT t FROM Task t WHERE t.scheduleEnabled = :scheduleEnabled")
    List<Task> findByScheduleEnabled(@Param("scheduleEnabled") Boolean scheduleEnabled);

    /**
     * 根据定时任务启用状态和调度状态查找任务
     * @param scheduleEnabled 是否启用定时任务
     * @param scheduleStatus 调度状态
     * @return 任务列表
     */
    @Query("SELECT t FROM Task t WHERE t.scheduleEnabled = :scheduleEnabled AND t.scheduleStatus = :scheduleStatus")
    List<Task> findByScheduleEnabledAndScheduleStatus(@Param("scheduleEnabled") Boolean scheduleEnabled, @Param("scheduleStatus") String scheduleStatus);

    /**
     * 统计启用定时任务的数量
     * @param scheduleEnabled 是否启用定时任务
     * @return 任务数量
     */
    @Query("SELECT COUNT(t) FROM Task t WHERE t.scheduleEnabled = :scheduleEnabled")
    long countByScheduleEnabled(@Param("scheduleEnabled") Boolean scheduleEnabled);

    /**
     * 统计指定状态的定时任务数量
     * @param scheduleEnabled 是否启用定时任务
     * @param scheduleStatus 调度状态
     * @return 任务数量
     */
    @Query("SELECT COUNT(t) FROM Task t WHERE t.scheduleEnabled = :scheduleEnabled AND t.scheduleStatus = :scheduleStatus")
    long countByScheduleEnabledAndScheduleStatus(@Param("scheduleEnabled") Boolean scheduleEnabled, @Param("scheduleStatus") String scheduleStatus);

    /**
     * 根据定时执行时间查找任务
     * @param scheduleTime 定时执行时间
     * @return 任务列表
     */
    @Query("SELECT t FROM Task t WHERE t.scheduleTime = :scheduleTime")
    List<Task> findByScheduleTime(@Param("scheduleTime") String scheduleTime);

    /**
     * 查找所有启用定时功能的活跃任务，按执行时间排序
     * @param scheduleEnabled 是否启用定时任务
     * @param scheduleStatus 调度状态
     * @return 按执行时间排序的任务列表
     */
    @Query("SELECT t FROM Task t WHERE t.scheduleEnabled = :scheduleEnabled AND t.scheduleStatus = :scheduleStatus ORDER BY t.scheduleTime ASC")
    List<Task> findByScheduleEnabledAndScheduleStatusOrderByScheduleTimeAsc(@Param("scheduleEnabled") Boolean scheduleEnabled, @Param("scheduleStatus") String scheduleStatus);
}
