package com.z3rd0.system.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.concurrent.TimeUnit;

/**
 * 天眼查工具类
 * 用于天眼查API的数据采集和处理
 *
 * <AUTHOR>
 * @version 2.0
 *
 * 主要功能：
 * 1. 公司投资信息查询
 * 2. 供应商信息搜索
 * 3. 公司名称清理和过滤
 * 4. 排除名单管理
 */
@Component
public class TianYanChaUtils {

    private static final Logger logger = LoggerFactory.getLogger(TianYanChaUtils.class);

    // 常量定义
    private static final int MAX_PAGES = 999;
    private static final int PAGE_SIZE = 10;
    private static final int REQUEST_DELAY_MS = 500;
    private static final int TOKEN_EXPIRED_CODE = 302004;
    private static final double MIN_PERCENT_THRESHOLD = 40.0;

    // API配置 - 从配置文件读取
    @Value("${tianyancha.token:}")
    private String token;

    @Value("${tianyancha.tycId:}")
    private String tycId;

    @Value("${tianyancha.version:TYC-Web}")
    private String version;

    // 排除名单配置
    @Value("#{'${companyName}'.split(',')}")
    private String[] companyNames;

    @Value("#{'${searchIcpName}'.split(',')}")
    private String[] searchIcpName;

    // 缓存的正则表达式
    private Pattern excludePattern;

    // HTTP客户端
    private final RestTemplate restTemplate;

    // JSON处理器
    private final ObjectMapper objectMapper;

    public TianYanChaUtils() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 搜索公司投资信息
     * @param companyGId 公司GID
     * @return 投资公司列表
     */
    public List<Map<String, Object>> searchCompanyInvestments(String companyGId) {
        List<Map<String, Object>> companies = new ArrayList<>();

        if (companyGId == null || companyGId.trim().isEmpty()) {
            logger.warn("公司GID为空，无法搜索投资信息");
            return companies;
        }

        try {
            for (int i = 0; i < MAX_PAGES; i++) {
                // 请求延迟
                TimeUnit.MILLISECONDS.sleep(REQUEST_DELAY_MS);

                String pageNum = String.valueOf(i + 1);
                String url = "https://capi.tianyancha.com/cloud-company-background/company/investListV2?_=" + companyGId;

                // 构建请求数据
                Map<String, Object> requestData = new HashMap<>();
                requestData.put("category", "-100");
                requestData.put("fullSearchText", "");
                requestData.put("percentLevel", "-100");
                requestData.put("province", "-100");
                requestData.put("registation", "-100");
                requestData.put("gid", companyGId);
                requestData.put("pageNum", pageNum);
                requestData.put("pageSize", PAGE_SIZE);

                // 构建请求头
                HttpHeaders headers = createHeaders();
                HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestData, headers);

                try {
                    ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

                    if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                        Map<String, Object> responseData = objectMapper.readValue(response.getBody(),
                                new TypeReference<Map<String, Object>>() {});

                        // 检查响应数据
                        if (!isValidResponse(responseData)) {
                            logger.warn("无效的响应数据，停止搜索");
                            break;
                        }

                        List<Map<String, Object>> results = extractInvestmentResults(responseData);
                        if (results.isEmpty()) {
                            logger.info("第{}页无数据，搜索完成", pageNum);
                            break;
                        }

                        // 过滤和处理结果
                        for (Map<String, Object> result : results) {
                            if (isValidInvestment(result)) {
                                companies.add(result);
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.error("搜索公司投资信息失败，页码：{}，错误：{}", pageNum, e.getMessage(), e);
                    break;
                }
            }
        } catch (InterruptedException e) {
            logger.error("搜索被中断", e);
            Thread.currentThread().interrupt();
        }

        logger.info("搜索完成，共找到{}家投资公司", companies.size());
        return companies;
    }

    /**
     * 搜索供应商信息
     * @param companyGId 公司GID
     * @return 搜索结果状态
     */
    public String searchSuppliers(String companyGId) {
        if (companyGId == null || companyGId.trim().isEmpty()) {
            logger.warn("公司GID为空，无法搜索供应商信息");
            return "error";
        }

        List<Map<String, Object>> suppliers = new ArrayList<>();

        try {
            for (int i = 0; i < MAX_PAGES; i++) {
                TimeUnit.MILLISECONDS.sleep(REQUEST_DELAY_MS);

                String pageNum = String.valueOf(i + 1);
                String url = String.format("http://capi.tianyancha.com/cloud-business-state/supply/summaryList?gid=%s&pageSize=%d&pageNum=%s&year=-100",
                                         companyGId, PAGE_SIZE, pageNum);

                HttpHeaders headers = createHeaders();
                HttpEntity<String> entity = new HttpEntity<>(headers);

                try {
                    ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

                    if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                        Map<String, Object> responseData = objectMapper.readValue(response.getBody(),
                                new TypeReference<Map<String, Object>>() {});

                        // 检查Token是否过期
                        if (isTokenExpired(responseData)) {
                            logger.error("Token失效！请重新获取token！");
                            return "error";
                        }

                        List<Map<String, Object>> results = extractSupplierResults(responseData);
                        if (results.isEmpty()) {
                            logger.info("第{}页无数据，查询完毕！", pageNum);
                            break;
                        }

                        // 处理供应商数据
                        for (Map<String, Object> result : results) {
                            String supplierName = (String) result.get("supplier_name");
                            if (supplierName != null) {
                                // 供应链公司根据关键字排除
                                if (isExcludedCompany(supplierName)) {
                                    continue;
                                }

                                // 处理包含特定字符串的公司名称
                                supplierName = cleanCompanyName(supplierName);

                                // 创建供应商信息
                                Map<String, Object> supplier = new HashMap<>();
                                supplier.put("name", supplierName);
                                supplier.put("originalName", result.get("supplier_name"));
                                supplier.put("companyGId", companyGId);
                                suppliers.add(supplier);

                                logger.debug("找到供应商：{}", supplierName);
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.error("搜索供应商信息失败，页码：{}，错误：{}", pageNum, e.getMessage(), e);
                    break;
                }
            }
        } catch (InterruptedException e) {
            logger.error("搜索被中断", e);
            Thread.currentThread().interrupt();
            return "error";
        }

        logger.info("搜索完成，共找到{}家供应商", suppliers.size());
        return "success";
    }

    /**
     * 创建HTTP请求头
     * @return HTTP请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("X-Auth-Token", token);
        headers.set("X-Tycid", tycId);
        headers.set("Version", version);
        return headers;
    }

    /**
     * 验证响应数据是否有效
     * @param responseData 响应数据
     * @return 是否有效
     */
    private boolean isValidResponse(Map<String, Object> responseData) {
        if (responseData == null) {
            return false;
        }

        // 检查是否有错误码
        Object errorCode = responseData.get("errorCode");
        if (errorCode != null && TOKEN_EXPIRED_CODE == ((Number) errorCode).intValue()) {
            logger.error("Token失效！请重新获取token！");
            return false;
        }

        return responseData.get("data") != null;
    }

    /**
     * 检查Token是否过期
     * @param responseData 响应数据
     * @return 是否过期
     */
    private boolean isTokenExpired(Map<String, Object> responseData) {
        if (responseData == null) {
            return false;
        }

        Object errorCode = responseData.get("errorCode");
        return errorCode != null && TOKEN_EXPIRED_CODE == ((Number) errorCode).intValue();
    }

    /**
     * 从响应数据中提取投资结果
     * @param responseData 响应数据
     * @return 投资结果列表
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> extractInvestmentResults(Map<String, Object> responseData) {
        try {
            Map<String, Object> data = (Map<String, Object>) responseData.get("data");
            if (data != null) {
                List<Map<String, Object>> result = (List<Map<String, Object>>) data.get("result");
                return result != null ? result : new ArrayList<>();
            }
        } catch (Exception e) {
            logger.error("提取投资结果失败：{}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    /**
     * 从响应数据中提取供应商结果
     * @param responseData 响应数据
     * @return 供应商结果列表
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> extractSupplierResults(Map<String, Object> responseData) {
        try {
            Map<String, Object> data = (Map<String, Object>) responseData.get("data");
            if (data != null) {
                Map<String, Object> pageBean = (Map<String, Object>) data.get("pageBean");
                if (pageBean != null) {
                    List<Map<String, Object>> result = (List<Map<String, Object>>) pageBean.get("result");
                    return result != null ? result : new ArrayList<>();
                }
            }
        } catch (Exception e) {
            logger.error("提取供应商结果失败：{}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    /**
     * 验证投资信息是否有效
     * @param investment 投资信息
     * @return 是否有效
     */
    private boolean isValidInvestment(Map<String, Object> investment) {
        if (investment == null) {
            return false;
        }

        // 检查注册状态
        String regStatus = (String) investment.get("regStatus");
        if (!"存续".equals(regStatus)) {
            return false;
        }

        // 检查持股比例
        String percentStr = (String) investment.get("percent");
        if (percentStr == null || percentStr.contains("-")) {
            return false;
        }

        try {
            double percent = Double.parseDouble(percentStr.replace("%", ""));
            return percent > MIN_PERCENT_THRESHOLD;
        } catch (NumberFormatException e) {
            logger.warn("无法解析持股比例：{}", percentStr);
            return false;
        }
    }

    /**
     * 判断公司是否在排除名单中
     * @param companyName 公司名称
     * @return 是否被排除
     */
    private boolean isExcludedCompany(String companyName) {
        if (companyName == null || companyName.isEmpty()) {
            return true;
        }

        Pattern pattern = getExcludePattern();
        if (pattern.matcher(companyName).find()) {
            logger.debug("排除！公司存在于排除名单中：{}", companyName);
            return true;
        }
        return false;
    }

    /**
     * 清理公司名称中的特殊字符
     * @param companyName 原始公司名称
     * @return 清理后的公司名称
     */
    private String cleanCompanyName(String companyName) {
        if (companyName == null) {
            return "";
        }

        Pattern cleanPattern = Pattern.compile("001包|01包01|01包1|01包|01标包|1标包1|1标包|等公司|等|1ZWKHA202211296包1|\\(普通合伙\\)|\\(原常熟开关厂\\)");
        Matcher matcher = cleanPattern.matcher(companyName);
        return matcher.replaceAll("").trim();
    }

    /**
     * 获取排除名单的正则表达式
     * @return 正则表达式
     */
    private Pattern getExcludePattern() {
        if (excludePattern == null) {
            // 合并两个数组
            List<String> nameList = new ArrayList<>();
            if (companyNames != null) {
                nameList.addAll(Arrays.asList(companyNames));
            }
            if (searchIcpName != null) {
                nameList.addAll(Arrays.asList(searchIcpName));
            }

            String regex = String.join("|", nameList);
            excludePattern = Pattern.compile(regex);
        }
        return excludePattern;
    }
}
