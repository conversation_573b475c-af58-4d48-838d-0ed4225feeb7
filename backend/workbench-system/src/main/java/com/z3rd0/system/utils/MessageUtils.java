package com.z3rd0.system.utils;

import com.z3rd0.system.constants.RabbitMQConstants;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 消息工具类
 * 提供RabbitMQ消息创建和处理的通用方法
 * 统一消息格式，减少重复代码
 */
public final class MessageUtils {

    /**
     * 私有构造函数，防止实例化
     */
    private MessageUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * 创建任务消息
     * 
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param rule 搜索规则
     * @param timeRange 时间范围
     * @return RabbitMQ消息对象
     */
    public static Message createTaskMessage(Long taskId, String taskName, String rule, String timeRange) {
        return createTaskMessage(taskId, taskName, rule, timeRange, 0);
    }

    /**
     * 创建任务消息（带重试次数）
     *
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param rule 搜索规则
     * @param timeRange 时间范围
     * @param retryCount 重试次数
     * @return RabbitMQ消息对象
     */
    public static Message createTaskMessage(Long taskId, String taskName, String rule, String timeRange, int retryCount) {
        return createTaskMessage(taskId, taskName, rule, timeRange, retryCount, 5);
    }

    /**
     * 创建任务消息（带重试次数和优先级）
     *
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param rule 搜索规则
     * @param timeRange 时间范围
     * @param retryCount 重试次数
     * @param priority 任务优先级
     * @return RabbitMQ消息对象
     */
    public static Message createTaskMessage(Long taskId, String taskName, String rule, String timeRange, int retryCount, int priority) {
        // 准备消息头
        Map<String, Object> headers = new HashMap<>();
        headers.put(RabbitMQConstants.HEADER_TASK_ID, taskId);
        headers.put(RabbitMQConstants.HEADER_TASK_NAME, taskName);
        headers.put(RabbitMQConstants.HEADER_TASK_RULE, rule);
        headers.put(RabbitMQConstants.HEADER_TIME_RANGE, timeRange);
        headers.put(RabbitMQConstants.HEADER_CREATED_AT, LocalDateTime.now().toString());
        headers.put(RabbitMQConstants.HEADER_RETRY_COUNT, retryCount);

        // 创建消息属性
        MessageProperties properties = new MessageProperties();
        properties.setContentType(RabbitMQConstants.MESSAGE_CONTENT_TYPE);
        properties.setHeaders(headers);
        // 设置消息优先级
        properties.setPriority(priority);

        // 创建JSON格式的消息体
        String messageBody = createTaskMessageBody(taskId, taskName, rule, timeRange);

        // 创建并返回消息
        return new Message(messageBody.getBytes(StandardCharsets.UTF_8), properties);
    }

    /**
     * 创建任务更新消息
     * 
     * @param action 更新动作
     * @param taskId 任务ID
     * @param filterRange 筛选范围
     * @return RabbitMQ消息对象
     */
    public static Message createTaskUpdateMessage(String action, Long taskId, String filterRange) {
        // 准备消息头
        Map<String, Object> headers = new HashMap<>();
        headers.put("action", action);
        headers.put(RabbitMQConstants.HEADER_TASK_ID, taskId);
        headers.put("filterRange", filterRange);
        headers.put(RabbitMQConstants.HEADER_CREATED_AT, LocalDateTime.now().toString());

        // 创建消息属性
        MessageProperties properties = new MessageProperties();
        properties.setContentType(RabbitMQConstants.MESSAGE_CONTENT_TYPE);
        properties.setHeaders(headers);

        // 创建JSON格式的消息体
        String messageBody = createTaskUpdateMessageBody(action, taskId, filterRange);

        // 创建并返回消息
        return new Message(messageBody.getBytes(StandardCharsets.UTF_8), properties);
    }

    /**
     * 创建任务消息体（JSON格式）
     * 
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param rule 搜索规则
     * @param timeRange 时间范围
     * @return JSON格式的消息体
     */
    private static String createTaskMessageBody(Long taskId, String taskName, String rule, String timeRange) {
        // 转义特殊字符
        String escapedName = escapeJsonString(taskName);
        String escapedRule = escapeJsonString(rule);
        String escapedTimeRange = escapeJsonString(timeRange);

        return String.format(
            "{\"taskId\":%d,\"name\":\"%s\",\"rule\":\"%s\",\"timeRange\":\"%s\"}",
            taskId, escapedName, escapedRule, escapedTimeRange
        );
    }

    /**
     * 创建任务更新消息体（JSON格式）
     * 
     * @param action 更新动作
     * @param taskId 任务ID
     * @param filterRange 筛选范围
     * @return JSON格式的消息体
     */
    private static String createTaskUpdateMessageBody(String action, Long taskId, String filterRange) {
        String escapedAction = escapeJsonString(action);
        String escapedFilterRange = escapeJsonString(filterRange);

        return String.format(
            "{\"action\":\"%s\",\"taskId\":%d,\"filterRange\":\"%s\"}",
            escapedAction, taskId, escapedFilterRange
        );
    }

    /**
     * 转义JSON字符串中的特殊字符
     * 
     * @param input 输入字符串
     * @return 转义后的字符串
     */
    private static String escapeJsonString(String input) {
        if (input == null) {
            return "";
        }
        return input.replace("\\", "\\\\")
                   .replace("\"", "\\\"")
                   .replace("\n", "\\n")
                   .replace("\r", "\\r")
                   .replace("\t", "\\t");
    }

    /**
     * 从消息中提取任务ID
     * 
     * @param message RabbitMQ消息
     * @return 任务ID，如果不存在则返回null
     */
    public static Long extractTaskId(Message message) {
        Object taskIdObj = message.getMessageProperties().getHeaders().get(RabbitMQConstants.HEADER_TASK_ID);
        if (taskIdObj instanceof Number) {
            return ((Number) taskIdObj).longValue();
        }
        return null;
    }

    /**
     * 从消息中提取重试次数
     * 
     * @param message RabbitMQ消息
     * @return 重试次数，如果不存在则返回0
     */
    public static int extractRetryCount(Message message) {
        Object retryCountObj = message.getMessageProperties().getHeaders().get(RabbitMQConstants.HEADER_RETRY_COUNT);
        if (retryCountObj instanceof Number) {
            return ((Number) retryCountObj).intValue();
        }
        return 0;
    }

    /**
     * 检查是否超过最大重试次数
     * 
     * @param message RabbitMQ消息
     * @return 如果超过最大重试次数返回true，否则返回false
     */
    public static boolean isMaxRetryExceeded(Message message) {
        int retryCount = extractRetryCount(message);
        return retryCount >= RabbitMQConstants.DEFAULT_RETRY_COUNT;
    }
}
