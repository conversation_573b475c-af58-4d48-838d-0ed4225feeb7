package com.z3rd0.system.exception;

/**
 * 任务相关异常
 * 用于处理任务管理中的异常情况
 */
public class TaskException extends BusinessException {

    /**
     * 任务不存在异常
     */
    public static class TaskNotFoundException extends TaskException {
        public TaskNotFoundException(Long taskId) {
            super("任务不存在: " + taskId, "TASK_NOT_FOUND", taskId);
        }
    }

    /**
     * 任务状态异常
     */
    public static class InvalidTaskStatusException extends TaskException {
        public InvalidTaskStatusException(String currentStatus, String expectedStatus) {
            super(String.format("任务状态错误，当前状态: %s，期望状态: %s", currentStatus, expectedStatus), 
                  "INVALID_TASK_STATUS", 
                  new TaskStatusInfo(currentStatus, expectedStatus));
        }
    }

    /**
     * 任务参数异常
     */
    public static class InvalidTaskParameterException extends TaskException {
        public InvalidTaskParameterException(String parameterName, String reason) {
            super(String.format("任务参数错误: %s - %s", parameterName, reason), 
                  "INVALID_TASK_PARAMETER", 
                  new ParameterInfo(parameterName, reason));
        }
    }

    /**
     * 任务执行异常
     */
    public static class TaskExecutionException extends TaskException {
        public TaskExecutionException(Long taskId, String reason, Throwable cause) {
            super(String.format("任务执行失败: %s", reason), 
                  "TASK_EXECUTION_FAILED", 
                  taskId, 
                  cause);
        }
    }

    /**
     * 任务重复异常
     */
    public static class DuplicateTaskException extends TaskException {
        public DuplicateTaskException(String taskName) {
            super("任务名称已存在: " + taskName, "DUPLICATE_TASK_NAME", taskName);
        }
    }

    /**
     * 基础构造函数
     */
    public TaskException(String message) {
        super(message, "TASK_ERROR");
    }

    /**
     * 带错误代码的构造函数
     */
    public TaskException(String message, String errorCode) {
        super(message, errorCode);
    }

    /**
     * 带详细信息的构造函数
     */
    public TaskException(String message, String errorCode, Object details) {
        super(message, errorCode, details);
    }

    /**
     * 带原因异常的构造函数
     */
    public TaskException(String message, String errorCode, Object details, Throwable cause) {
        super(message, errorCode, details, cause);
    }

    /**
     * 任务状态信息
     */
    public static class TaskStatusInfo {
        private final String currentStatus;
        private final String expectedStatus;

        public TaskStatusInfo(String currentStatus, String expectedStatus) {
            this.currentStatus = currentStatus;
            this.expectedStatus = expectedStatus;
        }

        public String getCurrentStatus() { return currentStatus; }
        public String getExpectedStatus() { return expectedStatus; }
    }

    /**
     * 参数信息
     */
    public static class ParameterInfo {
        private final String parameterName;
        private final String reason;

        public ParameterInfo(String parameterName, String reason) {
            this.parameterName = parameterName;
            this.reason = reason;
        }

        public String getParameterName() { return parameterName; }
        public String getReason() { return reason; }
    }
}
