package com.z3rd0.system.controller;

import com.z3rd0.system.service.TaskService;
import com.z3rd0.system.service.TaskStatusPushService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.annotation.SubscribeMapping;
import org.springframework.stereotype.Controller;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket控制器
 * 处理WebSocket消息和订阅请求
 */
@Slf4j
@Controller
@RequiredArgsConstructor
public class WebSocketController {
    
    private final TaskService taskService;
    private final TaskStatusPushService taskStatusPushService;
    
    /**
     * 处理客户端订阅任务状态的请求
     * 
     * @return 当前所有任务的状态概览
     */
    @SubscribeMapping("/topic/task/status")
    public Map<String, Object> subscribeTaskStatus() {
        try {
            log.info("客户端订阅任务状态推送");
            
            Map<String, Object> response = new HashMap<>();
            response.put("type", "SUBSCRIPTION_CONFIRMED");
            response.put("topic", "task/status");
            response.put("timestamp", LocalDateTime.now().toString());
            response.put("message", "任务状态订阅成功");
            
            // 可以在这里返回当前所有任务的状态概览
            response.put("taskCount", taskService.findAll().size());
            
            return response;
        } catch (Exception e) {
            log.error("处理任务状态订阅失败: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("type", "SUBSCRIPTION_ERROR");
            errorResponse.put("error", e.getMessage());
            return errorResponse;
        }
    }
    
    /**
     * 处理客户端订阅特定任务状态的请求
     * 
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    @SubscribeMapping("/topic/task/{taskId}/status")
    public Map<String, Object> subscribeSpecificTaskStatus(@DestinationVariable Long taskId) {
        try {
            log.info("客户端订阅任务状态推送: 任务ID={}", taskId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("type", "SUBSCRIPTION_CONFIRMED");
            response.put("topic", "task/" + taskId + "/status");
            response.put("taskId", taskId);
            response.put("timestamp", LocalDateTime.now().toString());
            
            // 返回当前任务状态
            taskService.findById(taskId).ifPresentOrElse(
                task -> {
                    response.put("currentStatus", task.getStatus());
                    response.put("taskName", task.getName());
                    response.put("message", "任务状态订阅成功");
                },
                () -> {
                    response.put("error", "任务不存在");
                    response.put("message", "任务状态订阅失败");
                }
            );
            
            return response;
        } catch (Exception e) {
            log.error("处理特定任务状态订阅失败: 任务ID={}, 错误={}", taskId, e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("type", "SUBSCRIPTION_ERROR");
            errorResponse.put("taskId", taskId);
            errorResponse.put("error", e.getMessage());
            return errorResponse;
        }
    }
    
    /**
     * 处理客户端订阅任务进度的请求
     * 
     * @return 进度订阅确认信息
     */
    @SubscribeMapping("/topic/task/progress")
    public Map<String, Object> subscribeTaskProgress() {
        try {
            log.info("客户端订阅任务进度推送");
            
            Map<String, Object> response = new HashMap<>();
            response.put("type", "SUBSCRIPTION_CONFIRMED");
            response.put("topic", "task/progress");
            response.put("timestamp", LocalDateTime.now().toString());
            response.put("message", "任务进度订阅成功");
            
            return response;
        } catch (Exception e) {
            log.error("处理任务进度订阅失败: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("type", "SUBSCRIPTION_ERROR");
            errorResponse.put("error", e.getMessage());
            return errorResponse;
        }
    }
    
    /**
     * 处理客户端订阅特定任务进度的请求
     * 
     * @param taskId 任务ID
     * @return 任务进度信息
     */
    @SubscribeMapping("/topic/task/{taskId}/progress")
    public Map<String, Object> subscribeSpecificTaskProgress(@DestinationVariable Long taskId) {
        try {
            log.info("客户端订阅任务进度推送: 任务ID={}", taskId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("type", "SUBSCRIPTION_CONFIRMED");
            response.put("topic", "task/" + taskId + "/progress");
            response.put("taskId", taskId);
            response.put("timestamp", LocalDateTime.now().toString());
            
            // 返回当前任务进度
            TaskStatusPushService.TaskProgress progress = taskStatusPushService.getTaskProgress(taskId);
            if (progress != null) {
                response.put("currentProgress", progress);
                response.put("message", "任务进度订阅成功");
            } else {
                response.put("message", "任务进度订阅成功，暂无进度信息");
            }
            
            return response;
        } catch (Exception e) {
            log.error("处理特定任务进度订阅失败: 任务ID={}, 错误={}", taskId, e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("type", "SUBSCRIPTION_ERROR");
            errorResponse.put("taskId", taskId);
            errorResponse.put("error", e.getMessage());
            return errorResponse;
        }
    }
    
    /**
     * 处理客户端订阅系统通知的请求
     * 
     * @return 系统通知订阅确认信息
     */
    @SubscribeMapping("/topic/system/notifications")
    public Map<String, Object> subscribeSystemNotifications() {
        try {
            log.info("客户端订阅系统通知推送");
            
            Map<String, Object> response = new HashMap<>();
            response.put("type", "SUBSCRIPTION_CONFIRMED");
            response.put("topic", "system/notifications");
            response.put("timestamp", LocalDateTime.now().toString());
            response.put("message", "系统通知订阅成功");
            
            return response;
        } catch (Exception e) {
            log.error("处理系统通知订阅失败: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("type", "SUBSCRIPTION_ERROR");
            errorResponse.put("error", e.getMessage());
            return errorResponse;
        }
    }
    
    /**
     * 处理客户端订阅资源监控的请求
     * 
     * @return 资源监控订阅确认信息
     */
    @SubscribeMapping("/topic/system/resources")
    public Map<String, Object> subscribeResourceMonitor() {
        try {
            log.info("客户端订阅资源监控推送");
            
            Map<String, Object> response = new HashMap<>();
            response.put("type", "SUBSCRIPTION_CONFIRMED");
            response.put("topic", "system/resources");
            response.put("timestamp", LocalDateTime.now().toString());
            response.put("message", "资源监控订阅成功");
            
            return response;
        } catch (Exception e) {
            log.error("处理资源监控订阅失败: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("type", "SUBSCRIPTION_ERROR");
            errorResponse.put("error", e.getMessage());
            return errorResponse;
        }
    }
    
    /**
     * 处理客户端发送的心跳消息
     * 
     * @param message 心跳消息
     * @return 心跳响应
     */
    @MessageMapping("/heartbeat")
    @SendTo("/topic/heartbeat")
    public Map<String, Object> handleHeartbeat(Map<String, Object> message) {
        Map<String, Object> response = new HashMap<>();
        response.put("type", "HEARTBEAT_RESPONSE");
        response.put("timestamp", LocalDateTime.now().toString());
        response.put("clientTimestamp", message.get("timestamp"));
        return response;
    }
}
