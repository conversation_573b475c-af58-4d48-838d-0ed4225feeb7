package com.z3rd0.system.service;

import com.z3rd0.common.model.Task;
import com.z3rd0.system.dto.BatchOperationResult;
import com.z3rd0.system.dto.BatchTaskCreateRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 批量任务操作服务
 * 提供高效的批量任务处理能力，支持并发执行和事务管理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BatchTaskService {
    
    private final TaskService taskService;
    private final TaskPublisher taskPublisher;
    
    // 批量操作线程池
    private final ExecutorService batchExecutor = Executors.newFixedThreadPool(5);
    
    /**
     * 批量启动任务（并发执行）
     * 
     * @param taskIds 任务ID列表
     * @param timeRange 时间范围
     * @param forceStart 是否强制启动
     * @return 批量操作结果
     */
    public BatchOperationResult batchStartTasks(List<Long> taskIds, String timeRange, boolean forceStart) {
        log.info("开始批量启动任务: 任务数量={}, 时间范围={}, 强制启动={}", taskIds.size(), timeRange, forceStart);
        
        // 查找所有任务
        List<Task> tasks = taskService.findByIds(taskIds);
        
        // 创建并发任务列表
        List<CompletableFuture<BatchOperationResult.TaskResult>> futures = tasks.stream()
            .map(task -> CompletableFuture.supplyAsync(() -> startSingleTask(task, timeRange, forceStart), batchExecutor))
            .collect(Collectors.toList());
        
        // 等待所有任务完成
        List<BatchOperationResult.TaskResult> results = futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toList());
        
        // 统计结果
        int successCount = (int) results.stream().filter(BatchOperationResult.TaskResult::isSuccess).count();
        int failureCount = results.size() - successCount;
        
        log.info("批量启动任务完成: 成功={}, 失败={}", successCount, failureCount);
        return new BatchOperationResult("BATCH_START", successCount, failureCount, results);
    }
    
    /**
     * 启动单个任务
     */
    private BatchOperationResult.TaskResult startSingleTask(Task task, String timeRange, boolean forceStart) {
        try {
            // 检查任务状态
            if (!forceStart && "PROCESSING".equals(task.getStatus())) {
                return BatchOperationResult.TaskResult.failure(
                    task.getId(), task.getName(), "任务正在执行中，无法启动");
            }
            
            // 发布任务到队列
            taskPublisher.publishSearchTaskWithId(task.getId(), task.getName(), task.getRule(), timeRange);
            
            return BatchOperationResult.TaskResult.success(
                task.getId(), task.getName(), "任务已成功提交到队列");
                
        } catch (Exception e) {
            log.error("启动任务失败: 任务ID={}, 错误={}", task.getId(), e.getMessage(), e);
            return BatchOperationResult.TaskResult.failure(
                task.getId(), task.getName(), e.getMessage());
        }
    }
    
    /**
     * 批量删除任务（事务性操作）
     * 
     * @param taskIds 任务ID列表
     * @param forceDelete 是否强制删除
     * @return 批量操作结果
     */
    @Transactional
    public BatchOperationResult batchDeleteTasks(List<Long> taskIds, boolean forceDelete) {
        log.info("开始批量删除任务: 任务数量={}, 强制删除={}", taskIds.size(), forceDelete);
        
        List<BatchOperationResult.TaskResult> results = new ArrayList<>();
        List<Long> toDeleteIds = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;
        
        // 查找所有任务并验证
        List<Task> tasks = taskService.findByIds(taskIds);
        
        for (Task task : tasks) {
            try {
                // 检查任务状态
                if (!forceDelete && "PROCESSING".equals(task.getStatus())) {
                    results.add(BatchOperationResult.TaskResult.failure(
                        task.getId(), task.getName(), "任务正在执行中，无法删除"));
                    failureCount++;
                    continue;
                }
                
                toDeleteIds.add(task.getId());
                results.add(BatchOperationResult.TaskResult.success(
                    task.getId(), task.getName(), "任务删除成功"));
                successCount++;
                
            } catch (Exception e) {
                log.error("验证删除任务失败: 任务ID={}, 错误={}", task.getId(), e.getMessage(), e);
                results.add(BatchOperationResult.TaskResult.failure(
                    task.getId(), task.getName(), e.getMessage()));
                failureCount++;
            }
        }
        
        // 批量删除
        if (!toDeleteIds.isEmpty()) {
            try {
                taskService.deleteByIds(toDeleteIds);
                log.info("批量删除任务成功: 删除数量={}", toDeleteIds.size());
            } catch (Exception e) {
                log.error("批量删除任务失败: {}", e.getMessage(), e);
                // 如果批量删除失败，更新所有成功的结果为失败
                results.stream()
                    .filter(BatchOperationResult.TaskResult::isSuccess)
                    .forEach(result -> {
                        result.setSuccess(false);
                        result.setMessage("批量删除操作失败: " + e.getMessage());
                    });
                successCount = 0;
                failureCount = results.size();
            }
        }
        
        log.info("批量删除任务完成: 成功={}, 失败={}", successCount, failureCount);
        return new BatchOperationResult("BATCH_DELETE", successCount, failureCount, results);
    }
    
    /**
     * 批量更新任务状态（事务性操作）
     * 
     * @param taskIds 任务ID列表
     * @param status 目标状态
     * @param errorMessage 错误信息
     * @return 批量操作结果
     */
    @Transactional
    public BatchOperationResult batchUpdateTaskStatus(List<Long> taskIds, String status, String errorMessage) {
        log.info("开始批量更新任务状态: 任务数量={}, 目标状态={}", taskIds.size(), status);
        
        List<BatchOperationResult.TaskResult> results = new ArrayList<>();
        List<Task> toUpdateTasks = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;
        
        // 查找所有任务
        List<Task> tasks = taskService.findByIds(taskIds);
        
        for (Task task : tasks) {
            try {
                // 更新任务状态
                task.setStatus(status);
                if (errorMessage != null) {
                    task.setErrorMessage(errorMessage);
                }
                
                toUpdateTasks.add(task);
                results.add(BatchOperationResult.TaskResult.success(
                    task.getId(), task.getName(), "状态更新成功"));
                successCount++;
                
            } catch (Exception e) {
                log.error("准备更新任务状态失败: 任务ID={}, 错误={}", task.getId(), e.getMessage(), e);
                results.add(BatchOperationResult.TaskResult.failure(
                    task.getId(), task.getName(), e.getMessage()));
                failureCount++;
            }
        }
        
        // 批量保存
        if (!toUpdateTasks.isEmpty()) {
            try {
                taskService.saveAll(toUpdateTasks);
                log.info("批量更新任务状态成功: 更新数量={}", toUpdateTasks.size());
            } catch (Exception e) {
                log.error("批量更新任务状态失败: {}", e.getMessage(), e);
                // 如果批量保存失败，更新所有成功的结果为失败
                results.stream()
                    .filter(BatchOperationResult.TaskResult::isSuccess)
                    .forEach(result -> {
                        result.setSuccess(false);
                        result.setMessage("批量更新操作失败: " + e.getMessage());
                    });
                successCount = 0;
                failureCount = results.size();
            }
        }
        
        log.info("批量更新任务状态完成: 成功={}, 失败={}", successCount, failureCount);
        return new BatchOperationResult("BATCH_STATUS_UPDATE", successCount, failureCount, results);
    }
    
    /**
     * 批量创建任务（事务性操作）
     *
     * @param request 批量创建任务请求
     * @return 批量操作结果
     */
    @Transactional
    public BatchOperationResult batchCreateTasks(BatchTaskCreateRequest request) {
        log.info("开始批量创建任务: 关键字数量={}, 名称前缀={}",
                request.getKeywords().size(), request.getNamePrefix());

        List<BatchOperationResult.TaskResult> results = new ArrayList<>();
        List<Task> tasksToSave = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;

        // 获取查询模板
        String queryTemplate = request.getQueryTemplate();

        // 为每个关键字创建任务
        for (String keyword : request.getKeywords()) {
            try {
                // 验证关键字
                if (keyword == null || keyword.trim().isEmpty()) {
                    results.add(BatchOperationResult.TaskResult.failure(
                        null, keyword, "关键字不能为空"));
                    failureCount++;
                    continue;
                }

                String cleanKeyword = keyword.trim();

                // 生成任务名称
                String taskName = request.getNamePrefix() + cleanKeyword;

                // 生成查询规则（替换模板中的占位符）
                String taskRule = queryTemplate.replace("{关键字}", cleanKeyword);

                // 创建任务对象
                Task task = new Task();
                task.setName(taskName);
                task.setRule(taskRule);
                task.setStatus("PENDING");
                task.setRetryCount(0);
                task.setPriority(request.getPriority());

                tasksToSave.add(task);
                results.add(BatchOperationResult.TaskResult.success(
                    null, taskName, "任务创建准备成功"));
                successCount++;

            } catch (Exception e) {
                log.error("准备创建任务失败: 关键字={}, 错误={}", keyword, e.getMessage(), e);
                results.add(BatchOperationResult.TaskResult.failure(
                    null, keyword, e.getMessage()));
                failureCount++;
            }
        }

        // 批量保存任务
        if (!tasksToSave.isEmpty()) {
            try {
                List<Task> savedTasks = taskService.saveAll(tasksToSave);
                log.info("批量创建任务成功: 创建数量={}", savedTasks.size());

                // 更新结果中的任务ID
                for (int i = 0; i < savedTasks.size(); i++) {
                    Task savedTask = savedTasks.get(i);
                    // 找到对应的成功结果并更新ID
                    results.stream()
                        .filter(BatchOperationResult.TaskResult::isSuccess)
                        .skip(i)
                        .findFirst()
                        .ifPresent(result -> result.setTaskId(savedTask.getId()));
                }

            } catch (Exception e) {
                log.error("批量创建任务失败: {}", e.getMessage(), e);
                // 如果批量保存失败，更新所有成功的结果为失败
                results.stream()
                    .filter(BatchOperationResult.TaskResult::isSuccess)
                    .forEach(result -> {
                        result.setSuccess(false);
                        result.setMessage("批量创建操作失败: " + e.getMessage());
                    });
                successCount = 0;
                failureCount = results.size();
            }
        }

        log.info("批量创建任务完成: 成功={}, 失败={}", successCount, failureCount);
        return new BatchOperationResult("BATCH_CREATE", successCount, failureCount, results);
    }

    /**
     * 获取批量操作统计信息
     *
     * @param taskIds 任务ID列表
     * @return 统计信息
     */
    public BatchTaskStats getBatchTaskStats(List<Long> taskIds) {
        List<Task> tasks = taskService.findByIds(taskIds);
        
        long pendingCount = tasks.stream().filter(t -> "PENDING".equals(t.getStatus())).count();
        long processingCount = tasks.stream().filter(t -> "PROCESSING".equals(t.getStatus())).count();
        long completedCount = tasks.stream().filter(t -> "COMPLETED".equals(t.getStatus())).count();
        long failedCount = tasks.stream().filter(t -> "FAILED".equals(t.getStatus())).count();
        long skippedCount = tasks.stream().filter(t -> "SKIPPED".equals(t.getStatus())).count();
        
        return new BatchTaskStats(tasks.size(), pendingCount, processingCount, 
                                 completedCount, failedCount, skippedCount);
    }
    
    /**
     * 批量任务统计信息
     */
    public static class BatchTaskStats {
        private final int totalCount;
        private final long pendingCount;
        private final long processingCount;
        private final long completedCount;
        private final long failedCount;
        private final long skippedCount;
        
        public BatchTaskStats(int totalCount, long pendingCount, long processingCount, 
                             long completedCount, long failedCount, long skippedCount) {
            this.totalCount = totalCount;
            this.pendingCount = pendingCount;
            this.processingCount = processingCount;
            this.completedCount = completedCount;
            this.failedCount = failedCount;
            this.skippedCount = skippedCount;
        }
        
        // Getters
        public int getTotalCount() { return totalCount; }
        public long getPendingCount() { return pendingCount; }
        public long getProcessingCount() { return processingCount; }
        public long getCompletedCount() { return completedCount; }
        public long getFailedCount() { return failedCount; }
        public long getSkippedCount() { return skippedCount; }
    }
}
