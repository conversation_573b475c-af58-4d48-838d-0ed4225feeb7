package com.z3rd0.system.dto;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

/**
 * 定时任务配置请求DTO
 * 用于更新任务的定时执行配置
 */
@Data
public class ScheduleConfigRequest {
    
    /**
     * 是否启用定时执行
     */
    @NotNull(message = "定时任务启用状态不能为空")
    private Boolean scheduleEnabled;
    
    /**
     * 定时执行时间(HH:mm格式)
     * 例如: "09:00", "14:30"
     */
    @Pattern(regexp = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "定时执行时间格式错误，应为HH:mm格式")
    private String scheduleTime;
    
    /**
     * Cron表达式(可选)
     * 用于更复杂的调度需求
     */
    private String scheduleCron;
    
    /**
     * 时间窗口策略
     * SMART: 智能策略
     * FIXED: 固定策略
     * ROLLING: 滚动策略
     */
    @Pattern(regexp = "^(SMART|FIXED|ROLLING)$", message = "时间窗口策略必须为SMART、FIXED或ROLLING")
    private String timeWindowStrategy = "SMART";
    
    /**
     * 时间窗口配置JSON
     * 存储窗口大小、偏移等参数
     */
    private String timeWindowConfig;
    
    /**
     * 任务时间范围参数
     * 用于配合时间窗口策略使用
     */
    private String timeRange;
    
    /**
     * 验证定时配置的有效性
     * @return 验证结果
     */
    public boolean isValid() {
        // 如果启用定时任务，必须配置执行时间
        if (Boolean.TRUE.equals(scheduleEnabled)) {
            return scheduleTime != null && !scheduleTime.trim().isEmpty();
        }
        return true;
    }
    
    /**
     * 获取验证错误信息
     * @return 错误信息，如果验证通过则返回null
     */
    public String getValidationError() {
        if (Boolean.TRUE.equals(scheduleEnabled)) {
            if (scheduleTime == null || scheduleTime.trim().isEmpty()) {
                return "启用定时任务时必须配置执行时间";
            }
            
            // 验证时间格式
            if (!scheduleTime.matches("^([01]?[0-9]|2[0-3]):[0-5][0-9]$")) {
                return "定时执行时间格式错误，应为HH:mm格式";
            }
        }
        
        return null;
    }
}
