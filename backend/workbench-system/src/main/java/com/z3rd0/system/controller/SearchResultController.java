package com.z3rd0.system.controller;

import com.z3rd0.common.model.SearchResult;
import com.z3rd0.system.dto.ApiResponse;
import com.z3rd0.system.repository.SearchResultRepository;
import com.z3rd0.system.utils.ExceptionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 搜索结果管理控制器
 * 提供搜索结果的CRUD操作接口
 */
@Slf4j
@RestController
@RequestMapping("/api/search/results")
@RequiredArgsConstructor
public class SearchResultController {

    private final SearchResultRepository repository;

    /**
     * 创建搜索结果
     */
    @PostMapping
    public ApiResponse<SearchResult> create(@RequestBody SearchResult result) {
        // 参数验证
        ExceptionUtils.requireNonNull(result, "searchResult");

        SearchResult savedResult = repository.save(result);
        log.info("搜索结果创建成功: ID={}", savedResult.getId());

        return ApiResponse.success(savedResult, "搜索结果创建成功");
    }

    /**
     * 根据ID获取搜索结果
     */
    @GetMapping("/{id}")
    public ApiResponse<SearchResult> get(@PathVariable Long id) {
        SearchResult result = repository.findById(id)
                .orElseThrow(() -> ExceptionUtils.dataNotFound("搜索结果", id));

        return ApiResponse.success(result, "获取搜索结果成功");
    }

    /**
     * 更新搜索结果
     */
    @PutMapping("/{id}")
    public ApiResponse<SearchResult> update(@PathVariable Long id, @RequestBody SearchResult result) {
        // 参数验证
        ExceptionUtils.requireNonNull(result, "searchResult");

        // 查找现有记录
        SearchResult existing = repository.findById(id)
                .orElseThrow(() -> ExceptionUtils.dataNotFound("搜索结果", id));

        // 只更新允许修改的字段：备注、已读状态、排除状态
        existing.setNote(result.getNote());
        existing.setIsRead(result.getIsRead());
        existing.setIsExcluded(result.getIsExcluded());

        SearchResult updatedResult = repository.save(existing);
        log.info("搜索结果更新成功: ID={}", id);

        return ApiResponse.success(updatedResult, "搜索结果更新成功");
    }

    /**
     * 删除搜索结果
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> delete(@PathVariable Long id) {
        // 验证记录是否存在
        if (!repository.existsById(id)) {
            throw ExceptionUtils.dataNotFound("搜索结果", id);
        }

        repository.deleteById(id);
        log.info("搜索结果删除成功: ID={}", id);

        return ApiResponse.success(null, "搜索结果删除成功");
    }
} 