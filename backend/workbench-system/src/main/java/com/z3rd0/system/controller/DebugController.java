package com.z3rd0.system.controller;

import com.z3rd0.common.model.Task;
import com.z3rd0.system.dto.ApiResponse;
import com.z3rd0.system.repository.TaskRepository;
import com.z3rd0.system.service.TaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 调试控制器 - 用于调试Task实体字段映射问题
 */
@RestController
@RequestMapping("/api/debug")
public class DebugController {
    
    private static final Logger log = LoggerFactory.getLogger(DebugController.class);
    
    @Autowired
    private TaskRepository taskRepository;

    @Autowired
    private TaskService taskService;
    
    /**
     * 检查Task实体的所有字段
     */
    @GetMapping("/task-fields")
    public ApiResponse<Map<String, Object>> checkTaskFields() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 获取Task类的所有字段
            Class<Task> taskClass = Task.class;
            Field[] fields = taskClass.getDeclaredFields();
            
            List<String> fieldNames = new ArrayList<>();
            List<String> scheduleFields = new ArrayList<>();
            
            for (Field field : fields) {
                String fieldName = field.getName();
                fieldNames.add(fieldName + " (" + field.getType().getSimpleName() + ")");
                
                // 检查定时任务相关字段
                if (fieldName.toLowerCase().contains("schedule") || 
                    fieldName.toLowerCase().contains("time") ||
                    fieldName.toLowerCase().contains("window")) {
                    scheduleFields.add(fieldName + " (" + field.getType().getSimpleName() + ")");
                }
            }
            
            result.put("totalFields", fields.length);
            result.put("allFields", fieldNames);
            result.put("scheduleRelatedFields", scheduleFields);
            result.put("className", taskClass.getName());
            
            log.info("Task实体字段检查完成，共{}个字段，其中{}个定时任务相关字段", 
                    fields.length, scheduleFields.size());
            
            return ApiResponse.success(result, "Task实体字段检查完成");
            
        } catch (Exception e) {
            log.error("检查Task实体字段失败: {}", e.getMessage(), e);
            return ApiResponse.error("检查Task实体字段失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试获取一个Task对象并检查其字段
     */
    @GetMapping("/task-instance")
    public ApiResponse<Map<String, Object>> checkTaskInstance() {
        try {
            // 获取第一个任务
            List<Task> tasks = taskRepository.findAll();
            if (tasks.isEmpty()) {
                return ApiResponse.error("没有找到任务数据");
            }
            
            Task task = tasks.get(0);
            Map<String, Object> result = new HashMap<>();
            
            result.put("taskId", task.getId());
            result.put("taskName", task.getName());
            
            // 尝试通过反射获取定时任务字段的值
            try {
                Field scheduleEnabledField = Task.class.getDeclaredField("scheduleEnabled");
                scheduleEnabledField.setAccessible(true);
                Object scheduleEnabledValue = scheduleEnabledField.get(task);
                result.put("scheduleEnabled", scheduleEnabledValue);
                result.put("scheduleEnabledFieldExists", true);
            } catch (NoSuchFieldException e) {
                result.put("scheduleEnabledFieldExists", false);
                result.put("scheduleEnabledError", e.getMessage());
            }
            
            try {
                Field scheduleTimeField = Task.class.getDeclaredField("scheduleTime");
                scheduleTimeField.setAccessible(true);
                Object scheduleTimeValue = scheduleTimeField.get(task);
                result.put("scheduleTime", scheduleTimeValue);
                result.put("scheduleTimeFieldExists", true);
            } catch (NoSuchFieldException e) {
                result.put("scheduleTimeFieldExists", false);
                result.put("scheduleTimeError", e.getMessage());
            }
            
            return ApiResponse.success(result, "Task实例检查完成");
            
        } catch (Exception e) {
            log.error("检查Task实例失败: {}", e.getMessage(), e);
            return ApiResponse.error("检查Task实例失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试数据库连接和基本查询
     */
    @GetMapping("/database-test")
    public ApiResponse<Map<String, Object>> testDatabase() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 测试基本查询
            long taskCount = taskRepository.count();
            result.put("taskCount", taskCount);
            
            // 测试是否能获取任务列表
            List<Task> tasks = taskRepository.findAll();
            result.put("canQueryTasks", !tasks.isEmpty());
            
            if (!tasks.isEmpty()) {
                Task firstTask = tasks.get(0);
                result.put("firstTaskId", firstTask.getId());
                result.put("firstTaskName", firstTask.getName());
            }
            
            return ApiResponse.success(result, "数据库测试完成");
            
        } catch (Exception e) {
            log.error("数据库测试失败: {}", e.getMessage(), e);
            return ApiResponse.error("数据库测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试定时任务功能
     */
    @GetMapping("/schedule-test")
    public ApiResponse<Map<String, Object>> testScheduleFunction() {
        try {
            Map<String, Object> result = new HashMap<>();

            // 测试查找定时任务
            List<Task> scheduledTasks = taskService.findScheduledTasks();
            result.put("scheduledTasksCount", scheduledTasks.size());

            // 测试查找启用的定时任务
            List<Task> enabledTasks = taskService.findEnabledScheduledTasks();
            result.put("enabledTasksCount", enabledTasks.size());

            // 测试Repository查询
            long totalScheduledCount = taskRepository.countByScheduleEnabled(true);
            result.put("totalScheduledCount", totalScheduledCount);

            long activeScheduledCount = taskRepository.countByScheduleEnabledAndScheduleStatus(true, "ACTIVE");
            result.put("activeScheduledCount", activeScheduledCount);

            result.put("serviceWorking", true);
            result.put("repositoryWorking", true);

            return ApiResponse.success(result, "定时任务功能测试完成");

        } catch (Exception e) {
            log.error("定时任务功能测试失败: {}", e.getMessage(), e);
            return ApiResponse.error("定时任务功能测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试启用定时任务功能
     */
    @PostMapping("/enable-schedule/{taskId}")
    public ApiResponse<Task> testEnableSchedule(@PathVariable Long taskId) {
        try {
            // 测试启用定时任务
            Task updatedTask = taskService.enableSchedule(taskId, "09:00", "SMART", "DAILY");

            log.info("成功启用任务定时功能: taskId={}, scheduleTime={}, strategy={}, frequency={}",
                    taskId, updatedTask.getScheduleTime(), updatedTask.getTimeWindowStrategy(), updatedTask.getScheduleFrequency());

            return ApiResponse.success(updatedTask, "定时任务启用成功");

        } catch (Exception e) {
            log.error("启用定时任务失败: {}", e.getMessage(), e);
            return ApiResponse.error("启用定时任务失败: " + e.getMessage());
        }
    }

    /**
     * 测试禁用定时任务功能
     */
    @PostMapping("/disable-schedule/{taskId}")
    public ApiResponse<Task> testDisableSchedule(@PathVariable Long taskId) {
        try {
            // 测试禁用定时任务
            Task updatedTask = taskService.disableSchedule(taskId);

            log.info("成功禁用任务定时功能: taskId={}", taskId);

            return ApiResponse.success(updatedTask, "定时任务禁用成功");

        } catch (Exception e) {
            log.error("禁用定时任务失败: {}", e.getMessage(), e);
            return ApiResponse.error("禁用定时任务失败: " + e.getMessage());
        }
    }
}
