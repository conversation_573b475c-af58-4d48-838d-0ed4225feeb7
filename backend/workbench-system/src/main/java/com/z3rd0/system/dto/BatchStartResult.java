package com.z3rd0.system.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批量启动结果DTO
 */
@Data
@NoArgsConstructor
public class BatchStartResult {
    
    /**
     * 总任务数
     */
    private int totalTasks;
    
    /**
     * 成功启动数
     */
    private int successCount;
    
    /**
     * 失败数
     */
    private int failCount;
    
    /**
     * 时间范围
     */
    private String timeRange;
    
    /**
     * 成功率
     */
    private double successRate;
    
    public BatchStartResult(int totalTasks, int successCount, int failCount, String timeRange) {
        this.totalTasks = totalTasks;
        this.successCount = successCount;
        this.failCount = failCount;
        this.timeRange = timeRange;
        this.successRate = totalTasks > 0 ? (double) successCount / totalTasks * 100 : 0.0;
    }
}
