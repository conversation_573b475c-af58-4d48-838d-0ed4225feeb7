package com.z3rd0.system.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 任务执行更新结果DTO
 */
@Data
@NoArgsConstructor
public class TaskExecutionUpdateResult {
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 筛选范围
     */
    private String filterRange;
    
    public TaskExecutionUpdateResult(Long taskId, String filterRange) {
        this.taskId = taskId;
        this.filterRange = filterRange;
    }
}
