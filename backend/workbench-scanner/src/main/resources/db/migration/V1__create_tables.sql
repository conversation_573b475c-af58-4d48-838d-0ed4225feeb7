-- 创建必要的数据库表

-- 创建搜索结果表
CREATE TABLE IF NOT EXISTS search_results (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_name VARCHAR(255),
    rule TEXT COMMENT '执行的搜索规则',
    ip VARCHAR(100),
    port VARCHAR(20),
    domain VARCHAR(255),
    time VARCHAR(100),
    title VARCHAR(500),
    host VA<PERSON><PERSON><PERSON>(255),
    path VARCHAR(500),
    province VARCHAR(100),
    created_at DATETIME,
    INDEX idx_search_results_domain (domain),
    INDEX idx_search_results_host (host),
    INDEX idx_search_results_task_name (task_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建Cookie记录表
CREATE TABLE IF NOT EXISTS cookie_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    cookie TEXT,
    create_time DATETIME,
    update_time DATETIME,
    INDEX idx_cookie_records_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
