-- 修复search_results表的索引问题
-- 版本: V5
-- 描述: 删除有问题的索引，重新创建优化后的索引

-- 删除可能存在的有问题的索引
DROP INDEX IF EXISTS idx_title ON search_results;
DROP INDEX IF EXISTS idx_path ON search_results;
DROP INDEX IF EXISTS idx_unique_check ON search_results;

-- 重新创建优化后的索引（使用前缀索引避免键长度超限）
CREATE INDEX IF NOT EXISTS idx_title_prefix ON search_results (title(100));
CREATE INDEX IF NOT EXISTS idx_path_prefix ON search_results (path(100));
CREATE INDEX IF NOT EXISTS idx_original_id ON search_results (original_id);

-- 确保其他必要的索引存在
CREATE INDEX IF NOT EXISTS idx_task_name ON search_results (task_name);
CREATE INDEX IF NOT EXISTS idx_ip ON search_results (ip);
CREATE INDEX IF NOT EXISTS idx_domain_prefix ON search_results (domain(100));
CREATE INDEX IF NOT EXISTS idx_asn ON search_results (asn);
CREATE INDEX IF NOT EXISTS idx_status_code ON search_results (status_code);
CREATE INDEX IF NOT EXISTS idx_server ON search_results (server);
CREATE INDEX IF NOT EXISTS idx_icp_licence ON search_results (icp_licence);
CREATE INDEX IF NOT EXISTS idx_location_country ON search_results (location_country);
CREATE INDEX IF NOT EXISTS idx_created_at ON search_results (created_at);
CREATE INDEX IF NOT EXISTS idx_is_read ON search_results (is_read);
CREATE INDEX IF NOT EXISTS idx_is_excluded ON search_results (is_excluded);

-- 创建复合索引用于常见查询
CREATE INDEX IF NOT EXISTS idx_task_ip ON search_results (task_name, ip);
CREATE INDEX IF NOT EXISTS idx_read_excluded ON search_results (is_read, is_excluded);
