-- 创建任务表
CREATE TABLE IF NOT EXISTS tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_name VARCHAR(255) NOT NULL,
    rule TEXT,
    time_range VARCHAR(100),
    status VARCHAR(20) DEFAULT 'PENDING',
    error_message TEXT,
    retry_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    
    -- 添加索引
    INDEX idx_task_status (status),
    INDEX idx_task_name (task_name),
    INDEX idx_task_created_at (created_at)
); 