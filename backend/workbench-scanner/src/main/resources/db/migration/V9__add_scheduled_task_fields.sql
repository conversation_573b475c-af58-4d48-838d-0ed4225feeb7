-- 添加定时任务相关字段到tasks表
-- 用于支持每日定时任务功能和智能时间窗口计算

-- 添加定时任务开关字段
ALTER TABLE tasks 
ADD COLUMN IF NOT EXISTS schedule_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用定时执行';

-- 添加定时执行时间配置字段
ALTER TABLE tasks 
ADD COLUMN IF NOT EXISTS schedule_time VARCHAR(20) COMMENT '定时执行时间(HH:mm格式，如09:00)';

-- 添加Cron表达式字段（可选，用于更复杂的调度需求）
ALTER TABLE tasks 
ADD COLUMN IF NOT EXISTS schedule_cron VARCHAR(50) COMMENT 'Cron表达式(可选，用于复杂调度)';

-- 添加时间窗口策略字段
ALTER TABLE tasks 
ADD COLUMN IF NOT EXISTS time_window_strategy VARCHAR(20) DEFAULT 'SMART' COMMENT '时间窗口策略: SMART(智能), FIXED(固定), ROLLING(滚动)';

-- 添加上次定时执行时间字段
ALTER TABLE tasks 
ADD COLUMN IF NOT EXISTS last_scheduled_at TIMESTAMP NULL COMMENT '上次定时执行时间';

-- 添加时间窗口配置字段
ALTER TABLE tasks 
ADD COLUMN IF NOT EXISTS time_window_config TEXT COMMENT '时间窗口配置JSON，存储窗口大小、偏移等参数';

-- 添加定时任务状态字段
ALTER TABLE tasks 
ADD COLUMN IF NOT EXISTS schedule_status VARCHAR(20) DEFAULT 'INACTIVE' COMMENT '定时任务状态: ACTIVE(活跃), INACTIVE(非活跃), PAUSED(暂停)';

-- 为新字段添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_tasks_schedule_enabled ON tasks(schedule_enabled);
CREATE INDEX IF NOT EXISTS idx_tasks_schedule_time ON tasks(schedule_time);
CREATE INDEX IF NOT EXISTS idx_tasks_last_scheduled_at ON tasks(last_scheduled_at);
CREATE INDEX IF NOT EXISTS idx_tasks_schedule_status ON tasks(schedule_status);

-- 更新现有记录的默认值
UPDATE tasks SET 
    schedule_enabled = FALSE,
    time_window_strategy = 'SMART',
    schedule_status = 'INACTIVE'
WHERE schedule_enabled IS NULL;

-- 添加表注释更新
ALTER TABLE tasks COMMENT = '任务表 - 存储系统中的所有任务信息，包括定时任务配置和时间窗口策略';
