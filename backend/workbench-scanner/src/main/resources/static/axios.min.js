/* 简化版axios实现 */
(function(global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = global || self, global.axios = factory());
}(this, function() {
  'use strict';
  
  // 简单实现的axios
  function axios(config) {
    return new Promise(function(resolve, reject) {
      try {
        var url = config.url;
        var method = (config.method || 'get').toLowerCase();
        var data = config.data;
        var headers = config.headers || {};
        var timeout = config.timeout || 0;
        
        var xhr = new XMLHttpRequest();
        xhr.open(method, url, true);
        
        // 设置headers
        Object.keys(headers).forEach(function(key) {
          xhr.setRequestHeader(key, headers[key]);
        });
        
        // 设置超时
        if (timeout) {
          xhr.timeout = timeout;
        }
        
        xhr.onreadystatechange = function() {
          if (xhr.readyState !== 4) return;
          
          if (xhr.status >= 200 && xhr.status < 300) {
            var responseData;
            try {
              responseData = JSON.parse(xhr.responseText);
            } catch (e) {
              responseData = xhr.responseText;
            }
            
            resolve({
              data: responseData,
              status: xhr.status,
              statusText: xhr.statusText,
              headers: xhr.getAllResponseHeaders(),
              config: config,
              request: xhr
            });
          } else {
            reject(new Error('请求失败，状态码: ' + xhr.status));
          }
        };
        
        xhr.onerror = function() {
          reject(new Error('网络错误'));
        };
        
        xhr.ontimeout = function() {
          reject(new Error('请求超时'));
        };
        
        // 发送请求
        if (method === 'get' || method === 'head') {
          xhr.send(null);
        } else {
          var requestData = null;
          if (data) {
            if (typeof data === 'object') {
              headers['Content-Type'] = headers['Content-Type'] || 'application/json;charset=utf-8';
              requestData = JSON.stringify(data);
            } else {
              requestData = data;
            }
          }
          xhr.send(requestData);
        }
      } catch (e) {
        reject(e);
      }
    });
  }
  
  // 实现axios.get方法
  axios.get = function(url, config) {
    return axios(Object.assign({}, config || {}, {
      method: 'get',
      url: url
    }));
  };
  
  // 实现axios.post方法
  axios.post = function(url, data, config) {
    return axios(Object.assign({}, config || {}, {
      method: 'post',
      url: url,
      data: data
    }));
  };
  
  // 实现axios.put方法
  axios.put = function(url, data, config) {
    return axios(Object.assign({}, config || {}, {
      method: 'put',
      url: url,
      data: data
    }));
  };
  
  // 实现axios.delete方法
  axios.delete = function(url, config) {
    return axios(Object.assign({}, config || {}, {
      method: 'delete',
      url: url
    }));
  };
  
  return axios;
}));
