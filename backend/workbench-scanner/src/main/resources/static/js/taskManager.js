/**
 * 爬虫任务管理器 JavaScript
 * 处理任务状态查询、提交和日志展示
 */

// 全局变量
let lastTaskName = '';
let lastStatus = '';
let statusCheckInterval = null;
let taskLogMessages = new Set(); // 使用Set避免重复消息
let lastProcessingLogCount = 0; // 跟踪最后一次处理日志的数量

// 在文档加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // DOM元素缓存
    const elements = {
        taskForm: document.getElementById('task-form'),
        taskName: document.getElementById('task-name'),
        searchRule: document.getElementById('search-rule'),
        timeRange: document.getElementById('time-range'),
        submitBtn: document.getElementById('submit-btn'),
        clearBtn: document.getElementById('clear-btn'),
        alertBox: document.getElementById('alert'),
        taskLogs: document.getElementById('task-logs'),
        clearLogs: document.getElementById('clear-logs'),
        autoScrollToggle: document.getElementById('auto-scroll-toggle'),
        refreshStatus: document.getElementById('refresh-status'),
        systemStatus: document.getElementById('system-status'),
        queueStatus: document.getElementById('queue-status'),
        lastRun: document.getElementById('last-run'),
        progressContainer: document.getElementById('task-progress-container'),
        currentTaskName: document.getElementById('current-task-name'),
        taskStatus: document.getElementById('task-status'),
        processedCount: document.getElementById('processed-count'),
        totalCount: document.getElementById('total-count'),
        elapsedTime: document.getElementById('elapsed-time'),
        progressBar: document.getElementById('progress-bar'),
        progressText: document.getElementById('progress-text'),
        remainingTime: document.getElementById('remaining-time'),
        progressDetails: document.getElementById('task-progress-details'),
        container: document.querySelector('.container'),
        widescreenRow: document.querySelector('.widescreen-row'),
        leftCol: document.querySelector('.widescreen-row .col-md-6:first-child'),
        rightCol: document.querySelector('.widescreen-row .col-md-6:last-child')
    };

    // 应用程序状态
    const appState = {
        autoScroll: true,
        taskRunning: false,
        startTime: null,
        timerInterval: null,
        taskData: {},
        isMobile: window.innerWidth < 768, // 检测是否为移动设备
        isWidescreen: window.innerWidth >= 1440, // 检测是否为宽屏
        useServerTime: false,  // 新增：标记是否使用服务器返回的时间
        serverTimeText: null  // 新增：存储服务器返回的原始时间文本
    };

    // 初始化
    init();

    // 初始化函数
    function init() {
        // 隐藏进度容器
        elements.progressContainer.style.display = 'none';
        
        // 注册事件监听器
        elements.taskForm.addEventListener('submit', handleTaskSubmit);
        elements.clearBtn.addEventListener('click', clearForm);
        elements.clearLogs.addEventListener('click', clearLogs);
        elements.autoScrollToggle.addEventListener('click', toggleAutoScroll);
        elements.refreshStatus.addEventListener('click', refreshSystemStatus);
        
        // 预设按钮点击事件
        document.querySelectorAll('[data-preset]').forEach(btn => {
            btn.addEventListener('click', () => setTimeRangePreset(btn.dataset.preset));
        });
        
        // 添加窗口大小变化监听器
        window.addEventListener('resize', handleWindowResize);
        
        // 初始加载系统状态
        refreshSystemStatus();
        
        // 自动滚动日志
        setInterval(() => {
            if (appState.autoScroll) {
                scrollLogsToBottom();
            }
        }, 500);
        
        // 初始化响应式UI调整
        adjustUIForScreenSize();
        
        // 初始化宽屏适配
        adjustForWidescreen();
    }
    
    // 窗口大小改变处理
    function handleWindowResize() {
        const wasMobile = appState.isMobile;
        const wasWidescreen = appState.isWidescreen;
        
        appState.isMobile = window.innerWidth < 768;
        appState.isWidescreen = window.innerWidth >= 1440;
        
        // 如果设备类型改变（电脑⟷手机），则调整UI
        if (wasMobile !== appState.isMobile) {
            adjustUIForScreenSize();
        }
        
        // 如果宽屏状态改变，调整宽屏布局
        if (wasWidescreen !== appState.isWidescreen) {
            adjustForWidescreen();
        }
        
        // 无论如何，确保布局适配当前宽度
        optimizeLayoutForCurrentWidth();
    }
    
    // 根据当前窗口宽度优化布局
    function optimizeLayoutForCurrentWidth() {
        const width = window.innerWidth;
        
        // 调整容器宽度适配屏幕
        if (width >= 2560) { // 2K及以上分辨率
            elements.container.style.maxWidth = '90%';
            elements.container.style.width = '90%';
        } else if (width >= 1920) { // 1080p
            elements.container.style.maxWidth = '85%';
            elements.container.style.width = '85%';
        } else if (width >= 1440) { // 较宽屏幕
            elements.container.style.maxWidth = '80%';
            elements.container.style.width = '80%';
        } else {
            elements.container.style.maxWidth = '';
            elements.container.style.width = '100%';
        }
        
        // 调整列宽比例
        if (width >= 1920) {
            if (elements.leftCol && elements.rightCol) {
                elements.leftCol.style.flex = '0 0 45%';
                elements.leftCol.style.maxWidth = '45%';
                elements.rightCol.style.flex = '0 0 55%';
                elements.rightCol.style.maxWidth = '55%';
            }
        } else if (width >= 768) {
            if (elements.leftCol && elements.rightCol) {
                elements.leftCol.style.flex = '0 0 50%';
                elements.leftCol.style.maxWidth = '50%';
                elements.rightCol.style.flex = '0 0 50%';
                elements.rightCol.style.maxWidth = '50%';
            }
        } else {
            if (elements.leftCol && elements.rightCol) {
                elements.leftCol.style.flex = '0 0 100%';
                elements.leftCol.style.maxWidth = '100%';
                elements.rightCol.style.flex = '0 0 100%';
                elements.rightCol.style.maxWidth = '100%';
            }
        }
    }
    
    // 宽屏适配调整
    function adjustForWidescreen() {
        if (appState.isWidescreen) {
            // 宽屏模式下的优化
            if (elements.widescreenRow) {
                elements.widescreenRow.style.flexWrap = 'nowrap';
            }
            
            // 应用优化的布局比例
            optimizeLayoutForCurrentWidth();
            
            // 增大控制台高度
            if (elements.taskLogs) {
                const height = window.innerHeight * 0.6; // 屏幕高度的60%
                elements.taskLogs.style.height = `${Math.min(height, 600)}px`; // 最大600px
            }
        } else {
            // 非宽屏模式
            if (elements.widescreenRow) {
                elements.widescreenRow.style.flexWrap = 'wrap';
            }
            
            // 还原默认高度
            if (elements.taskLogs) {
                elements.taskLogs.style.height = '';
            }
            
            // 恢复默认列宽
            if (elements.leftCol && elements.rightCol) {
                elements.leftCol.style.flex = '';
                elements.leftCol.style.maxWidth = '';
                elements.rightCol.style.flex = '';
                elements.rightCol.style.maxWidth = '';
            }
        }
    }
    
    // 根据屏幕大小调整UI
    function adjustUIForScreenSize() {
        if (appState.isMobile) {
            // 移动设备UI优化
            adjustUIForMobile();
        } else {
            // 桌面设备UI还原
            adjustUIForDesktop();
        }
    }
    
    // 移动设备UI优化
    function adjustUIForMobile() {
        // 简化按钮文本
        elements.clearLogs.innerHTML = '<i class="bi bi-trash"></i>';
        elements.autoScrollToggle.innerHTML = '<i class="bi bi-arrow-down-circle"></i>';
        elements.refreshStatus.innerHTML = '<i class="bi bi-arrow-repeat"></i>';
        elements.submitBtn.innerHTML = '<i class="bi bi-play-fill"></i> 启动';
        elements.clearBtn.innerHTML = '<i class="bi bi-x-lg"></i> 清空';
        
        // 添加工具提示
        elements.clearLogs.title = "清空日志";
        elements.autoScrollToggle.title = appState.autoScroll ? "关闭自动滚动" : "开启自动滚动";
        elements.refreshStatus.title = "刷新状态";
    }
    
    // 桌面设备UI还原
    function adjustUIForDesktop() {
        // 恢复按钮文本
        elements.clearLogs.innerHTML = '<i class="bi bi-trash"></i> 清空日志';
        elements.autoScrollToggle.innerHTML = `<i class="bi bi-arrow-down-circle"></i> 自动滚动: ${appState.autoScroll ? '开' : '关'}`;
        elements.refreshStatus.innerHTML = '<i class="bi bi-arrow-repeat"></i> 刷新状态';
        elements.submitBtn.innerHTML = '<i class="bi bi-play-fill"></i> 启动任务';
        elements.clearBtn.innerHTML = '<i class="bi bi-x-lg"></i> 清空表单';
        
        // 移除工具提示
        elements.clearLogs.removeAttribute('title');
        elements.autoScrollToggle.removeAttribute('title');
        elements.refreshStatus.removeAttribute('title');
    }

    // 提交任务表单处理
    function handleTaskSubmit(e) {
        e.preventDefault();
        
        // 获取表单数据
        const taskData = {
            taskName: elements.taskName.value.trim(),
            searchRule: elements.searchRule.value.trim(),
            timeRange: elements.timeRange.value.trim()
        };
        
        // 验证表单数据
        if (!validateTaskData(taskData)) {
            return;
        }
        
        // 禁用提交按钮，防止重复提交
        elements.submitBtn.disabled = true;
        elements.submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 提交中...';
        
        // 发送任务到后端
        submitTask(taskData);
    }

    // 验证任务数据
    function validateTaskData(taskData) {
        // 清空之前的错误提示
        showAlert('', 'none');
        
        // 检查任务名称
        if (!taskData.taskName) {
            showAlert('请输入任务名称', 'error');
            return false;
        }
        
        // 检查搜索规则
        if (!taskData.searchRule) {
            showAlert('请输入搜索规则', 'error');
            return false;
        }
        
        // 检查时间范围格式
        if (!taskData.timeRange || !validateTimeRange(taskData.timeRange)) {
            showAlert('时间范围格式不正确，请使用 YYYY-MM-DD HH:MM:SS,YYYY-MM-DD HH:MM:SS 格式', 'error');
            return false;
        }
        
        return true;
    }

    // 验证时间范围格式
    function validateTimeRange(timeRange) {
        const regex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
        if (!regex.test(timeRange)) {
            return false;
        }
        
        const [startStr, endStr] = timeRange.split(',');
        const start = new Date(startStr);
        const end = new Date(endStr);
        
        // 检查日期是否有效
        if (isNaN(start.getTime()) || isNaN(end.getTime())) {
            return false;
        }
        
        // 检查开始时间是否小于结束时间
        if (start > end) {
            return false;
        }
        
        return true;
    }

    // 提交任务到服务器
    function submitTask(taskData) {
        // 记录任务数据
        appState.taskData = taskData;
        
        // 添加日志
        addLog(`正在提交任务: ${taskData.taskName}`);
        addLog(`搜索规则: ${taskData.searchRule}`);
        addLog(`时间范围: ${taskData.timeRange}`);
        
        // 禁用提交按钮
        elements.submitBtn.disabled = true;
        elements.submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 提交中...';
        
        // 发送AJAX请求到后端
        fetch('/api/tasks/submit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(taskData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // 任务提交成功
            showAlert('任务提交成功', 'success');
            addLog(`任务已提交并放入队列，任务ID: ${data.taskId || '未知'}`);
            
            // 重置表单状态
            resetSubmitButtonState();
            
            // 更新系统状态
            updateSystemStatus('busy', '任务处理中');
            
            // 启动任务跟踪
            startTaskTracker(data.taskId || taskData.taskName);
        })
        .catch(error => {
            console.error('任务提交失败:', error);
            showAlert('任务提交失败: ' + error.message, 'error');
            addLog(`任务提交失败: ${error.message}`);
            
            // 重置表单状态
            resetSubmitButtonState();
        });
    }

    // 开始任务追踪
    function startTaskTracker(taskId) {
        // 显示进度容器
        elements.progressContainer.style.display = 'block';
        
        // 初始化任务进度显示
        elements.currentTaskName.textContent = `任务: ${appState.taskData.taskName}`;
        elements.taskStatus.textContent = '初始化中';
        elements.processedCount.textContent = '0';
        elements.totalCount.textContent = '计算中';
        elements.progressBar.style.width = '0%';
        elements.progressBar.textContent = '0%';
        elements.progressText.textContent = '正在准备任务...';
        elements.remainingTime.textContent = '剩余时间: 计算中';
        elements.progressDetails.innerHTML = '等待任务处理信息...';
        elements.elapsedTime.textContent = '等待服务器响应...';
        
        // 设置跟踪状态 
        appState.taskRunning = true;
        appState.serverTimeText = null; // 存储服务器返回的原始时间文本
        
        // 不再使用本地计时器
        if (appState.timerInterval) {
            clearInterval(appState.timerInterval);
            appState.timerInterval = null;
        }
        
        // 不再进行任务状态轮询，由Vue组件处理
    }

    // 更新任务进度
    function updateTaskProgress(data) {
        if (!data) return;
        
        // 更新任务状态
        elements.taskStatus.textContent = getStatusText(data.status);
        
        // 更新任务名称（如果服务器返回）
        if (data.currentTaskName && data.currentTaskName.trim() !== '') {
            elements.currentTaskName.textContent = `任务: ${data.currentTaskName}`;
            // 同时更新本地存储的任务名称
            appState.taskData.taskName = data.currentTaskName;
        }
        
        // 注意：任务执行时间已经由handleTaskExecutionTime函数处理
        
        // 更新处理数据
        // 兼容性处理：后端可能返回processedItems或processedCount
        const processedCount = data.processedItems !== undefined ? data.processedItems : 
                              (data.processedCount !== undefined ? data.processedCount : undefined);
        if (processedCount !== undefined) {
            elements.processedCount.textContent = processedCount;
        }
        
        // 更新总数据量
        if (data.totalCount !== undefined && data.totalCount > 0) {
            elements.totalCount.textContent = data.totalCount;
        }
        
        // 计算并更新进度百分比
        if (data.totalCount > 0 && processedCount !== undefined) {
            const percent = Math.min(Math.round((processedCount / data.totalCount) * 100), 100);
            elements.progressBar.style.width = `${percent}%`;
            elements.progressBar.textContent = `${percent}%`;
            
            // 更新进度文本
            elements.progressText.textContent = `已处理 ${processedCount} / ${data.totalCount}`;
            
            // 估算剩余时间
            if (percent > 0) {
                // 优先使用服务器估算的剩余时间
                if (data.estimatedRemainingTime !== undefined && data.estimatedRemainingTime > 0) {
                    elements.remainingTime.textContent = `剩余时间: ${formatTime(data.estimatedRemainingTime * 1000)}`;
                } else {
                    // 本地计算
                    const elapsedMs = new Date() - appState.startTime;
                    const estimatedTotalMs = (elapsedMs / percent) * 100;
                    const remainingMs = estimatedTotalMs - elapsedMs;
                    
                    elements.remainingTime.textContent = `剩余时间: ${formatTime(remainingMs)}`;
                }
            }
        }
        
        // 如果有最近的处理细节，更新显示
        if (data.recentDetails && data.recentDetails.length > 0) {
            elements.progressDetails.innerHTML = data.recentDetails
                .map(detail => `<div class="progress-detail-item">${formatProgressDetail(detail)}</div>`)
                .join('');
        }
        
        // 如果有处理日志，更新显示
        if (data.processingLogs && data.processingLogs.length > 0) {
            data.processingLogs.forEach(log => {
                if (log && log.message) {
                    // 创建包含日志消息的对象
                    addLog(log.message, log.type);
                }
            });
        }
        
        // 添加日志
        if (data.latestLog) {
            addLog(data.latestLog);
        }
    }

    // 格式化进度详情
    function formatProgressDetail(detail) {
        if (!detail) return '';
        
        // 根据详情类型设置不同的CSS类
        let className = '';
        if (detail.type === 'error') {
            className = 'text-danger';
        } else if (detail.type === 'warning') {
            className = 'text-warning';
        } else if (detail.type === 'success') {
            className = 'text-success';
        }
        
        return `<span class="${className}">${detail.message || detail}</span>`;
    }

    // 完成任务
    function finishTask(data) {
        appState.taskRunning = false;
        
        // 停止任何计时器（如果存在）
        if (appState.timerInterval) {
            clearInterval(appState.timerInterval);
            appState.timerInterval = null;
        }
        
        // 更新UI
        const status = data.status || 'UNKNOWN';
        elements.taskStatus.textContent = getStatusText(status);
        
        // 优先使用服务器返回的最终执行时间
        if (data.taskExecutionTime && data.taskExecutionTime.trim() !== '') {
            elements.elapsedTime.textContent = data.taskExecutionTime;
            console.log("任务完成，最终执行时间:", data.taskExecutionTime);
        } else if (appState.serverTimeText) {
            // 使用最后一次服务器返回的时间
            elements.elapsedTime.textContent = appState.serverTimeText;
            console.log("任务完成，使用最后一次服务器时间:", appState.serverTimeText);
        }
        
        // 添加完成日志
        if (status === 'COMPLETED') {
            // 注意：后端返回的字段名是processedItems
            const processedCount = data.processedItems !== undefined ? data.processedItems : 
                                 (data.processedCount !== undefined ? data.processedCount : 0);
            addLog(`任务 ${appState.taskData.taskName} 已完成，处理了 ${processedCount} 条数据`, "success");
            showAlert('任务已完成', 'success');
        } else if (status === 'FAILED') {
            addLog(`任务 ${appState.taskData.taskName} 失败: ${data.errorMessage || '未知错误'}`, "error");
            showAlert(`任务失败: ${data.errorMessage || '未知错误'}`, 'error');
        }
        
        // 更新系统状态
        updateSystemStatus('idle', '空闲中');
        
        // 更新最近运行信息
        const now = new Date();
        elements.lastRun.innerHTML = `<span class="status-icon ${status === 'COMPLETED' ? 'green' : 'red'}"></span> ${formatDateTime(now)}`;
    }

    // 格式化时间
    function formatTime(ms) {
        if (isNaN(ms)) return '00:00:00';
        
        const totalSeconds = Math.floor(ms / 1000);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;
        
        return [
            hours.toString().padStart(2, '0'),
            minutes.toString().padStart(2, '0'),
            seconds.toString().padStart(2, '0')
        ].join(':');
    }

    // 格式化日期时间
    function formatDateTime(date) {
        if (!date) return '';
        
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // 获取状态文本
    function getStatusText(status) {
        const statusMap = {
            'PENDING': '等待中',
            'PROCESSING': '处理中',
            'COMPLETED': '已完成',
            'FAILED': '失败',
            'CANCELED': '已取消'
        };
        
        return statusMap[status] || status;
    }

    // 添加日志
    function addLog(message, type = '') {
        if (!message) return;
        
        // 检查是否已存在相同消息，避免日志重复
        if (taskLogMessages.has(message)) {
            return;
        }
        
        // 记录消息到Set中
        taskLogMessages.add(message);
        
        // 如果日志消息过多，清除旧消息
        if (taskLogMessages.size > 200) {
            // 删除最早的日志，保持最多200条
            const toDelete = [...taskLogMessages].slice(0, taskLogMessages.size - 200);
            toDelete.forEach(msg => taskLogMessages.delete(msg));
        }
        
        const now = new Date();
        const timestamp = now.toTimeString().split(' ')[0];
        
        // 根据消息类型设置样式
        let className = '';
        if (type === 'error') {
            className = 'text-danger';
        } else if (type === 'warning') {
            className = 'text-warning';
        } else if (type === 'success') {
            className = 'text-success';
        } else if (type === 'info') {
            className = 'text-info';
        }
        
        const logLine = document.createElement('div');
        logLine.className = 'console-line';
        logLine.innerHTML = `<span class="timestamp">[${timestamp}]</span> <span class="${className}">${message}</span>`;
        
        elements.taskLogs.appendChild(logLine);
        
        // 自动滚动到底部
        if (appState.autoScroll) {
            scrollLogsToBottom();
        }
    }

    // 滚动日志到底部
    function scrollLogsToBottom() {
        elements.taskLogs.scrollTop = elements.taskLogs.scrollHeight;
    }

    // 清空日志
    function clearLogs() {
        elements.taskLogs.innerHTML = '';
        addLog('日志已清空');
    }

    // 切换自动滚动
    function toggleAutoScroll() {
        appState.autoScroll = !appState.autoScroll;
        
        if (appState.isMobile) {
            elements.autoScrollToggle.title = appState.autoScroll ? "关闭自动滚动" : "开启自动滚动";
        } else {
            elements.autoScrollToggle.innerHTML = 
                `<i class="bi bi-arrow-down-circle"></i> 自动滚动: ${appState.autoScroll ? '开' : '关'}`;
        }
        
        if (appState.autoScroll) {
            scrollLogsToBottom();
        }
    }

    // 刷新系统状态
    function refreshSystemStatus() {
        if (appState.isMobile) {
            elements.refreshStatus.innerHTML = '<i class="bi bi-arrow-repeat spin"></i>';
        } else {
            elements.refreshStatus.innerHTML = '<i class="bi bi-arrow-repeat spin"></i> 刷新中...';
        }
        
        // 发送请求获取系统状态
        fetch('/api/system/status')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // 更新系统状态
                updateSystemStatus(data.status, data.statusText);
                
                // 更新队列状态
                elements.queueStatus.innerHTML = 
                    `<span class="status-icon ${data.queueCount > 0 ? 'orange' : 'blue'}"></span> ${data.queueCount} 个任务等待处理`;
                
                // 更新最近运行
                if (data.lastRunTime) {
                    elements.lastRun.innerHTML = 
                        `<span class="status-icon ${data.lastRunSuccess ? 'green' : 'red'}"></span> ${data.lastRunTime}`;
                }
                
                elements.refreshStatus.innerHTML = '<i class="bi bi-arrow-repeat"></i> 刷新状态';
                addLog('系统状态已刷新');
            })
            .catch(error => {
                console.error('获取系统状态失败:', error);
                addLog(`获取系统状态失败: ${error.message}`);
                elements.refreshStatus.innerHTML = '<i class="bi bi-arrow-repeat"></i> 刷新状态';
            });
    }

    // 更新系统状态
    function updateSystemStatus(status, text) {
        // 移除之前的状态类
        elements.systemStatus.classList.remove('badge-idle', 'badge-busy', 'badge-error');
        
        // 添加新状态类
        elements.systemStatus.classList.add(`badge-${status}`);
        
        // 更新状态文本
        elements.systemStatus.querySelector('span').textContent = text || getStatusLabel(status);
        
        // 更新状态图标
        const iconClass = getStatusIcon(status);
        elements.systemStatus.querySelector('i').className = iconClass;
    }

    // 获取状态图标
    function getStatusIcon(status) {
        const iconMap = {
            'idle': 'bi bi-check-circle',
            'busy': 'bi bi-arrow-repeat spin',
            'error': 'bi bi-exclamation-triangle'
        };
        
        return iconMap[status] || 'bi bi-question-circle';
    }

    // 获取状态标签
    function getStatusLabel(status) {
        const labelMap = {
            'idle': '空闲中',
            'busy': '任务处理中',
            'error': '出错了'
        };
        
        return labelMap[status] || status;
    }

    // 显示提示信息
    function showAlert(message, type = 'info') {
        // 清除之前的类
        elements.alertBox.classList.remove('alert-info', 'alert-success', 'alert-error', 'alert-warning');
        
        if (type === 'none') {
            elements.alertBox.style.display = 'none';
            return;
        }
        
        // 设置新的类和消息
        elements.alertBox.classList.add(`alert-${type}`);
        elements.alertBox.innerHTML = message;
        elements.alertBox.style.display = 'block';
        
        // 如果是成功或错误消息，3秒后自动隐藏
        if (type === 'success' || type === 'error') {
            setTimeout(() => {
                elements.alertBox.style.opacity = '0';
                setTimeout(() => {
                    elements.alertBox.style.display = 'none';
                    elements.alertBox.style.opacity = '1';
                }, 500);
            }, 3000);
        }
    }

    // 清空表单
    function clearForm() {
        elements.taskForm.reset();
        showAlert('', 'none');
    }

    // 设置时间范围预设
    function setTimeRangePreset(preset) {
        const now = new Date();
        let startDate, endDate;
        
        switch (preset) {
            case 'today':
                startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
                endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
                break;
                
            case 'yesterday':
                startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 0, 0, 0);
                endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 23, 59, 59);
                break;
                
            case 'thisWeek':
                // 计算本周的星期一
                const dayOfWeek = now.getDay() || 7; // 获取星期几 (0-6)，星期天转为7
                const mondayDiff = dayOfWeek - 1; // 计算与星期一的天数差
                startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - mondayDiff, 0, 0, 0);
                endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + (7 - dayOfWeek), 23, 59, 59);
                break;
                
            case 'lastWeek':
                // 计算上周的星期一和星期日
                const lastMondayDiff = (now.getDay() || 7) + 6; // 上周一与今天的天数差
                startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - lastMondayDiff, 0, 0, 0);
                endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - lastMondayDiff + 6, 23, 59, 59);
                break;
                
            default:
                return;
        }
        
        // 格式化日期
        const formatDate = (date) => {
            return date.toISOString().slice(0, 19).replace('T', ' ');
        };
        
        // 设置时间范围输入框的值
        elements.timeRange.value = `${formatDate(startDate)},${formatDate(endDate)}`;
    }

    // 在任务提交成功后更新按钮状态
    function resetSubmitButtonState() {
        elements.submitBtn.disabled = false;
        if (appState.isMobile) {
            elements.submitBtn.innerHTML = '<i class="bi bi-play-fill"></i> 启动';
        } else {
            elements.submitBtn.innerHTML = '<i class="bi bi-play-fill"></i> 启动任务';
        }
    }
}); 