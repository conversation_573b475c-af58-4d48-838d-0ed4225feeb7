<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>爬虫引擎</title>
    <!-- 引入Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- axios加载，先尝试CDN，失败则加载本地文件 -->
    <script>
        function loadScript(src, fallbackSrc, callback) {
            var script = document.createElement('script');
            script.src = src;
            script.onerror = function() {
                console.warn('无法从CDN加载:' + src + '，尝试本地加载');
                if (fallbackSrc) {
                    var fallback = document.createElement('script');
                    fallback.src = fallbackSrc;
                    fallback.onerror = function() {
                        console.error('本地文件也加载失败:' + fallbackSrc);
                        if (callback) callback(false);
                    };
                    fallback.onload = function() {
                        console.log('成功从本地加载:' + fallbackSrc);
                        if (callback) callback(true);
                    };
                    document.head.appendChild(fallback);
                } else {
                    if (callback) callback(false);
                }
            };
            script.onload = function() {
                console.log('成功从CDN加载:' + src);
                if (callback) callback(true);
            };
            document.head.appendChild(script);
        }
        
        // 加载axios
        loadScript('https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js', 'axios.min.js', function(success) {
            if (!success) {
                console.error('axios加载失败，部分功能可能不可用！');
            }
        });
    </script>
    <!-- 引入Element Plus -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/element-plus"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue"></script>
    <!-- 添加Chart.js用于数据可视化 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 添加Animate.css用于动画效果 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        body {
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.5;
        }
        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .app-header {
            background-color: #409EFF;
            color: white;
            padding: 15px 20px;
            border-radius: 4px 4px 0 0;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }
        .app-header h1 {
            margin: 0;
            font-size: 1.8rem;
        }
        .status-card {
            margin-bottom: 20px;
        }
        .task-form-card, .logs-card {
            margin-bottom: 20px;
        }
        .console-wrapper {
            background-color: #2c3e50;
            color: #fff;
            padding: 10px;
            border-radius: 4px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9rem;
        }
        .console-line {
            line-height: 1.3;
            padding: 3px 0;
            border-bottom: 1px solid #3a5169;
        }
        .console-timestamp {
            color: #e6a23c;
            margin-right: 10px;
        }
        .console-message {
            color: #dcdfe6;
        }
        .console-success {
            color: #67c23a;
        }
        .console-error {
            color: #f56c6c;
        }
        .console-warning {
            color: #e6a23c;
        }
        .el-form-item {
            margin-bottom: 18px;
        }
        .task-progress {
            margin-bottom: 20px;
        }
        
        /* 新增样式 */
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 16px;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .status-badge-idle {
            background-color: #67c23a;
            color: white;
        }
        
        .status-badge-busy {
            background-color: #e6a23c;
            color: white;
        }
        
        .status-badge-error {
            background-color: #f56c6c;
            color: white;
        }
        
        .status-badge i {
            margin-right: 6px;
        }
        
        .spin {
            animation: spin 1.5s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .task-stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-bottom: 15px;
        }
        
        .task-stats-card {
            margin-bottom: 20px;
            animation: fadeIn 1s ease-in-out;
        }
        .task-stats-card .el-card__header {
            background-color: #f5f7fa;
            border-bottom: 1px solid #ebeef5;
        }
        .task-stats-card .stat-card {
            transition: all 0.3s ease;
            text-align: center;
            padding: 15px 10px;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            background-color: white;
            height: 100%;
        }
        .task-stats-card .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .task-stats-card .stat-icon {
            color: #409EFF;
            font-size: 24px;
            margin-bottom: 10px;
        }
        .task-stats-card .stat-value {
            font-size: 22px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .task-stats-card .stat-label {
            font-size: 14px;
            color: #909399;
        }
        
        .task-progress-chart-container {
            height: 200px;
            margin-bottom: 20px;
        }
        
        .progress-glow {
            box-shadow: 0 0 10px rgba(64, 158, 255, 0.6);
        }
        
        .task-activity-timeline {
            margin-top: 15px;
            position: relative;
            padding-left: 20px;
        }
        
        .timeline-line {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            width: 2px;
            background-color: #DCDFE6;
        }
        
        .timeline-item {
            position: relative;
            padding-bottom: 20px;
            padding-left: 20px;
        }
        
        .timeline-dot {
            position: absolute;
            left: -5px;
            top: 0;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #409EFF;
        }
        
        .timeline-item:last-child {
            padding-bottom: 0;
        }
        
        .timeline-content {
            background-color: white;
            padding: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
        }
        
        .timeline-timestamp {
            font-size: 12px;
            color: #909399;
        }
        
        .timeline-message {
            margin-top: 5px;
        }
        
        .floating-stats {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }
        
        .floating-stats.hidden {
            transform: translateX(120%);
        }
        
        .mini-chart-container {
            width: 60px;
            height: 40px;
            margin-right: 15px;
        }
        
        .mini-stats-info {
            display: flex;
            flex-direction: column;
        }
        
        .mini-stats-value {
            font-weight: bold;
            font-size: 18px;
        }
        
        .mini-stats-label {
            font-size: 12px;
            color: #909399;
        }
        
        @media (max-width: 768px) {
            .app-container {
                padding: 10px;
            }
            .app-header h1 {
                font-size: 1.5rem;
            }
            .task-stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div id="app" v-cloak>
        <div class="app-container">
            <div class="app-header animate__animated animate__fadeIn">
                <h1>爬虫引擎</h1>
            </div>
            
            <!-- 系统状态卡片 -->
            <el-card class="status-card">
                <template #header>
                    <div style="display: flex; justify-content: space-between; align-items: center">
                        <span>系统状态</span>
                        <div>
                            <el-tag :type="connectionStatus ? 'success' : 'danger'" size="small" style="margin-right: 10px">
                                {{ connectionStatus ? '已连接' : '未连接' }}
                            </el-tag>
                            <el-button 
                                :icon="Refresh" 
                                size="small" 
                                :loading="refreshing" 
                                @click="refreshStatus"
                            >刷新</el-button>
                        </div>
                    </div>
                </template>
                <el-row :gutter="20">
                    <el-col :span="8">
                        <div class="stat-card" :class="{ 'pulse-animation': systemStatus === 'busy' }">
                            <div class="stat-icon">
                                <el-icon v-if="systemStatus === 'idle'"><Check /></el-icon>
                                <el-icon v-else-if="systemStatus === 'busy'" class="spin"><Loading /></el-icon>
                                <el-icon v-else><WarningFilled /></el-icon>
                            </div>
                            <div class="stat-value">
                                <span :class="{
                                    'status-badge': true,
                                    'status-badge-idle': systemStatus === 'idle',
                                    'status-badge-busy': systemStatus === 'busy',
                                    'status-badge-error': systemStatus === 'error'
                                }">{{ statusText }}</span>
                            </div>
                            <div class="stat-label">系统当前状态</div>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <el-icon><Connection /></el-icon>
                            </div>
                            <div class="stat-value">
                                <span class="animate__animated" :class="queueStatusClass">{{ queueStatus }}</span>
                            </div>
                            <div class="stat-label">消息队列状态</div>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <el-icon><Timer /></el-icon>
                            </div>
                            <div class="stat-value">{{ lastRunRelative }}</div>
                            <div class="stat-label">上次执行: {{ lastRun }}</div>
                        </div>
                    </el-col>
                </el-row>
                
                <!-- 账号等级信息 -->
                <el-row style="margin-top: 15px" v-if="accountLevel && accountLevel !== 'normal'">
                    <el-col :span="24">
                        <el-alert
                            :title="'当前账号等级: ' + (accountLevel === 'vip' ? 'VIP会员' : '普通账号')"
                            :type="accountLevel === 'vip' ? 'success' : 'info'"
                            :description="'单次任务最多可处理 ' + dataLimit + ' 条数据'"
                            show-icon
                        />
                    </el-col>
                </el-row>
            </el-card>
            
            <el-row :gutter="20">
                <!-- 左侧任务配置 -->
                <el-col :xs="24" :sm="24" :md="12">
                    <el-card class="task-form-card">
                        <template #header>
                            <div>
                                <span>任务配置</span>
                            </div>
                        </template>
                        
                        <el-form 
                            ref="taskFormRef" 
                            :model="taskForm" 
                            :rules="taskFormRules" 
                            label-position="top"
                        >
                            <el-form-item label="任务名称" prop="name">
                                <el-input 
                                    v-model="taskForm.name" 
                                    placeholder="为任务命名，便于识别" 
                                />
                            </el-form-item>
                            
                            <el-form-item label="搜索规则" prop="rule">
                                <el-input 
                                    v-model="taskForm.rule" 
                                    placeholder="输入搜索关键词，如：国网" 
                                />
                            </el-form-item>
                            
                            <el-form-item label="时间范围" prop="timeRange">
                                <div style="margin-bottom: 10px">
                                    <span style="margin-right: 10px">预设:</span>
                                    <el-button-group>
                                        <el-button size="small" @click="setTimeRangePreset('today')">今天</el-button>
                                        <el-button size="small" @click="setTimeRangePreset('yesterday')">昨天</el-button>
                                        <el-button size="small" @click="setTimeRangePreset('thisWeek')">本周</el-button>
                                        <el-button size="small" @click="setTimeRangePreset('lastWeek')">上周</el-button>
                                    </el-button-group>
                                </div>
                                <el-input 
                                    v-model="taskForm.timeRange" 
                                    placeholder="格式: YYYY-MM-DD HH:MM:SS,YYYY-MM-DD HH:MM:SS" 
                                />
                            </el-form-item>
                            
                            <el-form-item>
                                <el-button-group>
                                    <el-button 
                                        type="primary" 
                                        :icon="VideoPlay" 
                                        :loading="submitting" 
                                        :disabled="submitCountdown > 0"
                                        @click="submitTask"
                                    >{{ submitCountdown > 0 ? \`发送任务(\${submitCountdown}s)\` : '发送任务' }}</el-button>
                                    <el-button 
                                        :icon="Delete" 
                                        @click="clearForm"
                                    >清空表单</el-button>
                                </el-button-group>
                            </el-form-item>
                        </el-form>
                    </el-card>
                </el-col>
                
                <!-- 右侧日志 -->
                <el-col :xs="24" :sm="24" :md="12">
                    <el-card class="logs-card">
                        <template #header>
                            <div style="display: flex; justify-content: space-between; align-items: center">
                                <span>任务执行日志</span>
                                <div>
                                    <el-switch
                                        v-model="autoScroll"
                                        active-text="自动滚动"
                                        inactive-text="手动滚动"
                                        style="margin-right: 10px"
                                    />
                                    <el-button 
                                        :icon="Delete" 
                                        size="small" 
                                        @click="clearLogs"
                                    >清空日志</el-button>
                                </div>
                            </div>
                        </template>
                        
                        <div class="console-wrapper" ref="logContainer">
                            <div 
                                v-for="(log, index) in logs" 
                                :key="index" 
                                class="console-line"
                                :class="{'animate__animated animate__fadeInUp': index >= logs.length - 3}"
                            >
                                <span class="console-timestamp">[{{ log.time }}]</span>
                                <span :class="'console-' + log.type">{{ log.message }}</span>
                            </div>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
            
            <!-- 任务状态统计 - 占满宽度 -->
            <el-card class="task-stats-card" style="margin-top: 20px;">
                <template #header>
                    <div style="display: flex; justify-content: space-between; align-items: center">
                        <span>当前任务</span>
                        <div>
                            <el-tag :type="connectionStatus ? 'success' : 'danger'" size="small" style="margin-right: 10px">
                                {{ connectionStatus ? '数据正常' : '连接断开' }}
                            </el-tag>
                            <el-button 
                                :icon="Refresh" 
                                size="small" 
                                :loading="refreshing" 
                                @click="refreshStatus"
                            >刷新状态</el-button>
                        </div>
                    </div>
                </template>
                
                <!-- 任务统计数据 -->
                <el-row :gutter="20">
                    <el-col :span="4">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <el-icon><Document /></el-icon>
                            </div>
                            <div class="stat-value animate__animated animate__fadeIn">
                                {{ currentTaskName || '无任务' }}
                            </div>
                            <div class="stat-label">当前任务</div>
                        </div>
                    </el-col>
                    
                    <el-col :span="4">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <el-icon><Timer /></el-icon>
                            </div>
                            <div class="stat-value animate__animated animate__fadeIn">
                                {{ elapsedTime || '00:00:00' }}
                            </div>
                            <div class="stat-label">已耗时</div>
                        </div>
                    </el-col>
                    
                    <el-col :span="4">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <el-icon><DataAnalysis /></el-icon>
                            </div>
                            <div class="stat-value animate__animated animate__fadeIn">
                                {{ currentPage || 0 }}
                            </div>
                            <div class="stat-label">当前页</div>
                        </div>
                    </el-col>
                    
                    <el-col :span="4">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <el-icon><DataAnalysis /></el-icon>
                            </div>
                            <div class="stat-value animate__animated animate__fadeIn">
                                {{ totalPages || 0 }}
                            </div>
                            <div class="stat-label">总页数</div>
                        </div>
                    </el-col>

                    <el-col :span="4">
                        <div class="stat-card">
                            <div class="stat-icon" style="color: #67C23A">
                                <el-icon><DataAnalysis /></el-icon>
                            </div>
                            <div class="stat-value animate__animated animate__fadeIn" style="color: #67C23A">
                                {{ itemsInRange || 0 }}
                            </div>
                            <div class="stat-label">有效数据</div>
                        </div>
                    </el-col>
                    
                    <el-col :span="4">
                        <div class="stat-card">
                            <div class="stat-icon" style="color: #E6A23C">
                                <el-icon><DataAnalysis /></el-icon>
                            </div>
                            <div class="stat-value animate__animated animate__fadeIn" style="color: #E6A23C">
                                {{ duplicateItems || 0 }}
                            </div>
                            <div class="stat-label">重复数据</div>
                        </div>
                    </el-col>
                </el-row>
            </el-card>
            
            <!-- 悬浮状态卡片 -->
            <div v-if="taskRunning" 
                class="floating-stats animate__animated animate__fadeInRight"
                :class="{ 'hidden': !showFloatingStats }">
                <div class="mini-chart-container">
                    <canvas ref="miniChart"></canvas>
                </div>
                <div class="mini-stats-info">
                    <div class="mini-stats-value">{{ processedCount }} 项</div>
                    <div class="mini-stats-label">{{ elapsedTime }}</div>
                </div>
                <el-button 
                    @click="showFloatingStats = !showFloatingStats"
                    circle
                    size="small"
                    :icon="ArrowRight"
                    style="position: absolute; left: -15px; top: 50%; transform: translateY(-50%);"
                ></el-button>
            </div>
        </div>
    </div>
    
    <script type="module">
        const { createApp, ref, reactive, computed, onMounted, onBeforeUnmount, watch, nextTick } = Vue;
        const { ElMessageBox, ElMessage, ElNotification, ElTag, ElButton, ElRow, ElCol, ElCard, ElForm, ElFormItem, ElInput, ElButtonGroup, ElSwitch, ElIcon, ElAlert } = ElementPlus;
        
        const App = {
            template: `
                <div class="app-container">
                    <!-- 头部 -->
                    <div class="app-header">
                        <h1>爬虫引擎</h1>
                    </div>
                    
                    <!-- 系统状态卡片 -->
                    <el-card class="status-card">
                        <template #header>
                            <div style="display: flex; justify-content: space-between; align-items: center">
                                <span>系统状态</span>
                                <div>
                                    <el-tag :type="connectionStatus ? 'success' : 'danger'" size="small" style="margin-right: 10px">
                                        {{ connectionStatus ? '已连接' : '未连接' }}
                                    </el-tag>
                                    <el-button 
                                        :icon="Refresh" 
                                        size="small" 
                                        :loading="refreshing" 
                                        @click="refreshStatus"
                                    >刷新</el-button>
                                </div>
                            </div>
                        </template>
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <div class="stat-card" :class="{ 'pulse-animation': systemStatus === 'busy' }">
                                    <div class="stat-icon">
                                        <el-icon v-if="systemStatus === 'idle'"><Check /></el-icon>
                                        <el-icon v-else-if="systemStatus === 'busy'" class="spin"><Loading /></el-icon>
                                        <el-icon v-else><WarningFilled /></el-icon>
                                    </div>
                                    <div class="stat-value">
                                        <span :class="{
                                            'status-badge': true,
                                            'status-badge-idle': systemStatus === 'idle',
                                            'status-badge-busy': systemStatus === 'busy',
                                            'status-badge-error': systemStatus === 'error'
                                        }">{{ statusText }}</span>
                                    </div>
                                    <div class="stat-label">系统当前状态</div>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <el-icon><Connection /></el-icon>
                                    </div>
                                    <div class="stat-value">
                                        <span class="animate__animated" :class="queueStatusClass">{{ queueStatus }}</span>
                                    </div>
                                    <div class="stat-label">消息队列状态</div>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <el-icon><Timer /></el-icon>
                                    </div>
                                    <div class="stat-value">{{ lastRunRelative }}</div>
                                    <div class="stat-label">上次执行: {{ lastRun }}</div>
                                </div>
                            </el-col>
                        </el-row>
                        
                        <!-- 账号等级信息 -->
                        <el-row style="margin-top: 15px" v-if="accountLevel && accountLevel !== 'normal'">
                            <el-col :span="24">
                                <el-alert
                                    :title="'当前账号等级: ' + (accountLevel === 'vip' ? 'VIP会员' : '普通账号')"
                                    :type="accountLevel === 'vip' ? 'success' : 'info'"
                                    :description="'单次任务最多可处理 ' + dataLimit + ' 条数据'"
                                    show-icon
                                />
                            </el-col>
                        </el-row>
                    </el-card>
                    
                    <el-row :gutter="20">
                        <!-- 左侧任务配置 -->
                        <el-col :xs="24" :sm="24" :md="12">
                            <el-card class="task-form-card">
                                <template #header>
                                    <div>
                                        <span>任务配置</span>
                                    </div>
                                </template>
                                
                                <el-form 
                                    ref="taskFormRef" 
                                    :model="taskForm" 
                                    :rules="taskFormRules" 
                                    label-position="top"
                                >
                                    <el-form-item label="任务名称" prop="name">
                                        <el-input 
                                            v-model="taskForm.name" 
                                            placeholder="为任务命名，便于识别" 
                                        />
                                    </el-form-item>
                                    
                                    <el-form-item label="搜索规则" prop="rule">
                                        <el-input 
                                            v-model="taskForm.rule" 
                                            placeholder="输入搜索关键词，如：国网" 
                                        />
                                    </el-form-item>
                                    
                                    <el-form-item label="时间范围" prop="timeRange">
                                        <div style="margin-bottom: 10px">
                                            <span style="margin-right: 10px">预设:</span>
                                            <el-button-group>
                                                <el-button size="small" @click="setTimeRangePreset('today')">今天</el-button>
                                                <el-button size="small" @click="setTimeRangePreset('yesterday')">昨天</el-button>
                                                <el-button size="small" @click="setTimeRangePreset('thisWeek')">本周</el-button>
                                                <el-button size="small" @click="setTimeRangePreset('lastWeek')">上周</el-button>
                                            </el-button-group>
                                        </div>
                                        <el-input 
                                            v-model="taskForm.timeRange" 
                                            placeholder="格式: YYYY-MM-DD HH:MM:SS,YYYY-MM-DD HH:MM:SS" 
                                        />
                                    </el-form-item>
                                    
                                    <el-form-item>
                                        <el-button-group>
                                            <el-button 
                                                type="primary" 
                                                :icon="VideoPlay" 
                                                :loading="submitting" 
                                                :disabled="submitCountdown > 0"
                                                @click="submitTask"
                                            >{{ submitCountdown > 0 ? \`发送任务(\${submitCountdown}s)\` : '发送任务' }}</el-button>
                                            <el-button 
                                                :icon="Delete" 
                                                @click="clearForm"
                                            >清空表单</el-button>
                                        </el-button-group>
                                    </el-form-item>
                                </el-form>
                            </el-card>
                        </el-col>
                        
                        <!-- 右侧日志 -->
                        <el-col :xs="24" :sm="24" :md="12">
                            <el-card class="logs-card">
                                <template #header>
                                    <div style="display: flex; justify-content: space-between; align-items: center">
                                        <span>任务执行日志</span>
                                        <div>
                                            <el-switch
                                                v-model="autoScroll"
                                                active-text="自动滚动"
                                                inactive-text="手动滚动"
                                                style="margin-right: 10px"
                                            />
                                            <el-button 
                                                :icon="Delete" 
                                                size="small" 
                                                @click="clearLogs"
                                            >清空日志</el-button>
                                        </div>
                                    </div>
                                </template>
                                
                                <div class="console-wrapper" ref="logContainer">
                                    <div 
                                        v-for="(log, index) in logs" 
                                        :key="index" 
                                        class="console-line"
                                        :class="{'animate__animated animate__fadeInUp': index >= logs.length - 3}"
                                    >
                                        <span class="console-timestamp">[{{ log.time }}]</span>
                                        <span :class="'console-' + log.type">{{ log.message }}</span>
                                    </div>
                                </div>
                            </el-card>
                        </el-col>
                    </el-row>
                    
                    <!-- 任务状态统计 - 占满宽度 -->
                    <el-card class="task-stats-card" style="margin-top: 20px;">
                        <template #header>
                            <div style="display: flex; justify-content: space-between; align-items: center">
                                <span>当前任务</span>
                                <div>
                                    <el-tag :type="connectionStatus ? 'success' : 'danger'" size="small" style="margin-right: 10px">
                                        {{ connectionStatus ? '数据正常' : '连接断开' }}
                                    </el-tag>
                                    <el-button 
                                        :icon="Refresh" 
                                        size="small" 
                                        :loading="refreshing" 
                                        @click="refreshStatus"
                                    >刷新状态</el-button>
                                </div>
                            </div>
                        </template>
                        
                        <!-- 任务统计数据 -->
                        <el-row :gutter="20">
                            <el-col :span="4">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <el-icon><Document /></el-icon>
                                    </div>
                                    <div class="stat-value animate__animated animate__fadeIn">
                                        {{ currentTaskName || '无任务' }}
                                    </div>
                                    <div class="stat-label">当前任务</div>
                                </div>
                            </el-col>
                            
                            <el-col :span="4">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <el-icon><Timer /></el-icon>
                                    </div>
                                    <div class="stat-value animate__animated animate__fadeIn">
                                        {{ elapsedTime || '00:00:00' }}
                                    </div>
                                    <div class="stat-label">已耗时</div>
                                </div>
                            </el-col>
                            
                            <el-col :span="4">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <el-icon><DataAnalysis /></el-icon>
                                    </div>
                                    <div class="stat-value animate__animated animate__fadeIn">
                                        {{ currentPage || 0 }}
                                    </div>
                                    <div class="stat-label">当前页</div>
                                </div>
                            </el-col>
                            
                            <el-col :span="4">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <el-icon><DataAnalysis /></el-icon>
                                    </div>
                                    <div class="stat-value animate__animated animate__fadeIn">
                                        {{ totalPages || 0 }}
                                    </div>
                                    <div class="stat-label">总页数</div>
                                </div>
                            </el-col>

                            <el-col :span="4">
                                <div class="stat-card">
                                    <div class="stat-icon" style="color: #67C23A">
                                        <el-icon><DataAnalysis /></el-icon>
                                    </div>
                                    <div class="stat-value animate__animated animate__fadeIn" style="color: #67C23A">
                                        {{ itemsInRange || 0 }}
                                    </div>
                                    <div class="stat-label">有效数据</div>
                                </div>
                            </el-col>
                            
                            <el-col :span="4">
                                <div class="stat-card">
                                    <div class="stat-icon" style="color: #E6A23C">
                                        <el-icon><DataAnalysis /></el-icon>
                                    </div>
                                    <div class="stat-value animate__animated animate__fadeIn" style="color: #E6A23C">
                                        {{ duplicateItems || 0 }}
                                    </div>
                                    <div class="stat-label">重复数据</div>
                                </div>
                            </el-col>
                        </el-row>
                    </el-card>
                    
                    <!-- 悬浮状态卡片 -->
                    <div v-if="taskRunning" 
                        class="floating-stats animate__animated animate__fadeInRight"
                        :class="{ 'hidden': !showFloatingStats }">
                        <div class="mini-chart-container">
                            <canvas ref="miniChart"></canvas>
                        </div>
                        <div class="mini-stats-info">
                            <div class="mini-stats-value">{{ processedCount }} 项</div>
                            <div class="mini-stats-label">{{ elapsedTime }}</div>
                        </div>
                        <el-button 
                            @click="showFloatingStats = !showFloatingStats"
                            circle
                            size="small"
                            :icon="ArrowRight"
                            style="position: absolute; left: -15px; top: 50%; transform: translateY(-50%);"
                        ></el-button>
                    </div>
                </div>
            `,
            
            setup() {
                // 导入Element Plus图标
                const { Refresh, VideoPlay, Delete, Check, Loading, WarningFilled, Timer, 
                        Connection, Document, DataAnalysis, ArrowRight } = ElementPlusIconsVue;
                
                // 响应式状态
                const isMobile = ref(window.innerWidth < 768);
                const refreshing = ref(false);
                const submitting = ref(false);
                const submitCountdown = ref(0); // 添加提交倒计时变量
                const autoScroll = ref(true);
                const showFloatingStats = ref(true);
                const connectionStatus = ref(false); // 后端连接状态
                const connectionRetries = ref(0); // 连接重试次数
                const maxRetries = 3; // 最大重试次数
                
                // 图表引用
                const progressChart = ref(null);
                const miniChart = ref(null);
                let chartInstance = null;
                let miniChartInstance = null;
                
                // 系统状态
                const systemStatus = ref('idle'); // idle, busy, error
                const statusText = computed(() => {
                    const statusMap = {
                        'idle': '空闲中',
                        'busy': '处理任务中',
                        'error': '系统错误'
                    };
                    return statusMap[systemStatus.value] || '未知状态';
                });
                
                const statusType = computed(() => {
                    const typeMap = {
                        'idle': 'success',
                        'busy': 'warning',
                        'error': 'danger'
                    };
                    return typeMap[systemStatus.value] || 'info';
                });
                
                const queueStatus = ref('正常');
                const queueStatusClass = computed(() => {
                    return systemStatus.value === 'busy' ? 'animate__pulse animate__infinite' : '';
                });
                
                const lastRun = ref('暂无执行记录');
                const lastRunSuccess = ref(false);
                const lastRunRelative = ref('从未执行');
                
                // 任务表单
                const taskFormRef = ref(null);
                const taskForm = reactive({
                    name: '',
                    rule: '',
                    timeRange: ''
                });
                
                const taskFormRules = {
                    name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
                    rule: [{ required: true, message: '请输入搜索规则', trigger: 'blur' }],
                    timeRange: [
                        { required: true, message: '请输入时间范围', trigger: 'blur' },
                        { validator: validateTimeRange, trigger: 'blur' }
                    ]
                };
                
                // 任务状态
                const taskRunning = ref(false);
                const currentTaskName = ref('');
                const taskStatus = ref('初始化中');
                const tagType = computed(() => {
                    if (taskStatus.value.includes('完成')) return 'success';
                    if (taskStatus.value.includes('错误') || taskStatus.value.includes('失败')) return 'danger';
                    if (taskStatus.value.includes('等待') || taskStatus.value.includes('准备')) return 'info';
                    return 'warning'; // 处理中状态
                });
                
                const processedCount = ref(0);
                const totalCount = ref('计算中');
                const startTime = ref(null);
                const elapsedTime = ref('00:00:00');
                const progressPercent = ref(0);
                const progressText = ref('正在准备任务...');
                const remainingTime = ref('剩余时间: 计算中');
                const progressDetails = ref('等待任务处理信息...');
                
                // 新增统计数据
                const itemsInRange = ref(0);
                const itemsOutOfRange = ref(0);
                const duplicateItems = ref(0);
                const currentPage = ref(0);
                const totalPages = ref(0);
                
                // 任务活动时间线
                const taskActivities = ref([]);
                
                // 图表数据
                const chartData = reactive({
                    processedHistory: [0],
                    itemsInRangeHistory: [0],
                    labels: ['开始']
                });
                
                // 日志
                const logContainer = ref(null);
                const logs = ref([
                    { time: formatTime(new Date()), message: '系统已就绪，等待任务启动...', type: '' }
                ]);
                
                // 定时器
                const timers = reactive({
                    elapsedTime: null,
                    pollStatus: null,
                    updateRelativeTime: null,
                    localTimeUpdate: null // 本地时间更新计时器
                });
                
                // 记录上次服务器返回的时间信息
                const serverTimeInfo = reactive({
                    lastServerTime: null,  // 上次服务器返回的执行时间
                    baseTimestamp: null,   // 接收到服务器时间时的本地时间戳
                    serverElapsed: 0,      // 服务器返回的执行时间（秒）
                    lastDisplayedTime: 0,  // 上次显示的时间值
                    targetTime: 0,         // 目标服务器时间值
                    transitionStartTime: 0,// 开始平滑过渡的时间戳
                    inTransition: false    // 是否正在平滑过渡中
                });
                
                // 添加缺失的变量定义
                const accountLevel = ref('normal');
                const dataLimit = ref(500);
                
                // 方法
                function handleResize() {
                    isMobile.value = window.innerWidth < 768;
                }
                
                function checkConnection() {
                    axios.get('/api/test/status', { timeout: 5000 })
                        .then(() => {
                            connectionStatus.value = true;
                            connectionRetries.value = 0;
                        })
                        .catch(() => {
                            connectionStatus.value = false;
                            connectionRetries.value++;
                            
                            // 重试次数超过限制，显示错误
                            if (connectionRetries.value > maxRetries) {
                                ElMessage.error('无法连接到后端服务，请检查网络或联系管理员');
                                addLog('无法连接到后端服务，请检查网络或重启应用', 'error');
                            } else {
                                // 继续尝试重连
                                setTimeout(checkConnection, 5000);
                            }
                        });
                }
                
                function refreshStatus(silent = false) {
                    refreshing.value = true;
                    
                    if (!silent) {
                        addLog('正在刷新系统状态...');
                    }
                    
                    return new Promise((resolve, reject) => {
                        axios.get('/api/test/status', { timeout: 10000 })
                            .then(response => {
                                if (response.data) {
                                    console.log('收到状态更新:', response.data);
                                    
                                    // 更新系统状态
                                    systemStatus.value = response.data.activeTaskCount > 0 ? 'busy' : 'idle';
                                    
                                    // 更新任务状态
                                    if (response.data.currentTask) {
                                        const task = response.data.currentTask;
                                        
                                        // 更新任务运行状态
                                        taskRunning.value = true;
                                        
                                        // 更新任务基本信息
                                        currentTaskName.value = task.name || '';
                                        taskStatus.value = task.progress || '处理中';
                                        processedCount.value = task.processedItems || 0;
                                        totalCount.value = task.totalPages || '计算中';
                                        elapsedTime.value = task.executionTime || '00:00:00';
                                        
                                        // 更新进度信息
                                        if (task.currentPage && task.totalPages) {
                                            progressPercent.value = Math.round((task.currentPage / task.totalPages) * 100);
                                        }
                                        progressText.value = task.progress || '正在处理...';
                                        
                                        // 更新剩余时间
                                        if (task.estimatedRemainingTime !== undefined) {
                                            const minutes = Math.floor(task.estimatedRemainingTime / 60);
                                            const seconds = task.estimatedRemainingTime % 60;
                                            remainingTime.value = `剩余时间: ${minutes}分${seconds}秒`;
                                        }
                                        
                                        // 更新统计数据
                                        itemsInRange.value = task.itemsInRange || 0;
                                        itemsOutOfRange.value = task.itemsOutOfRange || 0;
                                        duplicateItems.value = task.duplicateItems || 0;
                                        currentPage.value = task.currentPage || 0;
                                        totalPages.value = task.totalPages || 0;
                                        
                                        // 更新日志
                                        if (task.logs && task.logs.length > 0) {
                                            task.logs.forEach(log => {
                                                if (log && log.message) {
                                                    addLog(log.message, log.type || '');
                                                }
                                            });
                                        }
                                    } else {
                                        // 没有运行中的任务，重置状态
                                        taskRunning.value = false;
                                        currentTaskName.value = '';
                                        taskStatus.value = '无任务';
                                        processedCount.value = 0;
                                        totalCount.value = '计算中';
                                        progressPercent.value = 0;
                                        progressText.value = '';
                                        elapsedTime.value = '00:00:00';
                                        remainingTime.value = '';
                                        itemsInRange.value = 0;
                                        itemsOutOfRange.value = 0;
                                        duplicateItems.value = 0;
                                        currentPage.value = 0;
                                        totalPages.value = 0;
                                    }
                                    
                                    // 更新账号等级和数据限制
                                    if (response.data.accountLevel) {
                                        accountLevel.value = response.data.accountLevel;
                                    }
                                    if (response.data.dataLimit) {
                                        dataLimit.value = response.data.dataLimit;
                                    }
                                }
                                
                                if (!silent) {
                                    addLog('系统状态已刷新', 'success');
                                }
                                
                                resolve();
                            })
                            .catch(error => {
                                console.error('刷新状态失败:', error);
                                if (!silent) {
                                    addLog(`刷新状态失败: ${error.message}`, 'error');
                                }
                                reject(error);
                            })
                            .finally(() => {
                                refreshing.value = false;
                            });
                    });
                }
                
                function validateTimeRange(rule, value, callback) {
                    if (!value) {
                        return callback(new Error('请输入时间范围'));
                    }
                    
                    const regex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
                    if (!regex.test(value)) {
                        return callback(new Error('时间格式不正确，请使用YYYY-MM-DD HH:MM:SS,YYYY-MM-DD HH:MM:SS格式'));
                    }
                    
                    const [startStr, endStr] = value.split(',');
                    const start = new Date(startStr);
                    const end = new Date(endStr);
                    
                    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
                        return callback(new Error('无效的日期'));
                    }
                    
                    if (start > end) {
                        return callback(new Error('开始时间不能晚于结束时间'));
                    }
                    
                    callback();
                }
                
                function submitTask() {
                    taskFormRef.value.validate((valid) => {
                        if (!valid) {
                            ElMessage.warning('请正确填写表单');
                            return;
                        }
                        
                        submitting.value = true;
                        addLog(`正在提交任务: ${taskForm.name}`);
                        addLog(`搜索规则: ${taskForm.rule}`);
                        addLog(`时间范围: ${taskForm.timeRange}`);
                        
                        // 记录提交时间，用于计算耗时
                        const submitTime = new Date();
                        
                        // 修改为实际后端API路径
                        axios.post('/api/test/send-task', {
                            name: taskForm.name,
                            rule: taskForm.rule,
                            timeRange: taskForm.timeRange
                        }, { 
                            timeout: 30000,
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        })
                        .then(response => {
                            console.log('任务提交响应:', response.data);
                            
                            // 更加宽松的响应判断
                            if (response.data) {
                                ElMessage.success('任务提交成功');
                                addLog(`任务已提交并放入队列`, 'success');
                                
                                systemStatus.value = 'busy';
                                
                                // 启动任务追踪
                                startTaskTracker();
                                
                                // 添加5秒倒计时
                                startSubmitCountdown();
                            } else {
                                ElMessage.warning('任务提交成功但返回数据异常');
                                addLog(`任务提交成功但返回数据异常`, 'warning');
                                submitting.value = false;
                            }
                        })
                        .catch(error => {
                            console.error('任务提交错误:', error);
                            ElMessage.error('任务提交错误: ' + error.message);
                            addLog(`任务提交错误: ${error.message}`, 'error');
                            submitting.value = false;
                            
                            // 在错误发生15秒后尝试恢复提交状态（防止永久转圈）
                            setTimeout(() => {
                                if (submitting.value) {
                                    submitting.value = false;
                                    addLog('自动重置提交状态', 'warning');
                                }
                            }, 15000);
                        });
                    });
                }
                
                // 添加提交按钮5秒倒计时函数
                function startSubmitCountdown() {
                    // 倒计时从5秒开始
                    submitCountdown.value = 5;
                    submitting.value = false;
                    
                    // 创建倒计时定时器
                    const countdownTimer = setInterval(() => {
                        submitCountdown.value--;
                        
                        // 当倒计时结束时
                        if (submitCountdown.value <= 0) {
                            clearInterval(countdownTimer);
                            submitCountdown.value = 0;
                        }
                    }, 1000);
                }
                
                function setTimeRangePreset(preset) {
                    const now = new Date();
                    let startDate, endDate;
                    
                    switch (preset) {
                        case 'today':
                            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
                            endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
                            break;
                        case 'yesterday':
                            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 0, 0, 0);
                            endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 23, 59, 59);
                            break;
                        case 'thisWeek':
                            // 计算本周的星期一
                            const dayOfWeek = now.getDay() || 7; // 获取星期几 (0-6)，星期天转为7
                            const mondayDiff = dayOfWeek - 1; // 计算与星期一的天数差
                            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - mondayDiff, 0, 0, 0);
                            endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + (7 - dayOfWeek), 23, 59, 59);
                            break;
                        case 'lastWeek':
                            // 计算上周的星期一和星期日
                            const lastMondayDiff = (now.getDay() || 7) + 6; // 上周一与今天的天数差
                            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - lastMondayDiff, 0, 0, 0);
                            endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - lastMondayDiff + 6, 23, 59, 59);
                            break;
                    }
                    
                    // 设置时间范围
                    if (startDate && endDate) {
                        taskForm.timeRange = `${formatDateTime(startDate)},${formatDateTime(endDate)}`;
                        addLog(`已设置时间范围为: ${preset}`);
                    }
                }
                
                function clearForm() {
                    taskFormRef.value.resetFields();
                    addLog('表单已清空');
                }
                
                function addLog(message, type = '') {
                    logs.value.push({
                        time: formatTime(new Date()),
                        message: message,
                        type: type
                    });
                    
                    // 限制日志数量
                    if (logs.value.length > 100) {
                        logs.value.shift();
                    }
                    
                    // 更新时间线活动
                    if (taskRunning.value && 
                        (type === 'success' || type === 'error' || type === 'warning' || 
                         message.includes('页') || message.includes('搜索') || message.includes('数据'))) {
                        addTimelineActivity(message, type);
                    }
                }
                
                function addTimelineActivity(message, type = '') {
                    // 限制时间线活动条目，只保留最近的几条
                    if (taskActivities.value.length > 5) {
                        taskActivities.value.shift();
                    }
                    
                    taskActivities.value.push({
                        time: formatTime(new Date()),
                        message: message,
                        type: type
                    });
                }
                
                function scrollLogsToBottom() {
                    if (logContainer.value) {
                        logContainer.value.scrollTop = logContainer.value.scrollHeight;
                    }
                }
                
                function clearLogs() {
                    logs.value = [{ 
                        time: formatTime(new Date()), 
                        message: '日志已清空', 
                        type: '' 
                    }];
                }
                
                function startTaskTracker(taskName) {
                    // 先确保清除与任务相关的计时器，但保留轮询
                    // 不再清除轮询计时器
                    // clearTimer('pollStatus');
                    clearTimer('localTimeUpdate');
                    clearTimer('elapsedTime');
                    
                    // 显示任务进度
                    taskRunning.value = true;
                    
                    // 使用传入的任务名称或表单中的名称
                    if (taskName) {
                        currentTaskName.value = taskName;
                    } else if (taskForm && taskForm.name) {
                        currentTaskName.value = taskForm.name;
                    } else {
                        currentTaskName.value = "未命名任务";
                    }
                    
                    taskStatus.value = '初始化中';
                    processedCount.value = 0;
                    totalCount.value = '计算中';
                    progressPercent.value = 0;
                    progressText.value = '正在准备任务...';
                    remainingTime.value = '剩余时间: 计算中';
                    progressDetails.value = '等待任务处理信息...';
                    
                    // 清空统计数据
                    itemsInRange.value = 0;
                    itemsOutOfRange.value = 0;
                    duplicateItems.value = 0;
                    currentPage.value = 0;
                    totalPages.value = 0;
                    
                    // 清空时间线活动
                    taskActivities.value = [{
                        time: formatTime(new Date()),
                        message: '任务已启动',
                        type: 'success'
                    }];
                    
                    // 重置图表数据
                    chartData.processedHistory = [0];
                    chartData.itemsInRangeHistory = [0];
                    chartData.labels = ['开始'];
                    
                    // 重置时间相关信息
                    startTime.value = new Date();
                    serverTimeInfo.lastServerTime = null;
                    serverTimeInfo.baseTimestamp = startTime.value.getTime(); // 使用启动时间作为初始基准
                    serverTimeInfo.serverElapsed = 0;
                    
                    console.log('任务启动时间基准已设置:', formatDateTime(startTime.value));
                    
                    // 使用高频本地时间更新
                    startLocalTimeUpdate();
                    
                    // 如果轮询不存在，则启动状态轮询，否则保持现有轮询
                    if (!timers.pollStatus) {
                        console.log('轮询不存在，启动新轮询');
                        pollTaskStatus();
                    } else {
                        console.log('轮询已存在，保持现有轮询');
                    }
                    
                    // 初始化图表
                    nextTick(() => {
                        initChart();
                        initMiniChart();
                    });
                    
                    // 添加日志
                    addLog(`开始跟踪任务: ${currentTaskName.value}`, 'success');
                }
                
                // 更改updateElapsedTime函数，实现平滑时间过渡
                function updateElapsedTime() {
                    // 如果任务没有运行，不需要更新时间
                    if (!taskRunning.value) return;
                    
                    let currentTimeToDisplay = 0;
                    const now = new Date().getTime();
                    
                    // 如果有服务器基准时间，基于服务器基准进行补间计算
                    if (serverTimeInfo.baseTimestamp && serverTimeInfo.serverElapsed > 0) {
                        // 计算当前应该显示的时间：服务器基准秒数 + 本地流逝的秒数
                        const localElapsedSince = (now - serverTimeInfo.baseTimestamp) / 1000;
                        const calculatedTime = serverTimeInfo.serverElapsed + localElapsedSince;
                        
                        // 检查是否在过渡期
                        if (serverTimeInfo.inTransition) {
                            // 计算过渡进度 (0-1之间的值)
                            const transitionDuration = 2000; // 2秒平滑过渡
                            const elapsed = now - serverTimeInfo.transitionStartTime;
                            const progress = Math.min(elapsed / transitionDuration, 1);
                            
                            if (progress >= 1) {
                                // 过渡结束
                                serverTimeInfo.inTransition = false;
                                currentTimeToDisplay = calculatedTime;
                                console.log('平滑过渡完成:', Math.floor(currentTimeToDisplay), '秒');
                            } else {
                                // 在过渡期间，使用平滑插值
                                currentTimeToDisplay = serverTimeInfo.lastDisplayedTime + 
                                    (serverTimeInfo.targetTime - serverTimeInfo.lastDisplayedTime) * progress;
                                
                                // 确保不会倒退时间
                                currentTimeToDisplay = Math.max(currentTimeToDisplay, serverTimeInfo.lastDisplayedTime + 0.01);
                            }
                        } else {
                            // 不在过渡期，直接使用计算值（本地临时计算，每5秒会被服务器数据包校准）
                            currentTimeToDisplay = calculatedTime;
                        }
                        
                        // 只在时间变化较大时记录日志(每10秒记录一次)
                        if (Math.floor(currentTimeToDisplay) % 10 === 0 && 
                            Math.floor(currentTimeToDisplay) !== Math.floor(serverTimeInfo.lastDisplayedTime)) {
                            console.log('本地计算时间(等待服务器下次更新):', Math.floor(currentTimeToDisplay), '秒');
                        }
                    } else if (startTime.value) {
                        // 回退到基于本地开始时间的计算
                        const elapsed = (now - startTime.value.getTime()) / 1000;
                        currentTimeToDisplay = elapsed;
                        
                        // 只在时间变化较大时记录日志(每10秒记录一次)
                        if (Math.floor(currentTimeToDisplay) % 10 === 0 && 
                            Math.floor(currentTimeToDisplay) !== Math.floor(serverTimeInfo.lastDisplayedTime)) {
                            console.log('基于本地启动时间的临时计算:', Math.floor(currentTimeToDisplay), '秒');
                        }
                    } else {
                        // 没有任何时间基准
                        currentTimeToDisplay = 0;
                    }
                    
                    // 保存最后显示的时间值
                    serverTimeInfo.lastDisplayedTime = currentTimeToDisplay;
                    
                    // 更新显示 - 注意这个值会在每次服务器数据包到达时被覆盖
                    elapsedTime.value = formatDuration(currentTimeToDisplay * 1000);
                }
                
                // 更新任务执行时间处理
                function updateTaskTimeInfo(taskExecutionTime, taskElapsedSeconds) {
                    if (!taskExecutionTime || typeof taskElapsedSeconds !== 'number') return;
                    
                    // 优先使用服务器返回的格式化时间 (在数据包中直接的taskExecutionTime字符串)
                    // 只有当服务器没有提供格式化好的时间字符串，才进行本地计算
                    
                    // 获取当前本地时间
                    const now = new Date().getTime();
                    const newServerElapsed = taskElapsedSeconds;
                    
                    // 第一次接收到服务器时间，直接设置
                    if (!serverTimeInfo.baseTimestamp || !serverTimeInfo.lastServerTime) {
                        serverTimeInfo.lastServerTime = taskExecutionTime;
                        serverTimeInfo.baseTimestamp = now;
                        serverTimeInfo.serverElapsed = newServerElapsed;
                        serverTimeInfo.lastDisplayedTime = newServerElapsed;
                        console.log('首次设置时间基准:', newServerElapsed, '秒');
                        return;
                    }
                    
                    // 计算当前的本地预估时间
                    const localEstimate = serverTimeInfo.serverElapsed + (now - serverTimeInfo.baseTimestamp) / 1000;
                    
                    // 检查本地计算值与服务器值的偏差
                    const timeDifference = Math.abs(newServerElapsed - localEstimate);
                    
                    // 如果差异超过2秒，需要校准（启动平滑过渡）
                    if (timeDifference > 2) {
                        console.log('时间差异超过2秒，启动平滑过渡', 
                                    '本地计算:', localEstimate.toFixed(1), 
                                    '服务器时间:', newServerElapsed.toFixed(1),
                                    '差异:', timeDifference.toFixed(1), '秒');
                        
                        // 保存新的目标时间和过渡起点
                        serverTimeInfo.lastDisplayedTime = localEstimate; // 当前显示的时间
                        serverTimeInfo.targetTime = newServerElapsed;     // 目标时间
                        serverTimeInfo.transitionStartTime = now;        // 开始过渡的时间戳
                        serverTimeInfo.inTransition = true;              // 标记为正在过渡中
                        
                        // 同时更新服务器基准时间，以便后续本地计算更准确
                        serverTimeInfo.lastServerTime = taskExecutionTime;
                        serverTimeInfo.baseTimestamp = now;
                        serverTimeInfo.serverElapsed = newServerElapsed;
                    } else {
                        // 差异在可接受范围内，静默更新基准时间，但不启动过渡
                        // 这确保本地计时不会慢慢偏离，但也不会造成明显跳变
                        serverTimeInfo.lastServerTime = taskExecutionTime;
                        serverTimeInfo.baseTimestamp = now;
                        serverTimeInfo.serverElapsed = newServerElapsed;
                        console.log('时间差异可接受:', timeDifference.toFixed(1), '秒，静默更新基准');
                    }
                }

                function updateSystemStatus(data) {
                    if (!data) return;
                    
                    // 更新浏览器状态
                    if (data.browserStatus) {
                        if (data.browserStatus === 'BUSY') {
                            systemStatus.value = 'busy';
                        } else if (data.browserStatus === 'CLOSED') {
                            systemStatus.value = 'error';
                        } else {
                            systemStatus.value = 'idle';
                        }
                    }
                    
                    // 更新连接状态
                    connectionStatus.value = true;
                    
                    // 更新队列状态
                    if (data.consumerInfo) {
                        queueStatus.value = data.consumerInfo;
                        // 根据队列状态调整样式
                        if (queueStatus.value.includes('活跃')) {
                            queueStatusClass.value = 'animate__flash';
                        } else {
                            queueStatusClass.value = '';
                        }
                    }
                    
                    // 更新账号等级信息
                    if (data.accountLevel) {
                        accountLevel.value = data.accountLevel;
                        dataLimit.value = data.dataLimit || 0;
                    }
                    
                    // 更新上次执行时间
                    if (data.timestamp) {
                        lastRun.value = formatDateTime(new Date(data.timestamp));
                        updateLastRunRelative(new Date(data.timestamp));
                    }
                    
                    // 优先使用服务器直接提供的格式化时间字符串
                    if (data.taskExecutionTime && typeof data.taskExecutionTime === 'string' && data.taskExecutionTime.trim() !== '') {
                        // 直接使用服务器提供的已格式化时间
                        elapsedTime.value = data.taskExecutionTime;
                        console.log('使用服务器格式化时间:', data.taskExecutionTime);
                    } 
                    // 如果没有直接提供格式化时间，但提供了秒数，则更新时间基准
                    else if (data.hasOwnProperty('taskElapsedSeconds') && typeof data.taskElapsedSeconds === 'number') {
                        updateTaskTimeInfo(data.taskExecutionTime, data.taskElapsedSeconds);
                    }
                    
                    // 任务状态处理逻辑
                    // 如果有正在处理的任务且有任务名称
                    if (data.processingTask && data.currentTaskName) {
                        if (!taskRunning.value) {
                            // 新任务开始，重置和启动跟踪
                            taskRunning.value = true;
                            currentTaskName.value = data.currentTaskName;
                            startTaskTracker(data.currentTaskName);
                        } else if (currentTaskName.value !== data.currentTaskName) {
                            // 任务名称变更，更新当前任务名
                            currentTaskName.value = data.currentTaskName;
                        }
                    } 
                    // 如果后端表示没有正在处理的任务，或者明确返回任务已完成
                    else if (!data.processingTask || (data.taskProgress && data.taskProgress.includes('完成'))) {
                        // 检查是否有未完成的任务
                        if (taskRunning.value) {
                            // 任务已结束，调用finishTask完成任务
                            finishTask();
                            
                            // 显式重置当前任务名称和其他状态
                            taskRunning.value = false;
                            currentTaskName.value = '';
                            taskStatus.value = '无任务';
                            systemStatus.value = 'idle';
                            
                            // 更新统计数据
                            if (data.itemsInRange !== undefined) itemsInRange.value = data.itemsInRange;
                            if (data.itemsOutOfRange !== undefined) itemsOutOfRange.value = data.itemsOutOfRange;
                            if (data.duplicateItems !== undefined) duplicateItems.value = data.duplicateItems;
                            
                            // 重置计时状态
                            elapsedTime.value = '00:00:00';
                            
                            // 清除任务相关计时器
                            clearTimer('localTimeUpdate');
                            
                            // 重新开始轮询
                            if (timers.pollStatus) {
                                clearTimer('pollStatus');
                            }
                            console.log("任务完成，重置轮询");
                            setTimeout(() => {
                                pollTaskStatus();
                            }, 1000);
                        } else {
                            // 没有运行中的任务，直接更新状态
                            currentTaskName.value = '';
                            taskStatus.value = '无任务';
                            systemStatus.value = 'idle';
                        }
                    }
                }
                
                function updateTaskStatus(data) {
                    if (!data) return;
                    
                    // 首先检查任务是否已完成或不存在
                    if (!data.processingTask || (data.taskProgress && data.taskProgress.includes('完成'))) {
                        // 任务已完成或不存在，重置任务状态
                        taskStatus.value = '无任务';
                        currentTaskName.value = '';
                        taskRunning.value = false;
                        
                        // 记录最终统计数据（用于日志显示）
                        const finalProcessedCount = data.processedItems !== undefined ? data.processedItems : processedCount.value;
                        const finalValidItems = data.itemsInRange !== undefined ? data.itemsInRange : itemsInRange.value;
                        
                        // 清理所有相关计时器，但不清除状态轮询
                        clearTimer('localTimeUpdate');
                        
                        // 全面重置所有任务统计数据
                        processedCount.value = 0;
                        itemsInRange.value = 0;
                        itemsOutOfRange.value = 0;
                        duplicateItems.value = 0;
                        currentPage.value = 0;
                        totalPages.value = 0;
                        progressPercent.value = 0;
                        progressText.value = '';
                        elapsedTime.value = '00:00:00';
                        remainingTime.value = '';
                        
                        // 如果是刚完成的任务，记录日志
                        if (finalProcessedCount > 0) {
                            addLog(`任务完成，共处理 ${finalProcessedCount} 条数据，其中 ${finalValidItems} 条有效数据`, 'success');
                        }
                    } else {
                        // 任务正在运行，更新状态
                        taskRunning.value = true;
                        
                        // 更新任务名称
                        if (data.currentTaskName) {
                            currentTaskName.value = data.currentTaskName;
                        }
                        
                        // 更新任务状态
                        if (data.taskProgress) {
                            taskStatus.value = data.taskProgress;
                        }
                        
                        // 更新处理数量
                        if (data.processedItems !== undefined) {
                            processedCount.value = data.processedItems;
                        }
                        
                        // 更新总数量
                        if (data.totalCount !== undefined) {
                            totalCount.value = data.totalCount;
                        }
                        
                        // 更新进度百分比
                        if (data.progressPercent !== undefined) {
                            progressPercent.value = data.progressPercent;
                        }
                        
                        // 更新进度文本
                        if (data.progressText) {
                            progressText.value = data.progressText;
                        }
                        
                        // 更新剩余时间
                        if (data.remainingTime) {
                            remainingTime.value = data.remainingTime;
                        }
                        
                        // 更新执行时间
                        if (data.elapsedTime) {
                            elapsedTime.value = data.elapsedTime;
                        }
                        
                        // 更新统计数据
                        if (data.itemsInRange !== undefined) {
                            itemsInRange.value = data.itemsInRange;
                        }
                        if (data.itemsOutOfRange !== undefined) {
                            itemsOutOfRange.value = data.itemsOutOfRange;
                        }
                        if (data.duplicateItems !== undefined) {
                            duplicateItems.value = data.duplicateItems;
                        }
                        
                        // 更新当前页码和总页数
                        if (data.currentPage !== undefined) {
                            currentPage.value = data.currentPage;
                        }
                        if (data.totalPages !== undefined) {
                            totalPages.value = data.totalPages;
                        }
                    }
                }
                
                function updateChartData(processedItems, itemsInRange) {
                    try {
                        // 如果未初始化图表，则跳过更新
                        if (!chartInstance || !miniChartInstance) return;
                        
                        // 确保参数为数字，避免NaN错误
                        const processedCount = typeof processedItems === 'number' ? processedItems : 0;
                        const validItems = typeof itemsInRange === 'number' ? itemsInRange : 0;
                        
                        // 生成当前时间标签
                        const now = new Date();
                        const timeLabel = formatTime(now);
                        
                        // 克隆现有数据到临时数组以避免直接修改反应式数据
                        const newLabels = [...chartData.labels, timeLabel];
                        const newProcessed = [...chartData.processedHistory, processedCount];
                        const newSpeed = [...chartData.itemsInRangeHistory, validItems];
                        
                        // 限制数据点数量为最新的10个点
                        const maxDataPoints = 10;
                        const labels = newLabels.length > maxDataPoints ? newLabels.slice(-maxDataPoints) : newLabels;
                        const processed = newProcessed.length > maxDataPoints ? newProcessed.slice(-maxDataPoints) : newProcessed;
                        const speed = newSpeed.length > maxDataPoints ? newSpeed.slice(-maxDataPoints) : newSpeed;
                        
                        // 更新图表数据对象
                        chartData.labels = labels;
                        chartData.processedHistory = processed;
                        chartData.itemsInRangeHistory = speed;
                        
                        // 更新主图表
                        if (chartInstance && chartInstance.data) {
                            chartInstance.data.labels = labels;
                            chartInstance.data.datasets[0].data = processed;
                            chartInstance.data.datasets[1].data = speed;
                            
                            // 禁用动画以提高性能
                            chartInstance.options.animation = false;
                            chartInstance.update();
                        }
                        
                        // 更新小图表
                        if (miniChartInstance && miniChartInstance.data) {
                            miniChartInstance.data.labels = labels;
                            miniChartInstance.data.datasets[0].data = processed;
                            
                            // 禁用动画以提高性能
                            miniChartInstance.options.animation = false;
                            miniChartInstance.update();
                        }
                        
                        // 恢复动画设置
                        if (chartInstance) chartInstance.options.animation = true;
                        if (miniChartInstance) miniChartInstance.options.animation = true;
                    } catch (error) {
                        console.error('更新图表数据时出错:', error);
                    }
                }
                
                function initChart() {
                    if (chartInstance) {
                        chartInstance.destroy();
                    }
                    
                    if (!progressChart.value) return;
                    
                    const ctx = progressChart.value.getContext('2d');
                    chartInstance = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: chartData.labels,
                            datasets: [
                                {
                                    label: '已处理项目',
                                    data: chartData.processedHistory,
                                    borderColor: '#409EFF',
                                    backgroundColor: 'rgba(64, 158, 255, 0.1)',
                                    tension: 0.4,
                                    fill: true
                                },
                                {
                                    label: '有效数据',
                                    data: chartData.itemsInRangeHistory,
                                    borderColor: '#67C23A',
                                    backgroundColor: 'rgba(103, 194, 58, 0.1)',
                                    tension: 0.4,
                                    fill: true
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            animation: {
                                duration: 1000
                            },
                            plugins: {
                                legend: {
                                    position: 'top',
                                },
                                tooltip: {
                                    mode: 'index',
                                    intersect: false
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                }
                
                function initMiniChart() {
                    if (miniChartInstance) {
                        miniChartInstance.destroy();
                    }
                    
                    if (!miniChart.value) return;
                    
                    const ctx = miniChart.value.getContext('2d');
                    miniChartInstance = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: chartData.labels,
                            datasets: [
                                {
                                    data: chartData.processedHistory,
                                    borderColor: '#409EFF',
                                    backgroundColor: 'rgba(64, 158, 255, 0.1)',
                                    tension: 0.4,
                                    fill: true,
                                    pointRadius: 0
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                },
                                tooltip: {
                                    enabled: false
                                }
                            },
                            scales: {
                                x: {
                                    display: false
                                },
                                y: {
                                    display: false,
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                }
                
                function finishTask() {
                    // 清除本地时间更新
                    clearTimer('localTimeUpdate');
                    // 清除所有其他任务相关计时器（但不清除状态轮询）
                    clearTimer('elapsedTime');
                    
                    // 立即停止任务状态
                    taskRunning.value = false;
                    
                    // 记录任务结束前的统计数据（用于日志显示）
                    const finalProcessedCount = processedCount.value;
                    const finalValidItems = itemsInRange.value;
                    const taskNameForLog = currentTaskName.value || '当前任务';
                    
                    // 重置服务器时间信息
                    serverTimeInfo.lastServerTime = null;
                    serverTimeInfo.baseTimestamp = null;
                    serverTimeInfo.serverElapsed = 0;
                    
                    // 重置所有任务相关统计数据
                    processedCount.value = 0;
                    itemsInRange.value = 0;
                    itemsOutOfRange.value = 0;
                    duplicateItems.value = 0;
                    currentPage.value = 0;
                    totalPages.value = 0;
                    
                    // 更新状态
                    taskStatus.value = '无任务';
                    progressPercent.value = 0;  // 重置为0而不是100
                    progressText.value = '';    // 清空进度文本
                    remainingTime.value = '';   // 清空剩余时间
                    elapsedTime.value = '00:00:00'; // 重置耗时显示
                    
                    // 添加到时间线
                    addTimelineActivity('任务已完成', 'success');
                    
                    // 更新系统状态
                    systemStatus.value = 'idle';
                    // 不再直接修改计算属性
                    // statusText.value = '系统空闲';
                    lastRun.value = formatDateTime(new Date());
                    updateLastRunRelative(new Date());
                    lastRunSuccess.value = true;
                    
                    // 添加完成日志
                    addLog(`任务 ${taskNameForLog} 已完成，共处理 ${finalProcessedCount} 条数据，有效数据 ${finalValidItems} 条`, 'success');
                    ElMessage.success('任务已完成');
                    
                    // 显示任务完成通知
                    ElNotification({
                        title: '任务完成',
                        message: `${taskNameForLog} 已完成，共处理 ${finalProcessedCount} 条数据`,
                        type: 'success',
                        duration: 5000
                    });
                    
                    // 清理图表实例 - 不需要销毁图表，只需清除数据
                    if (chartInstance) {
                        // 重置图表数据而不是销毁图表
                        chartData.processedHistory = [0];
                        chartData.itemsInRangeHistory = [0];
                        chartData.labels = ['开始'];
                        
                        // 更新图表
                        chartInstance.data.labels = chartData.labels;
                        chartInstance.data.datasets[0].data = chartData.processedHistory;
                        chartInstance.data.datasets[1].data = chartData.itemsInRangeHistory;
                        chartInstance.update();
                    }
                    
                    if (miniChartInstance) {
                        // 更新小图表
                        miniChartInstance.data.labels = chartData.labels;
                        miniChartInstance.data.datasets[0].data = chartData.processedHistory;
                        miniChartInstance.update();
                    }
                    
                    // 清空当前任务名称
                    currentTaskName.value = '';
                    
                    // 确保轮询继续工作
                    // 避免在这里更改轮询定时器或清除它
                    // 让轮询逻辑由pollTaskStatus函数处理
                    console.log("任务完成处理完毕，轮询将继续", timers.pollStatus ? "轮询计时器已存在" : "轮询需要重新启动");
                    
                    // 如果当前没有轮询计时器，则启动一个
                    if (!timers.pollStatus) {
                        console.log("在finishTask中重新启动轮询");
                        setTimeout(pollTaskStatus, 5000); // 5秒后开始轮询
                    }
                }
                
                function clearTimer(timerName) {
                    // 避免清除pollStatus计时器
                    if (timerName === 'pollStatus') {
                        console.log("警告: 尝试清除轮询计时器，已阻止");
                        return;
                    }
                    
                    if (timers[timerName]) {
                        clearInterval(timers[timerName]);
                        timers[timerName] = null;
                    }
                }
                
                function clearAllTimers() {
                    // 保存对轮询计时器的引用
                    const pollStatusTimer = timers.pollStatus;
                    
                    // 清除所有其他计时器
                    Object.keys(timers).forEach(timerName => {
                        if (timerName !== 'pollStatus') {
                            clearTimer(timerName);
                        }
                    });
                    
                    // 恢复轮询计时器
                    timers.pollStatus = pollStatusTimer;
                }
                
                // 辅助函数
                function formatTime(date) {
                    return date.toTimeString().split(' ')[0];
                }
                
                function formatDateTime(date) {
                    // 使用本地时间格式化，而不是UTC时间
                    const year = date.getFullYear();
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const day = date.getDate().toString().padStart(2, '0');
                    const hours = date.getHours().toString().padStart(2, '0');
                    const minutes = date.getMinutes().toString().padStart(2, '0');
                    const seconds = date.getSeconds().toString().padStart(2, '0');
                    
                    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                }
                
                function formatDuration(ms) {
                    if (isNaN(ms)) {
                        console.log('formatDuration: 传入NaN毫秒值');
                        return '00:00:00';
                    }
                    
                    const totalSeconds = Math.floor(ms / 1000);
                    const hours = Math.floor(totalSeconds / 3600);
                    const minutes = Math.floor((totalSeconds % 3600) / 60);
                    const seconds = totalSeconds % 60;
                    
                    const formatted = [
                        hours.toString().padStart(2, '0'),
                        minutes.toString().padStart(2, '0'),
                        seconds.toString().padStart(2, '0')
                    ].join(':');
                    
                    // 记录长时间运行的日志
                    if (totalSeconds > 60) {
                        console.log('formatDuration:', totalSeconds, '秒 =>', formatted);
                    }
                    
                    return formatted;
                }
                
                // 生命周期钩子
                onMounted(() => {
                    // 初始化时重置轮询标志
                    window.pollingInProgress = false;
                    
                    // 添加更多错误处理以解决axios未定义的问题
                    if (typeof axios === 'undefined') {
                        console.error('警告: Axios未加载，创建基本的axios对象');
                        window.axios = {
                            get: function(url, config) {
                                return fetch(url)
                                    .then(response => response.json())
                                    .catch(error => { throw error; });
                            },
                            post: function(url, data, config) {
                                return fetch(url, {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify(data)
                                })
                                .then(response => response.json())
                                .catch(error => { throw error; });
                            }
                        };
                    }
                    
                    window.addEventListener('resize', handleResize);
                    
                    // 初始加载系统状态
                    refreshStatus();
                    
                    // 启动自动轮询 - 确保只有一个轮询实例
                    setTimeout(() => {
                        // 延迟1秒启动轮询，确保页面已完成初始化
                        startAutoPolling();
                    }, 1000);
                    
                    // 启动相对时间更新
                    timers.updateRelativeTime = setInterval(() => {
                        // 更新上次执行的相对时间显示
                        const lastRunDate = lastRun.value !== '暂无执行记录' 
                            ? new Date(lastRun.value) 
                            : null;
                        if (lastRunDate) {
                            updateLastRunRelative(lastRunDate);
                        }
                    }, 60000); // 每分钟更新一次相对时间
                    
                    // 初始检查连接状态
                    checkConnection();

                    // 设置页面可见性监听
                    setupVisibilityListeners();
                });
                
                // 自动轮询函数
                function startAutoPolling() {
                    // 如果已经存在轮询，则不再创建
                    if (timers.pollStatus) {
                        return;
                    }
                    
                    // 启动轮询
                    pollTaskStatus();
                    
                    // 添加日志
                    addLog('自动状态刷新已启动');
                }
                
                onBeforeUnmount(() => {
                    // 清理资源
                    window.removeEventListener('resize', handleResize);
                    
                    // 重置全局标记
                    window.pollingInProgress = false;
                    
                    // 清除所有计时器
                    clearAllTimers();
                    
                    // 清理图表实例
                    if (chartInstance) {
                        try {
                            chartInstance.destroy();
                        } catch (e) {
                            console.error("销毁图表实例出错:", e);
                        }
                    }
                    if (miniChartInstance) {
                        try {
                            miniChartInstance.destroy();
                        } catch (e) {
                            console.error("销毁小图表实例出错:", e);
                        }
                    }
                    
                    // 记录组件已销毁
                    console.log("组件已销毁，所有资源已清理");
                });
                
                // 监听日志变化，自动滚动
                watch(logs, () => {
                    if (autoScroll.value) {
                        nextTick(() => {
                            scrollLogsToBottom();
                        });
                    }
                });
                
                // 监听连接状态变化
                watch(connectionStatus, (newStatus) => {
                    if (newStatus) {
                        addLog('已恢复与后端的连接', 'success');
                        // 连接恢复后刷新数据，使用静默模式
                        refreshStatus(true);
                    } else {
                        addLog('与后端的连接已断开', 'error');
                    }
                });
                
                // 新增：启动本地时间更新
                function startLocalTimeUpdate() {
                    // 清除已有计时器
                    clearTimer('localTimeUpdate');
                    
                    // 设置计时器，每200毫秒更新一次时间，使显示更加平滑
                    timers.localTimeUpdate = setInterval(() => {
                        updateElapsedTime();
                    }, 200);
                }
                
                function updateLastRunRelative(timestamp) {
                    if (!timestamp) return;
                    
                    const now = new Date();
                    const diff = now - timestamp;
                    
                    // 计算相对时间
                    if (diff < 60000) { // 小于1分钟
                        lastRunRelative.value = '刚刚';
                    } else if (diff < 3600000) { // 小于1小时
                        lastRunRelative.value = Math.floor(diff / 60000) + '分钟前';
                    } else if (diff < 86400000) { // 小于1天
                        lastRunRelative.value = Math.floor(diff / 3600000) + '小时前';
                    } else { // 大于等于1天
                        lastRunRelative.value = Math.floor(diff / 86400000) + '天前';
                    }
                }
                
                // 页面可见性管理
                let isPageVisible = !document.hidden;
                let visibilityChangeHandler = null;
                let beforeUnloadHandler = null;

                // 设置页面可见性监听
                function setupVisibilityListeners() {
                    visibilityChangeHandler = function() {
                        const wasVisible = isPageVisible;
                        isPageVisible = !document.hidden;

                        if (wasVisible !== isPageVisible) {
                            console.log(`页面可见性变化: ${isPageVisible ? '可见' : '隐藏'}`);

                            if (isPageVisible && !timers.pollStatus) {
                                // 页面重新可见且轮询已停止，重新开始轮询
                                console.log('页面重新可见，恢复状态轮询');
                                pollTaskStatus();
                            }
                        }
                    };

                    beforeUnloadHandler = function() {
                        console.log('页面即将卸载，停止轮询');
                        stopPolling();
                    };

                    document.addEventListener('visibilitychange', visibilityChangeHandler);
                    window.addEventListener('beforeunload', beforeUnloadHandler);
                }

                // 清理可见性监听器
                function cleanupVisibilityListeners() {
                    if (visibilityChangeHandler) {
                        document.removeEventListener('visibilitychange', visibilityChangeHandler);
                        visibilityChangeHandler = null;
                    }
                    if (beforeUnloadHandler) {
                        window.removeEventListener('beforeunload', beforeUnloadHandler);
                        beforeUnloadHandler = null;
                    }
                }

                // 停止轮询
                function stopPolling() {
                    if (timers.pollStatus) {
                        clearTimeout(timers.pollStatus);
                        timers.pollStatus = null;
                    }
                    window.pollingInProgress = false;
                    console.log('轮询已停止');
                }

                function pollTaskStatus() {
                    // 确保之前的轮询已清除
                    if (timers.pollStatus) {
                        clearTimeout(timers.pollStatus);
                        timers.pollStatus = null;
                    }

                    // 防止重复轮询
                    if (window.pollingInProgress) {
                        console.log("已有轮询正在进行，跳过本次轮询");
                        return;
                    }

                    // 检查页面是否可见，如果不可见则使用更长的间隔或停止轮询
                    if (!isPageVisible) {
                        // 页面隐藏时，使用更长的间隔或停止轮询
                        const hiddenInterval = 30000; // 30秒
                        console.log(`页面隐藏，使用较长轮询间隔: ${hiddenInterval/1000}秒`);
                        timers.pollStatus = setTimeout(pollTaskStatus, hiddenInterval);
                        return;
                    }

                    // 标记轮询开始
                    window.pollingInProgress = true;

                    // 根据任务状态设置轮询间隔
                    let interval = taskRunning.value ? 2000 : 5000; // 任务运行时2秒，空闲时5秒
                    console.log(`设置轮询间隔: ${interval/1000}秒 (${taskRunning.value ? '任务运行中' : '空闲状态'})`);

                    // 使用递归timeout而不是interval，确保前一个请求完成后才开始下一个
                    timers.pollStatus = setTimeout(function() {
                        console.log(`开始轮询 - ${new Date().toLocaleTimeString()}`);

                        // 使用静默模式刷新状态，避免日志冗余
                        refreshStatus(true)
                            .then(() => {
                                // 不论刷新是否成功，都要清理标记
                                window.pollingInProgress = false;

                                // 成功时，设置下一次轮询
                                console.log(`轮询成功，${interval/1000}秒后再次轮询`);
                                timers.pollStatus = setTimeout(pollTaskStatus, interval);
                            })
                            .catch(error => {
                                // 错误处理
                                console.error("轮询状态失败:", error);
                                window.pollingInProgress = false;

                                // 连接失败后使用更长的间隔
                                timers.pollStatus = setTimeout(pollTaskStatus, 10000);
                            });
                    }, 100); // 初始延迟100ms，避免立即触发
                }
                
                return {
                    // Element Plus图标
                    Refresh, VideoPlay, Delete, Check, Loading, WarningFilled, 
                    Timer, Connection, Document, DataAnalysis, ArrowRight,
                    
                    // 响应式状态
                    isMobile,
                    refreshing,
                    submitting,
                    submitCountdown,
                    autoScroll,
                    showFloatingStats,
                    connectionStatus,
                    connectionRetries,
                    maxRetries,
                    
                    // 图表引用
                    progressChart,
                    miniChart,
                    
                    // 表单和验证
                    taskFormRef,
                    taskForm,
                    taskFormRules,
                    
                    // 系统状态
                    systemStatus,
                    statusText,
                    statusType,
                    queueStatus,
                    queueStatusClass,
                    lastRun,
                    lastRunRelative,
                    
                    // 任务状态
                    taskRunning,
                    currentTaskName,
                    taskStatus,
                    tagType,
                    processedCount,
                    totalCount,
                    elapsedTime,
                    progressPercent,
                    progressText,
                    remainingTime,
                    progressDetails,
                    
                    // 统计数据
                    itemsInRange,
                    itemsOutOfRange,
                    duplicateItems,
                    currentPage,
                    totalPages,
                    
                    // 时间线活动
                    taskActivities,
                    
                    // 日志
                    logContainer,
                    logs,
                    
                    // 方法
                    refreshStatus,
                    submitTask,
                    setTimeRangePreset,
                    clearForm,
                    clearLogs,
                    scrollLogsToBottom,
                    updateTaskStatus,

                    // 添加缺失的变量
                    accountLevel,
                    dataLimit
                };
            }
        };
        
        // 注册所有Element Plus图标
        const app = createApp(App);
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
        
        // 使用Element Plus
        app.use(ElementPlus);
        
        // 注册组件
        app.component('ElNotification', ElNotification)
        app.component('ElMessage', ElMessage)
        app.component('ElMessageBox', ElMessageBox)
        app.component('ElTag', ElTag)
        app.component('ElButton', ElButton)
        app.component('ElRow', ElRow)
        app.component('ElCol', ElCol)
        app.component('ElCard', ElCard)
        app.component('ElForm', ElForm)
        app.component('ElFormItem', ElFormItem)
        app.component('ElInput', ElInput)
        app.component('ElButtonGroup', ElButtonGroup)
        app.component('ElSwitch', ElSwitch)
        app.component('ElIcon', ElIcon)
        app.component('ElAlert', ElAlert)
        
        // 挂载Vue应用
        app.mount('#app');
    </script>
</body>
</html> 
</html> 