/**
 * 爬虫任务管理器 CSS样式
 */

:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #e74c3c;
    --secondary-dark: #c0392b;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --error-color: #e74c3c;
    --bg-color: #f8f9fa;
    --card-bg: #ffffff;
    --text-color: #333333;
    --border-radius: 8px;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    --transition-time: 0.3s;
}

/* 基本样式 */
body {
    font-family: "Microsoft YaHei", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    padding-top: 2rem;
    padding-bottom: 2rem;
}

.container {
    max-width: 1200px;
}

/* 卡片样式 */
.card {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: none;
    background-color: var(--card-bg);
    margin-bottom: 2rem;
    transition: transform var(--transition-time) ease, box-shadow var(--transition-time) ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    padding: 1rem 1.5rem;
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
    display: flex;
    align-items: center;
}

.card-header i {
    margin-right: 10px;
    font-size: 1.3rem;
}

.card-body {
    padding: 1.5rem;
}

/* 表单样式 */
.form-label {
    font-weight: 500;
}

.form-control {
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    border: 1px solid #ced4da;
    margin-bottom: 1rem;
    transition: border-color var(--transition-time) ease, box-shadow var(--transition-time) ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25);
}

/* 按钮样式 */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    border-radius: var(--border-radius);
    transition: background-color var(--transition-time) ease;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-outline-secondary {
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 500;
}

/* 预设任务区域 */
.task-presets {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(52, 152, 219, 0.1);
    border-radius: var(--border-radius);
    transition: background-color var(--transition-time) ease;
}

.task-presets:hover {
    background: rgba(52, 152, 219, 0.15);
}

/* 提示框 */
.alert {
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    display: none;
}

/* 控制台和日志样式 */
.console, .task-logs {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 1rem;
    border-radius: var(--border-radius);
    font-family: "Courier New", Courier, monospace;
    height: 250px;
    overflow-y: auto;
    margin-top: 1rem;
    transition: background-color var(--transition-time) ease;
}

.task-logs {
    height: 300px;
}

.console:hover, .task-logs:hover {
    background-color: #34495e;
}

.console-line {
    padding: 0.2rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.timestamp {
    color: #9a9a9a;
    margin-right: 10px;
}

.console-success {
    color: var(--success-color);
}

.console-error {
    color: var(--error-color);
}

.console-warning {
    color: var(--warning-color);
}

/* 状态徽章 */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 500;
    margin-left: auto;
    transition: background-color var(--transition-time) ease;
}

.badge-running, .badge-idle {
    background-color: var(--success-color);
    color: white;
}

.badge-idle {
    background-color: var(--primary-color);
}

.badge-stopped {
    background-color: var(--error-color);
    color: white;
}

.badge-busy {
    background-color: var(--warning-color);
    color: white;
}

/* 状态卡片 */
.status-card {
    display: flex;
    align-items: center;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #f8f9fa;
    border-left: 4px solid var(--primary-color);
    transition: transform var(--transition-time) ease, box-shadow var(--transition-time) ease;
}

.status-card:hover {
    transform: translateX(5px);
    box-shadow: var(--box-shadow);
}

/* 状态图标 */
.status-icon {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    display: inline-block;
    transition: background-color var(--transition-time) ease;
}

.status-icon.green {
    background-color: var(--success-color);
}

.status-icon.red {
    background-color: var(--error-color);
}

.status-icon.orange {
    background-color: var(--warning-color);
}

.status-icon.blue {
    background-color: var(--primary-color);
}

/* 状态详情 */
.status-details {
    background-color: #f8f9fa;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1.5rem;
    transition: background-color var(--transition-time) ease;
}

.status-details:hover {
    background-color: #f1f3f4;
}

.status-details pre {
    background-color: #ecf0f1;
    padding: 1rem;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    margin-top: 0.5rem;
    white-space: pre-wrap;
    transition: background-color var(--transition-time) ease;
}

.status-details pre:hover {
    background-color: #e0e5e7;
}

/* 任务进度样式 */
.task-progress {
    background-color: #f8f9fa;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid var(--warning-color);
}

.task-progress-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.task-progress-title i {
    margin-right: 8px;
    color: var(--warning-color);
}

.task-progress-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.task-progress-label {
    color: #666;
}

.task-progress-value {
    font-weight: 500;
}

.progress {
    height: 8px;
    border-radius: 4px;
    margin-top: 0.5rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card-header {
        padding: 1rem;
    }
    .card-body {
        padding: 1rem;
    }
} 