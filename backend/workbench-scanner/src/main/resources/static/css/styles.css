/* 爬虫引擎样式表 */
:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-bg: #f8f9fa;
    --dark-bg: #343a40;
    --border-color: #dee2e6;
    --text-color: #333;
    --light-text: #6c757d;
}

/* 基础样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-bg);
    margin: 0;
    padding: 0;
    font-size: 16px; /* 基础字体大小 */
    display: flex;
    justify-content: center; /* 水平居中 */
    min-height: 100vh;
    width: 100%; /* 确保body撑满整个宽度 */
}

/* 响应式容器 */
.container {
    width: 100%;
    min-width: 320px; /* 确保最小宽度 */
    max-width: 1400px; /* 增加最大宽度，适应宽屏 */
    margin: 0 auto;
    padding: 10px; /* 较小的默认内边距 */
    box-sizing: border-box; /* 确保padding计入宽度 */
    flex: 1; /* 让容器占据可用空间 */
}

.header {
    background-color: var(--dark-bg);
    color: white;
    padding: 0.8rem;
    text-align: center;
    border-radius: 5px 5px 0 0;
    margin-bottom: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header h1 {
    margin: 0;
    font-size: 1.2rem; /* 小屏幕默认字体大小 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 宽屏布局优化 */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
    width: 100%;
}

.widescreen-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
    width: 100%;
}

.col-md-6 {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0 15px;
    box-sizing: border-box;
}

/* 状态横幅 - 移动设备优先设计 */
.status-banner {
    display: flex;
    flex-direction: column; /* 在小屏幕上垂直排列 */
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 1rem;
    padding: 0.8rem;
    border-left: 4px solid var(--primary-color);
}

.status-badge {
    padding: 6px 10px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    animation: pulse 2s infinite;
    margin-bottom: 10px; /* 垂直布局时的间距 */
}

.status-info {
    flex: 1;
    padding: 0;
    margin-bottom: 10px; /* 垂直布局时的间距 */
}

.status-row {
    display: flex;
    margin-bottom: 0.5rem;
    align-items: center;
    flex-wrap: wrap; /* 允许在超小屏幕上换行 */
}

.status-label {
    min-width: 90px; /* 减小标签宽度 */
    font-weight: bold;
    color: var(--light-text);
    font-size: 0.8rem;
}

.status-value {
    flex: 1;
    font-size: 0.8rem;
    word-break: break-word; /* 防止长文本溢出 */
}

.refresh-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: background-color 0.3s;
    width: 100%; /* 在小屏幕上占据全宽 */
}

/* 卡片样式 */
.card {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 1rem;
    overflow: hidden;
}

.card-header {
    background-color: #f8f9fa;
    padding: 0.8rem;
    border-bottom: 1px solid var(--border-color);
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap; /* 允许在小屏幕上换行 */
    font-size: 0.9rem;
}

.card-body {
    padding: 1rem;
}

/* 表单元素 */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.4rem;
    font-weight: bold;
    font-size: 0.9rem;
}

.form-control {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.9rem;
    box-sizing: border-box;
}

/* 按钮样式 */
.btn {
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s;
    border: none;
    white-space: nowrap;
}

.btn-group {
    display: flex;
    gap: 8px;
    flex-wrap: wrap; /* 允许按钮在小屏幕上换行 */
}

/* 预设时间范围按钮容器 */
.preset-container {
    background-color: #f5f5f5;
    padding: 0.6rem;
    border-radius: 4px;
    margin-bottom: 0.8rem;
    font-size: 0.85rem;
    border: 1px solid #eaeaea;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    align-items: center;
}

.preset-label {
    font-weight: bold;
    color: var(--light-text);
    margin-right: 5px;
    margin-bottom: 5px;
    font-size: 0.8rem;
}

.preset-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.preset-buttons .btn {
    background-color: #e9ecef;
    color: #495057;
}

.preset-buttons .btn:hover {
    background-color: var(--primary-color);
    color: white;
}

/* 任务进度显示 */
.task-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.task-info-item {
    width: calc(50% - 5px); /* 在小屏幕上2个一排 */
    margin-bottom: 8px;
}

.task-info-label {
    font-size: 0.75rem;
    color: var(--light-text);
    margin-bottom: 3px;
}

.task-info-value {
    font-weight: bold;
    font-size: 0.9rem;
    word-break: break-word;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: var(--light-text);
    flex-wrap: wrap;
}

/* 日志控制台 */
.console-wrapper {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: #1e1e1e;
    height: 200px; /* 默认较小高度 */
    overflow-y: auto;
    font-family: 'Consolas', monospace;
    color: #e0e0e0;
    padding: 0.5rem;
    font-size: 0.8rem;
    line-height: 1.5;
}

/* 响应式布局 - 多个断点 */
/* 超小屏幕 - 手机 */
@media (max-width: 575.98px) {
    .container {
        padding: 8px;
    }
    
    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .card-header .btn-group {
        margin-top: 8px;
        width: 100%;
        justify-content: space-between;
    }
    
    .task-info-item {
        width: 100%; /* 在超小屏幕上每项占满宽度 */
    }
    
    .btn {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
    }
    
    /* 在小屏幕上改进预设按钮的布局 */
    .preset-container {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .preset-buttons {
        width: 100%;
        justify-content: space-between;
    }
    
    .preset-buttons .btn {
        flex: 1;
        text-align: center;
        padding: 0.4rem 0.3rem;
        font-size: 0.75rem;
    }
    
    /* 改进状态横幅在小屏幕上的显示 */
    .status-banner {
        padding: 0.6rem;
    }
    
    .refresh-btn {
        padding: 0.5rem;
        font-size: 0.75rem;
    }
    
    .refresh-btn i {
        margin-right: 3px;
    }
    
    /* 任务进度信息在小屏幕上的优化 */
    .progress-info {
        flex-direction: column;
        gap: 5px;
    }
    
    #remaining-time {
        align-self: flex-end;
    }
}

/* 小屏幕 - 平板 */
@media (min-width: 576px) and (max-width: 767.98px) {
    .container {
        padding: 12px;
    }
    
    .status-banner {
        flex-direction: row;
        align-items: flex-start;
    }
    
    .status-badge {
        margin-bottom: 0;
    }
    
    .status-info {
        margin-bottom: 0;
        padding: 0 0.8rem;
    }
    
    .refresh-btn {
        width: auto;
    }
    
    .console-wrapper {
        height: 250px;
    }
}

/* 中等屏幕 - 平板和小型笔记本 */
@media (min-width: 768px) and (max-width: 991.98px) {
    .container {
        padding: 15px;
    }
    
    .header h1 {
        font-size: 1.5rem;
    }
    
    .col-md-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    
    .console-wrapper {
        height: 280px;
    }
    
    .task-info-item {
        width: calc(50% - 5px); /* 在中等屏幕上2个一排 */
    }
}

/* 大屏幕 - 桌面 */
@media (min-width: 992px) and (max-width: 1199.98px) {
    .header h1 {
        font-size: 1.8rem;
    }
    
    .col-md-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    
    .console-wrapper {
        height: 300px;
    }
    
    .task-info-item {
        width: calc(25% - 8px); /* 在大屏幕上4个一排 */
    }
}

/* 超大屏幕 - 大型桌面 */
@media (min-width: 1200px) {
    .container {
        padding: 20px;
        max-width: 90%; /* 使用百分比宽度代替固定宽度 */
        min-width: 1000px; /* 确保在大屏上有足够的最小宽度 */
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .col-md-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    
    .console-wrapper {
        height: 350px;
    }
    
    .task-info-item {
        width: calc(25% - 10px); /* 在超大屏幕上4个一排，间距更大 */
    }
    
    .form-control {
        font-size: 1rem;
    }
    
    .btn {
        font-size: 1rem;
    }
}

/* 超宽屏幕 - 1440p及以上 */
@media (min-width: 2000px) {
    .container {
        max-width: 1800px; /* 在超宽屏幕上设置更大的最大宽度 */
        min-width: 1200px; /* 确保足够的最小宽度 */
    }
    
    .header h1 {
        font-size: 2.2rem;
    }
    
    .card {
        margin-bottom: 2.5rem;
    }
    
    .card-body {
        padding: 2rem;
    }
    
    .form-group {
        margin-bottom: 2rem;
    }
    
    .console-wrapper {
        height: 450px; /* 增加控制台高度 */
    }
    
    /* 在1440p及以上的屏幕上优化布局 */
    .widescreen-row {
        flex-wrap: nowrap; /* 防止换行 */
        max-width: 2400px; /* 限制最大宽度，避免内容过于分散 */
        margin: 0 auto; /* 居中显示 */
    }
    
    .col-md-6 {
        transition: all 0.3s ease; /* 平滑过渡 */
        min-width: 500px; /* 确保每列有合理的最小宽度 */
    }
}

/* 特宽屏幕 - 4k及以上 */
@media (min-width: 3000px) {
    .container {
        max-width: 2400px; /* 在4K屏幕上设置更大的最大宽度 */
        min-width: 1600px; /* 确保足够的最小宽度 */
    }
    
    body {
        font-size: 18px; /* 在4K屏幕上增加基础字体大小 */
    }
    
    .header h1 {
        font-size: 2.5rem;
    }
    
    .form-control {
        font-size: 1.1rem;
        padding: 0.7rem;
        max-width: 1200px; /* 限制输入框最大宽度，避免过长难以阅读 */
        margin: 0 auto;
    }
    
    .btn {
        font-size: 1.1rem;
        padding: 0.6rem 1.2rem;
    }
    
    .console-wrapper {
        height: 550px;
        font-size: 1rem;
    }
    
    /* 特宽屏幕下居中内容 */
    .container {
        display: flex;
        flex-direction: column;
        align-items: center; /* 居中对齐 */
    }
    
    .status-banner,
    .widescreen-row {
        max-width: 2400px;
        width: 100%;
    }
    
    /* 确保卡片内容可读性 */
    .card-body {
        padding: 2.5rem;
        max-width: 100%;
    }
}

/* 保留原有动画和一些特殊样式 */
.badge-idle {
    background-color: #e3f2fd;
    color: #0d6efd;
}

.badge-busy {
    background-color: #fff3cd;
    color: #ff9800;
    animation: busy-pulse 1.5s infinite;
}

.badge-stopped {
    background-color: #f8d7da;
    color: #dc3545;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.4);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(52, 152, 219, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
    }
}

@keyframes busy-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.4);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(255, 152, 0, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 152, 0, 0);
    }
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.status-icon {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.green {
    background-color: #2ecc71;
    box-shadow: 0 0 5px #2ecc71;
}

.blue {
    background-color: #3498db;
    box-shadow: 0 0 5px #3498db;
}

.orange {
    background-color: #f39c12;
    box-shadow: 0 0 5px #f39c12;
}

.red {
    background-color: #e74c3c;
    box-shadow: 0 0 5px #e74c3c;
}

.console-line {
    margin-bottom: 2px;
    display: flex;
    align-items: flex-start;
}

.timestamp {
    color: #7f7f7f;
    margin-right: 8px;
    user-select: none;
    white-space: nowrap;
    font-size: 0.75rem;
}

.console-success {
    color: #2ecc71;
}

.console-error {
    color: #e74c3c;
}

.console-warning {
    color: #f39c12;
}

.alert {
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    display: none;
    word-wrap: break-word;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.progress {
    height: 15px;
    background-color: #e9ecef;
    border-radius: 30px;
    position: relative;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(to right, #4facfe, #00f2fe);
    border-radius: 30px;
    transition: width 0.5s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    color: white;
    font-size: 0.7rem;
    font-weight: bold;
    box-shadow: 0 0 10px rgba(79, 172, 254, 0.5);
}

.task-progress-details {
    margin-top: 10px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    font-size: 0.85rem;
    color: #495057;
    overflow-wrap: break-word;
}

.info-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    background-color: #17a2b8;
    color: white;
    text-align: center;
    line-height: 16px;
    border-radius: 50%;
    font-size: 11px;
    margin-left: 5px;
    cursor: help;
}

/* 提高浏览器支持性的前缀 */
.console-wrapper {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: #555 #1e1e1e;
}

.console-wrapper::-webkit-scrollbar {
    width: 6px;
}

.console-wrapper::-webkit-scrollbar-track {
    background: #1e1e1e;
}

.console-wrapper::-webkit-scrollbar-thumb {
    background-color: #555;
    border-radius: 4px;
}

/* 任务日志容器与常规控制台区分 */
#task-logs {
    background-color: #212121;
    border-left: 4px solid #f39c12; /* 左侧橙色边框以区分任务日志 */
}

/* 进度条动画效果 */
@keyframes progress-bar-stripes {
    from {
        background-position: 1rem 0;
    }
    to {
        background-position: 0 0;
    }
}

.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
    background-image: linear-gradient(45deg, 
        rgba(255, 255, 255, 0.15) 25%, 
        transparent 25%, 
        transparent 50%, 
        rgba(255, 255, 255, 0.15) 50%, 
        rgba(255, 255, 255, 0.15) 75%, 
        transparent 75%, 
        transparent);
    background-size: 1rem 1rem;
}

/* 确保表格和其他元素在小屏幕上的正确显示 */
table {
    width: 100%;
    border-collapse: collapse;
}

table, th, td {
    border: 1px solid var(--border-color);
}

th, td {
    padding: 8px;
    text-align: left;
    font-size: 0.9rem;
}

@media (max-width: 767.98px) {
    table, th, td {
        font-size: 0.8rem;
        padding: 4px;
    }
    
    table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }
}

/* 处理长文本溢出问题 */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
} 