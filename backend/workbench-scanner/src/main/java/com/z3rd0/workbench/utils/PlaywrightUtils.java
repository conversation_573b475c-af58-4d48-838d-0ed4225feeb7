package com.z3rd0.workbench.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.microsoft.playwright.*;
import com.microsoft.playwright.options.*;
import com.z3rd0.common.model.CookieRecord;
import com.z3rd0.workbench.service.CookieService;
import com.z3rd0.workbench.config.CrawlerConfig;
import com.z3rd0.workbench.config.CaptchaConfig;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.time.Instant;
import java.time.ZoneId;
import java.time.LocalDate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.time.ZonedDateTime;

@Component
public class PlaywrightUtils {
    private static final Logger logger = LoggerFactory.getLogger(PlaywrightUtils.class);
    private Playwright playwright;
    private Browser browser;
    private BrowserContext context;
    private Page page;
    
    // 自动注入配置类
    @Autowired
    private CrawlerConfig crawlerConfig;
    
    // 添加静态实例和设置方法，解决依赖注入问题
    private static CookieService staticCookieService;
    private static CaptchaConfig staticCaptchaConfig;

    // 为了支持静态方法和测试，添加静态配置字段
    private static String staticUsername;
    private static String staticPassword;

    // 添加设备阈值的静态配置
    private static int staticNormalDeviceThreshold = 1; // 普通账号默认阈值
    private static int staticVipDeviceThreshold = 4;    // VIP账号默认阈值

    // 验证码请求检测标记
    private volatile boolean captchaRequestDetected = false;

    // 验证码验证完成检测标记
    private volatile boolean captchaVerifyDetected = false;
    
    // 添加ObjectMapper实例用于JSON处理
    private final ObjectMapper jsonMapper = new ObjectMapper();

    /**
     * 安全解析JSON响应
     * @param responseText 响应文本
     * @param source 数据源描述（用于日志）
     * @return JSONObject或null
     */
    private JSONObject safeParseJsonResponse(String responseText, String source) {
        if (responseText == null || responseText.trim().isEmpty()) {
            logger.warn("{}响应为空，无法解析JSON", source);
            return null;
        }

        String trimmedResponse = responseText.trim();

        // 检查是否是JSON格式
        if (!trimmedResponse.startsWith("{") && !trimmedResponse.startsWith("[")) {
            logger.error("{}响应不是JSON格式 - 响应内容: {}", source,
                trimmedResponse.length() > 200 ? trimmedResponse.substring(0, 200) + "..." : trimmedResponse);
            return null;
        }

        try {
            JSONObject jsonResponse = new JSONObject(trimmedResponse);
            logger.debug("{}JSON解析成功", source);
            return jsonResponse;
        } catch (Exception e) {
            logger.error("{}JSON解析失败: {} - 响应内容: {}", source, e.getMessage(),
                trimmedResponse.length() > 300 ? trimmedResponse.substring(0, 300) + "..." : trimmedResponse);

            // 尝试检查是否是HTML错误页面
            if (trimmedResponse.toLowerCase().contains("<html") ||
                trimmedResponse.toLowerCase().contains("<!doctype")) {
                logger.error("{}返回的是HTML页面而非JSON，可能是错误页面或登录页面", source);
            }

            return null;
        }
    }
    
    // 静态账号级别标记
    private static boolean staticIsVipAccount = false;
    
    private String lastResponseText = null;

    // 数据完整性保障
    private volatile boolean operationInProgress = false;
    private final Object operationLock = new Object();

    // 重试统计
    private volatile int totalRetryCount = 0;
    private volatile int successAfterRetryCount = 0;
    private final Map<String, Integer> operationRetryStats = new java.util.concurrent.ConcurrentHashMap<>();

    // JSON解析统计
    private volatile int totalJsonParseAttempts = 0;
    private volatile int jsonParseFailures = 0;
    private final Map<String, Integer> jsonParseFailuresByType = new java.util.concurrent.ConcurrentHashMap<>();

    public String getLastResponseText() {
        return lastResponseText;
    }

    public void setLastResponseText(String responseText) {
        this.lastResponseText = responseText;
    }

    /**
     * 判断异常是否为可重试的超时错误
     */
    private boolean isRetryableTimeoutError(Exception e) {
        if (e == null) return false;

        String errorMessage = e.getMessage();
        String exceptionType = e.getClass().getSimpleName();

        // 检查异常类型
        if (e instanceof com.microsoft.playwright.TimeoutError ||
            e instanceof java.net.SocketTimeoutException ||
            e instanceof java.net.ConnectException ||
            e instanceof java.io.IOException) {
            return true;
        }

        // 检查错误消息中的关键词
        if (errorMessage != null) {
            String lowerMessage = errorMessage.toLowerCase();
            return lowerMessage.contains("timeout") ||
                   lowerMessage.contains("timed out") ||
                   lowerMessage.contains("connection reset") ||
                   lowerMessage.contains("connection refused") ||
                   lowerMessage.contains("network error") ||
                   lowerMessage.contains("page crashed") ||
                   lowerMessage.contains("navigation timeout");
        }

        return false;
    }

    /**
     * 计算重试延时时间（指数退避策略）
     */
    private int calculateRetryDelay(int attemptNumber) {
        int delay = RETRY_BASE_DELAY * (int) Math.pow(2, attemptNumber - 1);
        return Math.min(delay, RETRY_MAX_DELAY);
    }

    /**
     * 检查网络连接和浏览器状态
     */
    private boolean checkSystemHealth() {
        try {
            // 检查浏览器是否已初始化且连接正常
            if (!isInitialized()) {
                logger.warn("浏览器未初始化，系统健康检查失败");
                return false;
            }

            if (!isConnected()) {
                logger.warn("浏览器连接异常，系统健康检查失败");
                return false;
            }

            // 执行简单的页面操作验证浏览器响应
            String readyState = (String) page.evaluate("document.readyState");
            logger.debug("页面状态: {}", readyState);

            // 检查当前页面URL是否有效
            String currentUrl = page.url();
            if (currentUrl == null || currentUrl.isEmpty() || currentUrl.equals("about:blank")) {
                logger.warn("当前页面URL无效: {}", currentUrl);
                return false;
            }

            // 检查页面是否可交互
            boolean isInteractive = (boolean) page.evaluate(
                "document.readyState === 'complete' || document.readyState === 'interactive'"
            );

            if (!isInteractive) {
                logger.warn("页面不可交互，readyState: {}", readyState);
                return false;
            }

            logger.debug("系统健康检查通过 - URL: {}, 状态: {}", currentUrl, readyState);
            return true;

        } catch (Exception e) {
            logger.warn("系统健康检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 增强的网络连接检查
     */
    private boolean checkNetworkConnectivity() {
        try {
            logger.debug("开始网络连接检查...");

            // 尝试访问一个简单的测试页面
            Response response = page.navigate("https://www.baidu.com",
                new Page.NavigateOptions().setTimeout(NETWORK_CHECK_TIMEOUT));

            if (response != null && response.ok()) {
                logger.debug("网络连接检查通过");
                return true;
            } else {
                logger.warn("网络连接检查失败 - 响应状态: {}",
                    response != null ? response.status() : "null");
                return false;
            }

        } catch (Exception e) {
            logger.warn("网络连接检查异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查操作是否可以安全执行（避免重复操作）
     */
    private boolean canExecuteOperation(String operationName) {
        synchronized (operationLock) {
            if (operationInProgress) {
                logger.warn("操作 {} 被跳过，因为另一个操作正在进行中", operationName);
                return false;
            }
            operationInProgress = true;
            logger.debug("开始执行操作: {}", operationName);
            return true;
        }
    }

    /**
     * 标记操作完成
     */
    private void markOperationComplete(String operationName) {
        synchronized (operationLock) {
            operationInProgress = false;
            logger.debug("操作完成: {}", operationName);
        }
    }

    /**
     * 执行带重试的操作
     */
    private <T> T executeWithRetry(String operationName, RetryableOperation<T> operation) {
        // 检查是否可以安全执行操作
        if (!canExecuteOperation(operationName)) {
            throw new RuntimeException("操作 " + operationName + " 无法执行，系统繁忙");
        }

        Exception lastException = null;
        T result = null;

        try {
            for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                logger.debug("执行操作: {} (尝试 {}/{})", operationName, attempt, MAX_RETRY_ATTEMPTS);

                // 第一次尝试之后，检查系统健康状态
                if (attempt > 1) {
                    if (!checkSystemHealth()) {
                        logger.warn("系统健康检查失败，跳过重试 (尝试 {}/{})", attempt, MAX_RETRY_ATTEMPTS);
                        continue;
                    }
                }

                result = operation.execute();

                if (attempt > 1) {
                    logger.info("操作 {} 在第 {} 次尝试后成功", operationName, attempt);
                    // 更新重试统计
                    successAfterRetryCount++;
                    operationRetryStats.merge(operationName, attempt - 1, Integer::sum);
                }

                return result;

            } catch (Exception e) {
                lastException = e;

                if (!isRetryableTimeoutError(e)) {
                    logger.error("操作 {} 遇到不可重试的错误: {}", operationName, e.getMessage());
                    throw new RuntimeException("不可重试的错误", e);
                }

                if (attempt < MAX_RETRY_ATTEMPTS) {
                    int delay = calculateRetryDelay(attempt);
                    logger.warn("操作 {} 第 {} 次尝试失败: {}，{}ms 后重试",
                        operationName, attempt, e.getMessage(), delay);

                    // 更新重试统计
                    totalRetryCount++;

                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("重试被中断", ie);
                    }
                } else {
                    logger.error("操作 {} 在 {} 次尝试后仍然失败: {}",
                        operationName, MAX_RETRY_ATTEMPTS, e.getMessage());
                    // 记录最终失败的重试次数
                    operationRetryStats.merge(operationName, MAX_RETRY_ATTEMPTS, Integer::sum);
                }
            }
            }

            throw new RuntimeException("操作 " + operationName + " 重试失败", lastException);

        } finally {
            // 确保操作完成后释放锁
            markOperationComplete(operationName);
        }
    }

    /**
     * 重试操作的函数式接口
     */
    @FunctionalInterface
    private interface RetryableOperation<T> {
        T execute() throws Exception;
    }

    /**
     * 带重试机制的waitForResponse方法 - 不使用全局操作锁，避免与搜索操作的锁冲突
     */
    private Response waitForResponseWithRetry(
            java.util.function.Predicate<Response> urlPredicate,
            Runnable action,
            String operationName,
            int timeoutMs) throws Exception {

        // 直接执行响应等待，不使用全局操作锁，避免与搜索操作的锁冲突
        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                logger.debug("等待{}响应 (尝试 {}/{}), 超时时间: {}ms", operationName, attempt, MAX_RETRY_ATTEMPTS, timeoutMs);

                Response response = page.waitForResponse(
                    urlPredicate,
                    new Page.WaitForResponseOptions().setTimeout(timeoutMs),
                    action
                );

                if (response == null) {
                    throw new RuntimeException(operationName + "响应为null");
                }

                if (!response.ok()) {
                    logger.warn("{}响应状态异常: {}", operationName, response.status());
                }

                logger.debug("{}响应获取成功，状态码: {}", operationName, response.status());
                if (attempt > 1) {
                    logger.info("{}响应在第{}次尝试后成功", operationName, attempt);
                }
                return response;

            } catch (com.microsoft.playwright.TimeoutError e) {
                if (attempt < MAX_RETRY_ATTEMPTS) {
                    int delay = calculateRetryDelay(attempt);
                    logger.warn("{}响应等待第{}次尝试超时: {}，{}ms后重试",
                        operationName, attempt, e.getMessage(), delay);
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("响应等待被中断", ie);
                    }
                } else {
                    logger.error("{}响应在{}次尝试后仍然超时: {}", operationName, MAX_RETRY_ATTEMPTS, e.getMessage());
                    throw new RuntimeException(operationName + "响应超时: " + e.getMessage(), e);
                }
            } catch (Exception e) {
                if (attempt < MAX_RETRY_ATTEMPTS && isRetryableTimeoutError(e)) {
                    int delay = calculateRetryDelay(attempt);
                    logger.warn("{}响应等待第{}次尝试失败: {}，{}ms后重试",
                        operationName, attempt, e.getMessage(), delay);
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("响应等待被中断", ie);
                    }
                } else {
                    logger.error("{}响应获取失败: {}", operationName, e.getMessage());
                    throw new RuntimeException(operationName + "响应获取失败: " + e.getMessage(), e);
                }
            }
        }

        throw new RuntimeException(operationName + "响应等待重试失败");
    }

    /**
     * 带重试机制的元素等待方法 - 不使用全局操作锁，避免搜索框定位时的锁冲突
     */
    private boolean waitForElementWithRetry(Locator locator, String elementName, int timeoutMs) {
        try {
            // 直接执行元素等待，不使用全局操作锁，避免搜索框定位时的并发冲突
            for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
                try {
                    logger.debug("等待{} (尝试 {}/{})", elementName, attempt, MAX_RETRY_ATTEMPTS);

                    locator.waitFor(new Locator.WaitForOptions()
                            .setState(WaitForSelectorState.VISIBLE)
                            .setTimeout(timeoutMs));
                    sleep(500); // 元素出现后略微等待

                    if (attempt > 1) {
                        logger.info("{}在第{}次尝试后成功", elementName, attempt);
                    }
                    return true;

                } catch (com.microsoft.playwright.TimeoutError e) {
                    if (attempt < MAX_RETRY_ATTEMPTS) {
                        int delay = calculateRetryDelay(attempt);
                        logger.warn("{}等待第{}次尝试失败: {}，{}ms后重试",
                            elementName, attempt, e.getMessage(), delay);
                        try {
                            Thread.sleep(delay);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            throw new RuntimeException("等待被中断", ie);
                        }
                    } else {
                        logger.error("{}在{}次尝试后仍然失败: {}", elementName, MAX_RETRY_ATTEMPTS, e.getMessage());
                        throw new RuntimeException(elementName + "等待超时: " + e.getMessage(), e);
                    }
                }
            }
            return false;
        } catch (Exception e) {
            logger.error("等待{}失败: {}", elementName, e.getMessage());
            return false;
        }
    }
    
    /**
     * 设置CookieService实例，用于在非Spring环境中使用
     * @param cookieService CookieService实例
     */
    public static void setCookieService(CookieService cookieService) {
        PlaywrightUtils.staticCookieService = cookieService;
        logger.info("静态CookieService已设置");
    }

    /**
     * 设置CaptchaConfig实例，用于在非Spring环境中使用
     * @param captchaConfig CaptchaConfig实例
     */
    public static void setCaptchaConfig(CaptchaConfig captchaConfig) {
        PlaywrightUtils.staticCaptchaConfig = captchaConfig;
        logger.info("静态CaptchaConfig已设置");
    }
    
    /**
     * 设置静态账号信息，用于在非Spring环境中使用
     * @param username 用户名
     * @param password 密码
     */
    public static void setStaticCredentials(String username, String password) {
        PlaywrightUtils.staticUsername = username;
        PlaywrightUtils.staticPassword = password;
        
        // 同时更新VIP状态
        updateVipStatus();
        
        logger.info("静态账号凭据已设置，账号类型: {}", staticIsVipAccount ? "VIP" : "普通");
    }
    
    /**
     * 更新账号VIP状态（根据用户名判断）
     */
    private static void updateVipStatus() {
        // 根据静态用户名判断是否为VIP账号
        staticIsVipAccount = (staticUsername != null && staticUsername.toLowerCase().contains("vip"));
    }
    
    /**
     * 判断当前账号是否为VIP账号
     * @return 是否为VIP账号
     */
    public static boolean isVipAccount() {
        return staticIsVipAccount;
    }
    
    /**
     * 设置静态设备阈值，用于在非Spring环境中使用
     * @param normalThreshold 普通账号设备阈值
     * @param vipThreshold VIP账号设备阈值
     */
    public static void setStaticDeviceThresholds(int normalThreshold, int vipThreshold) {
        staticNormalDeviceThreshold = normalThreshold;
        staticVipDeviceThreshold = vipThreshold;
        logger.info("静态设备阈值已设置 - 普通账号: {}, VIP账号: {}", normalThreshold, vipThreshold);
    }
    
    /**
     * 获取设备阈值信息，仅使用静态配置的阈值
     * @return 设备阈值和账号等级信息
     */
    private DeviceThresholdInfo getDeviceThresholdInfo() {
        int deviceThreshold;
        String accountLevel;
        boolean isVip;
        
        try {
            // 使用静态方法判断VIP状态
            isVip = isVipAccount();
            deviceThreshold = isVip ? staticVipDeviceThreshold : staticNormalDeviceThreshold;
            accountLevel = isVip ? "VIP" : "普通";
            logger.debug("使用静态配置的设备阈值: {}, 账号等级: {}", deviceThreshold, accountLevel);
        } catch (Exception e) {
            logger.warn("获取设备阈值时出错: {}，使用默认值", e.getMessage());
            // 使用最保守的值
            deviceThreshold = staticNormalDeviceThreshold;
            accountLevel = "普通";
            isVip = false;
        }
        
        return new DeviceThresholdInfo(deviceThreshold, accountLevel, isVip);
    }
    
    /**
     * 设备阈值信息数据类
     */
    private static class DeviceThresholdInfo {
        final int threshold;
        final String levelName;
        final boolean isVip;
        
        DeviceThresholdInfo(int threshold, String levelName, boolean isVip) {
            this.threshold = threshold;
            this.levelName = levelName;
            this.isVip = isVip;
        }
    }
    
    // 配置参数，可以通过setter方法修改
    private int defaultDelay = 1000; // 默认步骤间延时1秒
    private int typeDelay = 150; // 输入文字时的延时
    private boolean slowMode = true; // 是否启用慢速模式
    private int pageLoadTimeout = 60000; // 页面加载超时时间（毫秒）

    // 重试机制配置
    private static final int MAX_RETRY_ATTEMPTS = 3; // 最大重试次数
    private static final int RETRY_BASE_DELAY = 3000; // 基础重试延时（毫秒）
    private static final int RETRY_MAX_DELAY = 10000; // 最大重试延时（毫秒）
    private static final int NETWORK_CHECK_TIMEOUT = 5000; // 网络检查超时时间（毫秒）

    /**
     * 构造函数，不再自动初始化浏览器
     */
    public PlaywrightUtils() {
        logger.info("PlaywrightUtils实例创建，等待显式初始化");
        // 不再自动调用initialize()
    }

    /**
     * 初始化浏览器环境
     */
    public void initialize() {
        if (playwright != null) {
            logger.warn("重复初始化PlaywrightUtils，忽略此次调用");
            return;
        }
        
        logger.info("PlaywrightUtils初始化中...");

        // 先进行网络连接检测
        logger.info("进行网络连接检测...");
        boolean networkOk = NetworkDiagnostic.checkNetworkConnection();
        if (!networkOk) {
            logger.warn("网络连接检测失败，但继续尝试初始化浏览器");
            logger.info(NetworkDiagnostic.getNetworkStatusReport());
        }

        try {
            playwright = Playwright.create();
            logger.info("Playwright创建成功");
            
            // 使用有界面模式，方便调试，添加网络相关启动参数
            browser = playwright.chromium().launch(new BrowserType.LaunchOptions()
                    .setHeadless(false)
                    .setSlowMo(slowMode ? 100 : 0) // 如果启用慢速模式，设置slowMo参数
                    .setArgs(Arrays.asList(
                        "--no-sandbox",
                        "--disable-setuid-sandbox",
                        "--disable-dev-shm-usage",
                        "--disable-web-security",
                        "--disable-features=VizDisplayCompositor",
                        "--no-first-run",
                        "--no-default-browser-check",
                        "--disable-background-timer-throttling",
                        "--disable-backgrounding-occluded-windows",
                        "--disable-renderer-backgrounding",
                        "--disable-extensions",
                        "--disable-plugins",
                        "--disable-default-apps",
                        "--disable-hang-monitor",
                        "--disable-prompt-on-repost",
                        "--disable-sync",
                        "--metrics-recording-only",
                        "--no-proxy-server", // 禁用代理服务器
                        "--disable-background-networking" // 禁用后台网络
                    )));
            logger.info("浏览器启动成功");

            context = browser.newContext(new Browser.NewContextOptions()
                    .setViewportSize(1440, 960) // 设置更大的视口
                    .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                    .setIgnoreHTTPSErrors(true) // 忽略HTTPS错误
                    .setJavaScriptEnabled(true) // 确保JavaScript启用
                    .setAcceptDownloads(false)); // 禁用下载
            logger.info("浏览器上下文创建成功");
            
            page = context.newPage();
            page.setDefaultTimeout(pageLoadTimeout);
            logger.info("页面创建成功");

            // 对话框处理函数
            page.onDialog(dialog -> {
                logger.info("页面对话框: {}", dialog.message());
                dialog.dismiss();
            });

            // 测试网络连接
            testNetworkConnection();
            
            sleep(1000); // 初始化后稍作停顿
        } catch (Exception e) {
            logger.error("初始化失败: {}", e.getMessage(), e);
            throw new RuntimeException("PlaywrightUtils初始化失败", e);
        }
    }

    /**
     * 测试网络连接
     */
    private void testNetworkConnection() {
        try {
            logger.info("开始测试网络连接...");

            // 测试访问百度（国内网站）
            page.navigate("https://www.baidu.com", new Page.NavigateOptions().setTimeout(10000));
            logger.info("网络连接测试成功 - 可以访问百度");

            // 等待页面加载完成
            page.waitForLoadState();

            // 获取页面标题验证
            String title = page.title();
            logger.info("页面标题: {}", title);

        } catch (Exception e) {
            logger.error("网络连接测试失败: {}", e.getMessage());

            // 尝试测试本地网络
            try {
                logger.info("尝试测试本地网络连接...");
                page.navigate("http://httpbin.org/get", new Page.NavigateOptions().setTimeout(5000));
                logger.info("本地网络连接正常");
            } catch (Exception localE) {
                logger.error("本地网络连接也失败: {}", localE.getMessage());
                logger.error("请检查网络设置和防火墙配置");
            }
        }
    }

    /**
     * 直接保存cert_common到数据库
     * @param certCommon cert_common值
     */
    private void saveCertCommonDirectly(String certCommon) {
        logger.info("开始保存cert_common到数据库...");
        System.out.println("开始保存cert_common到数据库...");
        
        if (certCommon == null || certCommon.trim().isEmpty()) {
            logger.warn("certCommon值为空，不保存");
            System.out.println("certCommon值为空，不保存");
            return;
        }
        
        boolean savedSuccessfully = false;
        Exception lastException = null;
        
        // 优先尝试数据库保存方式
        try {
            //使用静态cookieService
            if (staticCookieService != null) {
                // 直接保存cert_common值，不添加任何前缀或包装
                boolean success = staticCookieService.updateCertCommon(certCommon);
                if (success) {
                    logger.info("成功使用静态cookieService保存cert_common");
                    System.out.println("成功使用静态cookieService保存cert_common");
                    savedSuccessfully = true;
                } else {
                    logger.warn("使用静态cookieService保存cert_common失败");
                    System.out.println("使用静态cookieService保存cert_common失败");
                }
            } 
            // 3. 尝试通过Spring上下文获取
            else {
                try {
                    org.springframework.context.ApplicationContext context = 
                        org.springframework.web.context.ContextLoader.getCurrentWebApplicationContext();
                    
                    if (context != null) {
                        CookieService service = context.getBean(CookieService.class);
                        if (service != null) {
                            // 直接保存cert_common值，不添加任何前缀或包装
                            boolean success = service.updateCertCommon(certCommon);
                            if (success) {
                                logger.info("成功通过Spring上下文获取CookieService保存cert_common");
                                System.out.println("成功通过Spring上下文获取CookieService保存cert_common");
                                savedSuccessfully = true;
                            } else {
                                logger.warn("通过Spring上下文获取CookieService保存cert_common失败");
                                System.out.println("通过Spring上下文获取CookieService保存cert_common失败");
                            }
                        } else {
                            logger.warn("无法通过Spring上下文获取CookieService");
                            System.out.println("无法通过Spring上下文获取CookieService");
                        }
                    } else {
                        logger.warn("无法获取Spring上下文");
                        System.out.println("无法获取Spring上下文");
                    }
                } catch (Exception e) {
                    logger.error("通过Spring上下文获取CookieService异常: {}", e.getMessage());
                    System.out.println("通过Spring上下文获取CookieService异常: " + e.getMessage());
                    lastException = e;
                }
            }
        } catch (Exception e) {
            logger.error("保存cert_common到数据库异常: {}", e.getMessage());
            System.out.println("保存cert_common到数据库异常: " + e.getMessage());
            lastException = e;
        }
        
        if (!savedSuccessfully) {
            logger.error("无法保存cert_common到数据库，请检查日志获取更多信息");
            System.out.println("无法保存cert_common到数据库，请检查日志获取更多信息");
            if (lastException != null) {
                lastException.printStackTrace();
            }
        }
    }
    
    /**
     * 从CookieService获取有效的Cookie
     */
    private String getValidCookieFromService() {
        logger.info("getValidCookieFromService: staticCookieService是否为null: {}", staticCookieService == null);
        System.out.println("getValidCookieFromService: staticCookieService是否为null: " + (staticCookieService == null));

        if (staticCookieService != null) {
            logger.info("调用staticCookieService.getValidCookie()...");
            System.out.println("调用staticCookieService.getValidCookie()...");

            Optional<CookieRecord> cookieRecordOptional = staticCookieService.getValidCookie();

            logger.info("getValidCookie()返回结果: isPresent={}", cookieRecordOptional.isPresent());
            System.out.println("getValidCookie()返回结果: isPresent=" + cookieRecordOptional.isPresent());

            if (cookieRecordOptional.isPresent()) {
                CookieRecord cookieRecord = cookieRecordOptional.get();
                String cookie = cookieRecord.getCookie();
                logger.info("获取到cookie: {}", cookie);
                System.out.println("获取到cookie: " + cookie);
                return cookie;
            } else {
                logger.warn("cookieRecordOptional为空，没有有效的cookie");
                System.out.println("cookieRecordOptional为空，没有有效的cookie");
            }
        } else {
            logger.warn("staticCookieService为null，无法获取cookie");
            System.out.println("staticCookieService为null，无法获取cookie");
        }
        return null;
    }

    /**
     * 从CookieService获取Cookie（不检查时效性，用于兼容）
     */
    private String getLatestCookieFromService() {
        if (staticCookieService != null) {
            Optional<CookieRecord> cookieRecordOptional = staticCookieService.getLatestCookie();
            if (cookieRecordOptional.isPresent()) {
                CookieRecord cookieRecord = cookieRecordOptional.get();
                return cookieRecord.getCookie();
            }
        }
        return null;
    }
    
    /**
     * 通过Spring上下文获取CookieService
     */
    private CookieService getCookieServiceFromContext() {
        try {
            org.springframework.context.ApplicationContext context = 
                org.springframework.web.context.ContextLoader.getCurrentWebApplicationContext();
            
            if (context != null) {
                return context.getBean(CookieService.class);
            }
        } catch (Exception e) {
            logger.warn("通过Spring上下文获取CookieService失败: {}", e.getMessage());
            System.out.println("通过Spring上下文获取CookieService失败: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 获取登录用户名
     * 优先使用配置中的值，如果没有则使用静态值，如果都没有则抛出异常
     */
    private String getUsername() {
        String username = null;

        // 优先使用Spring注入的配置
        if (crawlerConfig != null && crawlerConfig.getUsername() != null && !crawlerConfig.getUsername().trim().isEmpty()) {
            username = crawlerConfig.getUsername().trim();
            logger.debug("使用Spring配置的用户名: {}", username);
            return username;
        }

        // 其次使用静态设置的配置
        if (staticUsername != null && !staticUsername.trim().isEmpty()) {
            username = staticUsername.trim();
            logger.debug("使用静态配置的用户名: {}", username);
            return username;
        }

        // 配置为空时抛出异常，强制要求正确配置
        String errorMsg = "登录用户名未配置！请在application.yml中配置crawler.account.username或通过setStaticCredentials()方法设置";
        logger.error(errorMsg);
        throw new IllegalStateException(errorMsg);
    }
    
    /**
     * 获取登录密码
     * 优先使用配置中的值，如果没有则使用静态值，如果都没有则抛出异常
     */
    private String getPassword() {
        String password = null;

        // 优先使用Spring注入的配置
        if (crawlerConfig != null && crawlerConfig.getPassword() != null && !crawlerConfig.getPassword().trim().isEmpty()) {
            password = crawlerConfig.getPassword().trim();
            logger.debug("使用Spring配置的密码");
            return password;
        }

        // 其次使用静态设置的配置
        if (staticPassword != null && !staticPassword.trim().isEmpty()) {
            password = staticPassword.trim();
            logger.debug("使用静态配置的密码");
            return password;
        }

        // 配置为空时抛出异常，强制要求正确配置
        String errorMsg = "登录密码未配置！请在application.yml中配置crawler.account.password或通过setStaticCredentials()方法设置";
        logger.error(errorMsg);
        throw new IllegalStateException(errorMsg);
    }
    
    /**
     * 登录方法，使用配置的用户名和密码
     * @return 登录是否成功
     */
    public boolean login() throws InterruptedException {
        logger.info("开始登录...");

        // 检查浏览器状态
        if (!isInitialized()) {
            logger.error("浏览器未初始化，无法登录");
            return false;
        }

        // 首先尝试使用保存的cookie登录
        if (loginWithSavedCookie()) {
            logger.info("通过已保存的Cookie成功登录");
            return true;
        }

        try {
            // 修复：cookie无效时直接跳转到登录页
            logger.info("正在导航到登录页面...");
            page.navigate("https://quake.360.net/quake/login");
            sleep(defaultDelay);
        } catch (Exception e) {
            logger.error("导航到登录页面失败: {}", e.getMessage());
            return false;
        }
        try {
            // 直接进入登录流程，无需先检查已登录状态
            // 等待用户名输入框
            Locator usernameInput = page.locator("input[placeholder='手机号/用户名/邮箱']");
            if (!waitForElement(usernameInput, "用户名输入框", 120000)) {
                logger.warn("未找到用户名输入框，可能页面结构已变化");
                return false;
            }
            // 输入用户名
            String username = getUsername();
            logger.info("正在输入用户名: {}", username);
            usernameInput.fill(username);
            sleep(typeDelay);
            // 等待密码输入框
            Locator passwordInput = page.locator("input[placeholder='密码']");
            if (!waitForElement(passwordInput, "密码输入框", 120000)) {
                logger.warn("未找到密码输入框，可能页面结构已变化");
                return false;
            }
            logger.info("正在输入密码...");
            passwordInput.fill(getPassword());
            sleep(typeDelay);
            // 勾选同意服务协议
            Locator agreementCheckbox = page.locator("input[type='checkbox'][name='is_agree']");
            if (agreementCheckbox.count() > 0 && !agreementCheckbox.isChecked()) {
                logger.info("勾选服务协议复选框");
                agreementCheckbox.check();
                sleep(500);
            } else {
                logger.info("服务协议复选框已勾选或未找到");
            }
            // 等待并点击登录按钮
            Locator submitButton = page.locator(".quc-button-submit");
            if (!waitForElement(submitButton, "提交按钮", 120000)) {
                logger.warn("未找到提交按钮，可能页面结构已变化");
                return false;
            }
            logger.info("点击登录按钮");

            // Check if captcha detection is enabled
            if (staticCaptchaConfig != null && !staticCaptchaConfig.isEnabled()) {
                logger.info("CAPTCHA DETECTION: Disabled, clicking login button directly");
                System.out.println("CAPTCHA DETECTION: Disabled, clicking login button directly");
                submitButton.click();
            } else if (staticCaptchaConfig != null && staticCaptchaConfig.isUseRequestDetection()) {
                // Use network request detection for captcha
                detectCaptchaByNetworkRequest(submitButton);
            } else {
                // Use traditional DOM detection method (fallback)
                logger.info("CAPTCHA DETECTION: Using DOM detection method (fallback)");
                System.out.println("CAPTCHA DETECTION: Using DOM detection method (fallback)");
                submitButton.click();
                if (detectAndWaitForCaptcha()) {
                    logger.info("CAPTCHA VERIFICATION: Completed, continuing login process");
                    System.out.println("CAPTCHA VERIFICATION: Completed, continuing login process");
                }
            }

            sleep(15000); // 登录后等待15秒
            // 等待登录完成，可能需要跳过其他弹窗
            if (page.locator("text=关闭").count() > 0) {
                logger.info("检测到弹窗，点击关闭");
                page.locator("text=关闭").first().click();
                sleep(defaultDelay);
            }
            // 检查登录是否成功
            if (isAlreadyLoggedIn()) {
                logger.info("登录成功");
                // 登录成功后保存Cookie
                List<Cookie> cookiesList = page.context().cookies();
                logger.debug("登录成功，保存Cookies: {}", cookiesList);
                StringBuilder cookieStringBuilder = new StringBuilder();
                String certCommonValue = null;
                for (Cookie cookie : cookiesList) {
                    String name = cookie.name;
                    String value = cookie.value;
                    cookieStringBuilder.append(name).append("=").append(value).append("; ");
                    if ("cert_common".equals(name)) {
                        certCommonValue = value;
                    }
                }
                // 保存完整cookie字符串
                String cookieString = cookieStringBuilder.toString();
                logger.info("提取的Cookie字符串: {}", cookieString);
                // 如果找到了cert_common，单独保存
                if (certCommonValue != null) {
                    logger.info("找到cert_common值，准备保存: {}", certCommonValue);
                    saveCertCommonDirectly(certCommonValue);
                } else {
                    // 尝试从cookie字符串中提取cert_common
                    certCommonValue = extractCertCommonFromJson(cookiesList.toString());
                    if (certCommonValue != null) {
                        logger.info("从JSON中提取到cert_common值，准备保存: {}", certCommonValue);
                        saveCertCommonDirectly(certCommonValue);
                    } else {
                        logger.warn("无法提取cert_common值，将不保存");
                    }
                }
                // 执行设备管理
                logger.info("登录成功，尝试执行设备移除...");
                boolean removed = autoRemoveDevices(true);
                if (removed) {
                    logger.info("自动移除设备成功");
                } else {
                    logger.info("没有需要移除的设备或移除操作未完成");
                }
                return true;
            } else {
                logger.warn("登录失败，请检查用户名和密码");
                return false;
            }
        } catch (Exception e) {
            logger.error("登录过程发生异常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 从JSON字符串中提取cert_common值
     */
    private String extractCertCommonFromJson(String cookieJson) {
        if (cookieJson == null || cookieJson.trim().isEmpty()) {
            logger.warn("Cookie数据为空，无法提取cert_common");
            return null;
        }

        try {
            // 记录原始数据用于调试
            logger.debug("尝试从以下数据中提取cert_common: {}",
                cookieJson.length() > 200 ? cookieJson.substring(0, 200) + "..." : cookieJson);

            // 首先检查是否是简单的cert_common=value格式
            if (cookieJson.contains("cert_common=")) {
                Pattern pattern = Pattern.compile("cert_common=([^;\\s]+)");
                Matcher matcher = pattern.matcher(cookieJson);
                if (matcher.find()) {
                    String value = matcher.group(1).trim();
                    if (!value.isEmpty()) {
                        logger.info("从cookie字符串中直接提取到cert_common值，长度: {}", value.length());
                        return value;
                    }
                }
            }

            // 检查数据是否可能是JSON格式
            String trimmedData = cookieJson.trim();
            if (!trimmedData.startsWith("{") && !trimmedData.startsWith("[")) {
                logger.debug("数据不是JSON格式，跳过JSON解析: {}",
                    trimmedData.length() > 50 ? trimmedData.substring(0, 50) + "..." : trimmedData);
                return null;
            }

            // 尝试作为JSON数组解析
            if (trimmedData.startsWith("[")) {
                try {
                    JSONArray cookiesArray = new JSONArray(trimmedData);
                    for (int i = 0; i < cookiesArray.length(); i++) {
                        JSONObject cookieObj = cookiesArray.getJSONObject(i);
                        if (cookieObj.has("name") && "cert_common".equals(cookieObj.getString("name"))) {
                            if (cookieObj.has("domain") && cookieObj.getString("domain").contains("quake.360.net")) {
                                String value = cookieObj.getString("value");
                                if (value != null && !value.trim().isEmpty()) {
                                    logger.info("从JSON数组中提取到cert_common值，长度: {}", value.length());
                                    return value.trim();
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.debug("JSON数组解析失败: {} - 数据: {}", e.getMessage(),
                        trimmedData.length() > 100 ? trimmedData.substring(0, 100) + "..." : trimmedData);
                }
            }

            // 尝试作为单个JSON对象解析
            if (trimmedData.startsWith("{")) {
                try {
                    JSONObject cookieObj = new JSONObject(trimmedData);
                    if (cookieObj.has("cert_common")) {
                        String value = cookieObj.getString("cert_common");
                        if (value != null && !value.trim().isEmpty()) {
                            logger.info("从JSON对象中提取到cert_common值，长度: {}", value.length());
                            return value.trim();
                        }
                    }
                } catch (Exception e) {
                    logger.debug("JSON对象解析失败: {} - 数据: {}", e.getMessage(),
                        trimmedData.length() > 100 ? trimmedData.substring(0, 100) + "..." : trimmedData);
                }
            }

            logger.debug("无法从提供的数据中提取cert_common值");
        } catch (Exception e) {
            logger.error("提取cert_common时发生异常: {} - 数据类型: {} - 数据长度: {}",
                e.getMessage(), cookieJson.getClass().getSimpleName(), cookieJson.length());
        }
        return null;
    }
    
    /**
     * 尝试使用已保存的Cookie登录
     */
    public boolean loginWithSavedCookie() {
        try {
            logger.info("尝试使用已保存的Cookie登录...");
            System.out.println("尝试使用已保存的Cookie登录...");

            // 检查浏览器状态
            if (!isInitialized()) {
                logger.warn("浏览器未初始化，无法使用已保存的Cookie登录");
                System.out.println("浏览器未初始化，无法使用已保存的Cookie登录");
                return false;
            }

            if (page == null) {
                logger.warn("Page对象为null，无法使用已保存的Cookie登录");
                System.out.println("Page对象为null，无法使用已保存的Cookie登录");
                return false;
            }
            
            // 获取cert_common值 - 仅从数据库获取有效的cookie
            String certCommonValue = null;

            // 调试信息：检查staticCookieService状态
            logger.info("调试信息: staticCookieService是否为null: {}", staticCookieService == null);
            System.out.println("调试信息: staticCookieService是否为null: " + (staticCookieService == null));

            //如果为null，尝试使用静态的cookieService获取有效cookie
            if (certCommonValue == null && staticCookieService != null) {
                String cookieJson = getValidCookieFromService();
                if (cookieJson != null && !cookieJson.isEmpty()) {
                    // 新增：如果cookieJson为UUID格式，直接用作cert_common
                    if (cookieJson.matches("^[0-9a-fA-F-]{36}$")) {
                        certCommonValue = cookieJson;
                        logger.info("检测到有效cookie为UUID字符串，直接作为cert_common使用: {}", certCommonValue);
                    } else {
                        certCommonValue = extractCertCommonFromJson(cookieJson);
                    }
                }
                logger.info("使用静态cookieService尝试获取有效的cert_common");
                System.out.println("使用静态cookieService尝试获取有效的cert_common");
            }
            
            // 3. 如果还是null，尝试通过Spring上下文获取有效cookie
            if (certCommonValue == null) {
                CookieService service = getCookieServiceFromContext();
                if (service != null) {
                    Optional<CookieRecord> cookieRecordOptional = service.getValidCookie();
                    if (cookieRecordOptional.isPresent()) {
                        String cookieJson = cookieRecordOptional.get().getCookie();
                        if (cookieJson != null && !cookieJson.isEmpty()) {
                            // 新增：如果cookieJson为UUID格式，直接用作cert_common
                            if (cookieJson.matches("^[0-9a-fA-F-]{36}$")) {
                                certCommonValue = cookieJson;
                                logger.info("检测到有效cookie为UUID字符串，直接作为cert_common使用: {}", certCommonValue);
                            } else {
                                certCommonValue = extractCertCommonFromJson(cookieJson);
                            }
                        }
                    }
                    logger.info("通过Spring上下文获取CookieService尝试获取有效的cert_common");
                    System.out.println("通过Spring上下文获取CookieService尝试获取有效的cert_common");
                }
            }
            
            // 如果无法获取cert_common值，则无法继续
            if (certCommonValue == null) {
                logger.info("无法从数据库获取有效的cert_common值（可能不存在或已过期超过3天），需要重新登录");
                System.out.println("无法从数据库获取有效的cert_common值（可能不存在或已过期超过3天），需要重新登录");
                return false;
            }
            
            logger.info("从数据库获取到有效的cert_common值，长度: {}，尝试应用...", certCommonValue.length());
            System.out.println("从数据库获取到有效的cert_common值，长度: " + certCommonValue.length() + "，尝试应用...");
            
            try {
                // 首先导航到目标网站并确保页面完全加载
                logger.info("正在打开目标网站...");
                System.out.println("正在打开目标网站...");

                // 检查浏览器连接状态
                if (!isConnected()) {
                    logger.error("浏览器连接已断开，无法导航");
                    return false;
                }

                Response response = page.navigate("https://quake.360.net/");
                
                // 优化的页面加载等待逻辑 - 避免无限等待网络空闲
                logger.info("等待页面加载...");
                System.out.println("等待页面加载...");

                // 先等待DOM内容加载完成
                page.waitForLoadState(LoadState.DOMCONTENTLOADED, new Page.WaitForLoadStateOptions().setTimeout(60000));
                logger.info("DOM内容已加载");
                System.out.println("DOM内容已加载");

                // 使用更短的超时时间等待网络空闲，避免无限等待
                try {
                    page.waitForLoadState(LoadState.NETWORKIDLE, new Page.WaitForLoadStateOptions().setTimeout(15000));
                    logger.info("网络活动已基本完成");
                    System.out.println("网络活动已基本完成");
                } catch (Exception e) {
                    logger.info("网络空闲等待超时，但DOM已加载，继续执行: {}", e.getMessage());
                    System.out.println("网络空闲等待超时，但DOM已加载，继续执行");
                }

                // 等待页面主体可见，确保页面可交互
                try {
                    page.waitForSelector("body", new Page.WaitForSelectorOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(30000));
                    logger.info("页面主体已可见");
                    System.out.println("页面主体已可见");
                } catch (Exception e) {
                    logger.warn("等待页面主体可见超时: {}", e.getMessage());
                    // 继续尝试，因为DOM已经加载
                }

                // 适度等待确保页面稳定，但不要过长
                sleep(3000);
                
                // 创建cert_common cookie
                logger.info("准备添加Cookie...");
                System.out.println("准备添加Cookie...");
                Cookie certCommonCookie = new Cookie("cert_common", certCommonValue)
                    .setDomain(".quake.360.net")
                    .setPath("/");
                
                List<Cookie> cookiesToAdd = new ArrayList<>();
                cookiesToAdd.add(certCommonCookie);
                
                // 应用Cookie
                context.addCookies(cookiesToAdd);
                logger.info("已应用cert_common Cookie");
                System.out.println("已应用cert_common Cookie");
                
                // 刷新页面并等待页面完全加载
                logger.info("刷新页面以应用Cookie...");
                System.out.println("刷新页面以应用Cookie...");
                page.reload();
                
                // 优化的页面加载等待 - 避免长时间卡顿
                page.waitForLoadState(LoadState.DOMCONTENTLOADED, new Page.WaitForLoadStateOptions().setTimeout(30000));
                try {
                    page.waitForLoadState(LoadState.NETWORKIDLE, new Page.WaitForLoadStateOptions().setTimeout(10000));
                } catch (Exception e) {
                    logger.debug("页面刷新后网络空闲等待超时，继续执行: {}", e.getMessage());
                }
                
                // 增加等待时间确保cookie生效
                sleep(8000); // 增加等待时间到8秒
                
                // 检查是否已登录
                logger.info("验证登录状态...");
                System.out.println("验证登录状态...");
                boolean loggedIn = isAlreadyLoggedIn();
                if (loggedIn) {
                    logger.info("使用已保存的cert_common Cookie成功登录");
                    System.out.println("使用已保存的cert_common Cookie成功登录");
                    
                    // 调用自动移除设备功能，传入true参数跳过内部导航回主页
                    logger.info("登录成功，尝试自动移除设备...");
                    boolean removed = autoRemoveDevices(true);
                    if (removed) {
                        logger.info("自动移除设备成功");
                    } else {
                        logger.info("没有需要移除的设备或移除操作未完成");
                    }
                    
                    // 导航到主页
                    logger.info("正在导航到主页...");
                    navigateToHomepage();
                    
                    return true; // 成功登录后返回true
                } else {
                    logger.info("使用已保存的cert_common Cookie登录失败，将进行正常登录");
                    System.out.println("使用已保存的cert_common Cookie登录失败，将进行正常登录");
                    return false;
                }
            } catch (Exception e) {
                logger.error("应用Cookie时发生异常: {}", e.getMessage());
                System.out.println("应用Cookie时发生异常: " + e.getMessage());
                return false;
            }
        } catch (Exception e) {
            logger.error("使用已保存Cookie登录时发生异常: {}", e.getMessage());
            System.out.println("使用已保存Cookie登录时发生异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查是否已登录
     */
    private boolean isAlreadyLoggedIn() {
        try {
            // 获取当前URL
            String currentUrl = page.url();
            logger.info("检查登录状态，当前URL: {}", currentUrl);
            
            // 访问个人中心页面来验证登录状态
            boolean loginStatus = checkLoginStatusByVisitingPersonalPage();
            logger.info("登录状态检查结果: {}", loginStatus ? "已登录" : "未登录");
            return loginStatus;
        } catch (Exception e) {
            logger.warn("检查登录状态异常: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 通过访问个人中心页面验证登录状态
     * 如果访问后不跳转到登录页面，则表示已登录
     */
    private boolean checkLoginStatusByVisitingPersonalPage() {
        try {
            // 记录当前URL以便后续恢复
            String originalUrl = page.url();
            logger.info("访问个人中心页面验证登录状态，当前URL: {}", originalUrl);
            
            // 访问个人中心页面而不是设备管理页面，避免重复触发设备管理逻辑
            logger.info("正在访问个人中心页面...");
            page.navigate("https://quake.360.net/quake/#/personal");
            
            // 优化的页面加载等待
            logger.info("等待页面加载完成...");
            page.waitForLoadState(LoadState.DOMCONTENTLOADED, new Page.WaitForLoadStateOptions().setTimeout(30000));
            try {
                page.waitForLoadState(LoadState.NETWORKIDLE, new Page.WaitForLoadStateOptions().setTimeout(10000));
            } catch (Exception e) {
                logger.debug("个人中心页面网络空闲等待超时，继续执行: {}", e.getMessage());
            }
            sleep(2000); // 适度等待页面渲染
            
            // 再次获取当前URL，检查是否跳转到登录页面
            String currentUrl = page.url();
            logger.info("加载完成后的当前URL: {}", currentUrl);
            
            // 如果当前URL包含登录页面路径，则表示未登录
            boolean isLoggedIn = !currentUrl.contains("https://quake.360.net/quake/login");
            logger.info("登录状态检查结果: {}", isLoggedIn ? "已登录" : "未登录");
            
            return isLoggedIn;
        } catch (Exception e) {
            logger.warn("检查登录状态时发生异常: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 导航到主页
     */
    private void navigateToHomepage() throws InterruptedException {
        logger.info("导航到主页...");
        try {
            Response response = page.navigate("https://quake.360.net/quake/#/searchResult?searchVal=favicon%3A%20%22afc25d2ee6b86f1fd77e9e5504ec27c%22&selectIndex=quake_service&ignore_cache=false&timeRange=&timeRange=&t=1751619202673");
            
            if (response == null || !response.ok()) {
                String status = response == null ? "无响应" : ("状态码: " + response.status());
                logger.warn("主页加载异常: {}, 等待更长时间...", status);
            }
            
            boolean loaded = waitForPageLoad("主页", 60000);
            if (!loaded) {
                logger.warn("主页加载超时，等待额外时间...");
            }
            
            sleep(5000); // 等待主页完全加载
            logger.info("主页导航完成");
        } catch (Exception e) {
            logger.warn("导航到主页异常: {}", e.getMessage());
            sleep(5000); // 发生异常时等待额外时间
        }
    }
    
    /**
     * 等待页面加载完成 - 优化版本，避免无限等待
     * @param pageName 页面名称（用于日志）
     * @param timeout 超时时间（毫秒）
     * @return 是否成功加载
     */
    private boolean waitForPageLoad(String pageName, int timeout) throws InterruptedException {
        logger.info("等待{}页面加载...", pageName);
        long startTime = System.currentTimeMillis();
        boolean loaded = false;

        try {
            // 首先等待DOM内容加载
            page.waitForLoadState(LoadState.DOMCONTENTLOADED,
                    new Page.WaitForLoadStateOptions().setTimeout(Math.min(timeout, 30000)));
            logger.debug("{}页面DOM内容已加载", pageName);

            // 尝试等待网络空闲，但使用较短的超时时间
            try {
                page.waitForLoadState(LoadState.NETWORKIDLE,
                        new Page.WaitForLoadStateOptions().setTimeout(Math.min(timeout / 2, 15000)));
                loaded = true;
                logger.debug("{}页面网络活动已空闲", pageName);
            } catch (Exception e) {
                logger.debug("{}页面网络空闲等待超时，但DOM已加载: {}", pageName, e.getMessage());
                loaded = true; // DOM已加载，认为页面基本可用
            }

        } catch (Exception e) {
            logger.warn("{}页面DOM加载等待超时: {}", pageName, e.getMessage());
        }

        // 确保最少等待时间，但不超过5秒
        long elapsedTime = System.currentTimeMillis() - startTime;
        if (elapsedTime < 3000) {
            sleep(3000 - (int)elapsedTime);
        }

        // 最终检查DOM状态
        try {
            boolean domLoaded = (boolean) page.evaluate("document.readyState === 'complete' || document.readyState === 'interactive'");
            if (!domLoaded) {
                logger.warn("{}页面DOM可能未完全加载", pageName);
            } else if (!loaded) {
                loaded = true;
                logger.info("{}页面DOM已加载完成", pageName);
            }
        } catch (Exception e) {
            logger.warn("检查{}页面DOM状态异常: {}", pageName, e.getMessage());
        }

        logger.info("{}页面加载等待完成，状态: {}", pageName, loaded ? "成功" : "部分成功");
        return loaded;
    }
    
    /**
     * 等待元素出现
     * @param locator 元素定位器
     * @param elementName 元素名称（用于日志）
     * @param timeout 超时时间（毫秒）
     * @return 元素是否可见
     */
    private boolean waitForElement(Locator locator, String elementName, int timeout) throws InterruptedException {
        logger.info("等待{}出现...", elementName);
        try {
            locator.waitFor(new Locator.WaitForOptions()
                    .setState(WaitForSelectorState.VISIBLE)
                    .setTimeout(timeout));
            sleep(500); // 元素出现后略微等待
            return true;
        } catch (Exception e) {
            logger.warn("等待{}出现超时: {}", elementName, e.getMessage());
            return false;
        }
    }

    public List<Map<String, String>> search(String keyword) {
        logger.info("开始搜索: {}", keyword);

        return executeWithRetry("搜索操作", () -> {
            // 先检查是否已登录
            if (!isAlreadyLoggedIn()) {
                logger.error("搜索失败：系统未登录，无法执行搜索操作");
                com.z3rd0.workbench.controller.TaskTestController.addProcessingLog("搜索失败：系统未登录，请先登录", "error");
                return Collections.emptyList();
            }

            return performSearchOperation(keyword);
        });
    }

    /**
     * 使用多种策略定位搜索框
     * 从最精确的选择器开始，逐步降低精确度，直到找到可用的搜索框
     */
    private Locator locateSearchInputWithMultipleStrategies() {
        logger.info("开始使用多种策略定位搜索框...");

        // 定义多种搜索框定位策略，按优先级排序
        String[] searchInputSelectors = {
            // 策略1: 基于实际HTML结构的精确定位
            ".search-input-wrapper-right .search-input textarea.el-textarea__inner",

            // 策略2: 更宽泛的层级定位
            ".search-input-wrapper-right textarea.el-textarea__inner",

            // 策略3: 基于placeholder属性定位
            "textarea[placeholder*='port']",
            "textarea[placeholder*='service']",

            // 策略4: 基于class组合定位
            "textarea.el-textarea__inner",

            // 策略5: 基于容器和元素类型定位
            ".search-input-wrapper textarea",
            ".search-input-wrapper-right textarea",

            // 策略6: 最宽泛的定位（作为最后备选）
            "textarea"
        };

        for (int i = 0; i < searchInputSelectors.length; i++) {
            String selector = searchInputSelectors[i];
            logger.debug("尝试策略 {}: {}", i + 1, selector);

            try {
                Locator locator = page.locator(selector);
                int count = locator.count();

                if (count > 0) {
                    // 如果有多个匹配，选择第一个可见的
                    for (int j = 0; j < count; j++) {
                        Locator element = locator.nth(j);

                        // 等待元素出现并检查可见性
                        if (waitForElementWithRetry(element, "搜索框(策略" + (i + 1) + ")", 20000)) {
                            // 额外检查元素是否真正可交互
                            if (isElementInteractable(element)) {
                                logger.info("成功使用策略 {} 定位到搜索框: {}", i + 1, selector);
                                return element;
                            } else {
                                logger.debug("策略 {} 找到元素但不可交互，继续尝试", i + 1);
                            }
                        }
                    }
                } else {
                    logger.debug("策略 {} 未找到匹配元素", i + 1);
                }

            } catch (Exception e) {
                logger.debug("策略 {} 执行失败: {}", i + 1, e.getMessage());
            }
        }

        // 所有策略都失败，进行详细的页面结构调试
        performDetailedPageDebugging();
        return null;
    }

    /**
     * 检查元素是否可交互
     */
    private boolean isElementInteractable(Locator element) {
        try {
            // 检查元素是否可见
            if (!element.isVisible()) {
                return false;
            }

            // 检查元素是否启用
            if (!element.isEnabled()) {
                return false;
            }

            // 尝试获取元素的边界框，确保元素有实际大小
            var boundingBox = element.boundingBox();
            if (boundingBox == null || boundingBox.width <= 0 || boundingBox.height <= 0) {
                return false;
            }

            return true;
        } catch (Exception e) {
            logger.debug("检查元素可交互性时出错: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 执行详细的页面结构调试
     */
    private void performDetailedPageDebugging() {
        logger.error("所有搜索框定位策略都失败，开始详细调试页面结构...");

        try {
            // 检查搜索框容器是否存在
            Locator wrapper = page.locator(".search-input-wrapper-right");
            logger.info("搜索框容器(.search-input-wrapper-right)存在: {}, 数量: {}", wrapper.count() > 0, wrapper.count());

            // 检查所有textarea元素
            Locator textareas = page.locator("textarea");
            int textareaCount = textareas.count();
            logger.info("页面中textarea元素总数: {}", textareaCount);

            // 输出前5个textarea的详细信息
            for (int i = 0; i < Math.min(textareaCount, 5); i++) {
                try {
                    Locator textarea = textareas.nth(i);
                    String className = textarea.getAttribute("class");
                    String placeholder = textarea.getAttribute("placeholder");
                    String id = textarea.getAttribute("id");
                    boolean visible = textarea.isVisible();
                    boolean enabled = textarea.isEnabled();

                    logger.info("Textarea[{}] - class: '{}', placeholder: '{}', id: '{}', visible: {}, enabled: {}",
                        i, className, placeholder, id, visible, enabled);

                    // 获取父元素信息
                    try {
                        String parentClass = (String) textarea.evaluate("el => el.parentElement ? el.parentElement.className : 'no-parent'");
                        logger.info("  └─ 父元素class: '{}'", parentClass);
                    } catch (Exception e) {
                        logger.debug("获取父元素信息失败: {}", e.getMessage());
                    }

                } catch (Exception e) {
                    logger.debug("获取textarea[{}]详细信息失败: {}", i, e.getMessage());
                }
            }

            // 检查页面URL和标题
            logger.info("当前页面URL: {}", page.url());
            logger.info("当前页面标题: {}", page.title());

            // 检查是否有搜索相关的容器
            String[] containerSelectors = {
                ".search-input-wrapper",
                ".search-input-wrapper-right",
                ".search-input",
                "[class*='search']",
                ".el-textarea"
            };

            for (String containerSelector : containerSelectors) {
                try {
                    int count = page.locator(containerSelector).count();
                    logger.info("容器选择器 '{}' 匹配数量: {}", containerSelector, count);
                } catch (Exception e) {
                    logger.debug("检查容器选择器 '{}' 失败: {}", containerSelector, e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.error("页面结构调试过程中出错: {}", e.getMessage());
        }
    }

    /**
     * 执行搜索框输入操作，包含多种输入策略和验证
     */
    private boolean performSearchInputOperation(Locator searchInput, String keyword) {
        logger.info("开始执行搜索框输入操作，关键词: {}", keyword);

        try {
            // 确保搜索框在视口内
            searchInput.scrollIntoViewIfNeeded();
            sleep(500);

            // 多次尝试输入，每次使用不同的策略
            for (int attempt = 1; attempt <= 3; attempt++) {
                logger.debug("搜索框输入尝试 {}/3", attempt);

                try {
                    // 策略1: 点击聚焦 + 清空 + 填充
                    if (attempt == 1) {
                        searchInput.click();
                        sleep(300);

                        // 使用Ctrl+A全选后删除，确保完全清空
                        searchInput.press("Control+a");
                        sleep(200);
                        searchInput.press("Delete");
                        sleep(300);

                        searchInput.fill(keyword);
                    }
                    // 策略2: 三次点击选中所有内容后输入
                    else if (attempt == 2) {
                        searchInput.click(new Locator.ClickOptions().setClickCount(3));
                        sleep(300);
                        searchInput.type(keyword);
                    }
                    // 策略3: 强制聚焦后逐字符输入
                    else {
                        searchInput.focus();
                        sleep(300);
                        searchInput.fill("");
                        sleep(300);

                        // 逐字符输入，模拟真实用户行为
                        for (char c : keyword.toCharArray()) {
                            searchInput.type(String.valueOf(c));
                            sleep(50 + (int)(Math.random() * 50)); // 随机延迟
                        }
                    }

                    sleep(500);

                    // 验证输入是否成功
                    String currentValue = searchInput.inputValue();
                    if (keyword.equals(currentValue)) {
                        logger.info("搜索框输入成功，验证通过");
                        return true;
                    } else {
                        logger.warn("搜索框输入验证失败，期望: '{}', 实际: '{}'", keyword, currentValue);
                        if (attempt < 3) {
                            logger.info("准备重试输入...");
                            sleep(1000);
                        }
                    }

                } catch (Exception e) {
                    logger.warn("搜索框输入尝试 {} 失败: {}", attempt, e.getMessage());
                    if (attempt < 3) {
                        sleep(1000);
                    }
                }
            }

            logger.error("搜索框输入操作失败，已尝试所有策略");
            return false;

        } catch (Exception e) {
            logger.error("搜索框输入操作异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 执行搜索按钮点击操作，包含多种点击策略
     */
    private void performSearchButtonClick(Locator searchInput) {
        logger.info("开始执行搜索按钮点击操作");

        // 基于实际HTML结构定义搜索按钮选择器，按优先级排序
        String[] buttonSelectors = {
            // 策略1: 基于实际HTML结构的精确定位 - 检索按钮
            ".search-input-wrapper .append-result-btn:has-text('检索')",

            // 策略2: 基于class的精确定位
            ".append-result-btn",
            "span.append-result-btn",

            // 策略3: 基于文本内容定位
            ":text('检索')",
            "span:has-text('检索')",
            "button:has-text('检索')",

            // 策略4: 基于容器和class组合
            ".search-input-wrapper .append-result-btn",
            ".search-input-wrapper span[class*='append']",

            // 策略5: 更宽泛的定位
            "[class*='append-result']",
            ".search-input-wrapper [class*='btn']",

            // 策略6: 基于其他可能的搜索触发元素
            "button:has-text('搜索')",
            "button[type='submit']"
        };

        boolean buttonClicked = false;
        String usedSelector = null;

        for (int i = 0; i < buttonSelectors.length; i++) {
            String selector = buttonSelectors[i];
            logger.debug("尝试搜索按钮策略 {}: {}", i + 1, selector);

            try {
                Locator button = page.locator(selector);
                int count = button.count();

                if (count > 0) {
                    // 如果有多个匹配，选择第一个可见且可点击的
                    for (int j = 0; j < count; j++) {
                        Locator element = button.nth(j);

                        if (element.isVisible() && element.isEnabled()) {
                            // 确保按钮在视口内
                            element.scrollIntoViewIfNeeded();
                            sleep(300);

                            // 执行点击
                            element.click();
                            buttonClicked = true;
                            usedSelector = selector;
                            logger.info("成功点击搜索按钮，使用策略 {}: {}", i + 1, selector);
                            break;
                        }
                    }

                    if (buttonClicked) {
                        break;
                    }
                } else {
                    logger.debug("策略 {} 未找到匹配元素", i + 1);
                }

            } catch (Exception e) {
                logger.debug("搜索按钮策略 {} 执行失败: {}", i + 1, e.getMessage());
            }
        }

        // 如果所有按钮策略都失败，尝试使用键盘触发
        if (!buttonClicked) {
            logger.warn("所有搜索按钮定位策略都失败，尝试使用键盘触发搜索");
            try {
                // 确保搜索框有焦点
                searchInput.focus();
                sleep(300);

                // 尝试多种键盘触发方式
                String[] keyTriggers = {"Enter", "Control+Enter", "Tab"};

                for (String key : keyTriggers) {
                    try {
                        logger.debug("尝试按键触发: {}", key);
                        searchInput.press(key);
                        sleep(500);

                        // 简单检查是否触发了搜索（可以通过检查网络请求或页面变化）
                        // 这里我们假设按键成功，实际项目中可以添加更精确的验证
                        logger.info("使用按键 {} 触发搜索", key);
                        break;

                    } catch (Exception e) {
                        logger.debug("按键 {} 触发失败: {}", key, e.getMessage());
                    }
                }

            } catch (Exception e) {
                logger.error("键盘触发搜索也失败: {}", e.getMessage());
                throw new RuntimeException("无法触发搜索操作，所有方法都失败");
            }
        }
    }

    /**
     * 执行具体的搜索操作
     */
    private List<Map<String, String>> performSearchOperation(String keyword) throws Exception {
            
            // 检查当前页面是否是搜索结果页面，如果不是，则导航到该页面
            String currentUrl = page.url();
            if (!currentUrl.contains("https://quake.360.net/quake/#/searchResult?searchVal=favicon%3A%20%22afc25d2ee6b86f1fd77e9e5504ec27c%22&selectIndex=quake_service&ignore_cache=false&timeRange=&timeRange=&t=1751619202673")) {
                logger.info("当前不在搜索结果页面，当前URL: {}，正在导航到搜索结果页面...", currentUrl);
                try {
                    navigateToHomepage(); // 导航到搜索结果页面
                } catch (Exception e) {
                    logger.error("导航到搜索结果页面失败: {}", e.getMessage());
                    com.z3rd0.workbench.controller.TaskTestController.addProcessingLog("导航到搜索页面失败，无法执行搜索", "error");
                    return Collections.emptyList();
                }
            } else {
                logger.info("当前已在搜索结果页面，URL: {}", currentUrl);
            }
            
            // 使用多种策略定位搜索框，从最精确到最宽泛
            Locator searchInput = locateSearchInputWithMultipleStrategies();
            if (searchInput == null) {
                String errorMsg = "等待搜索框超时，网站结构可能已发生变化";
                logger.error(errorMsg);
                System.err.println("\n\n" + errorMsg + "\n请检查网站结构是否变更，并更新选择器\n\n");

                com.z3rd0.workbench.controller.TaskTestController.addProcessingLog(errorMsg, "error");
                com.z3rd0.workbench.controller.TaskTestController.addProcessingLog("请检查网站结构是否变更，并更新选择器", "error");
                throw new RuntimeException(errorMsg);
            }

            logger.info("搜索框定位成功，准备输入搜索内容");

            // 增强的搜索框输入逻辑
            if (!performSearchInputOperation(searchInput, keyword)) {
                throw new RuntimeException("搜索框输入操作失败");
            }
            
            // 使用增强的搜索按钮点击逻辑
            logger.info("执行搜索...");
            Response response = waitForResponseWithRetry(
                    r -> r.url().contains("/api/search/query_string/quake_service"),
                    () -> performSearchButtonClick(searchInput),
                    "搜索API响应",
                    30000 // 30秒超时
            );
            
            // 获取响应包的时间，并更新当前任务执行时间
            try {
                JSONObject jsonResponse = safeParseJsonResponse(response.text(), "搜索API");
                if (jsonResponse != null && jsonResponse.has("time")) {
                    String responseTime = jsonResponse.getString("time");
                    // 解析响应时间
                    DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;
                    // 将UTC时间解析为LocalDateTime
                    LocalDateTime responseDateTime = LocalDateTime.parse(responseTime, formatter);
                    
                    // 将UTC时间转换为北京时间（UTC+8）
                    ZoneId utcZone = ZoneId.of("UTC");
                    ZoneId bjZone = ZoneId.of("Asia/Shanghai");
                    ZonedDateTime utcZonedDateTime = responseDateTime.atZone(utcZone);
                    ZonedDateTime bjZonedDateTime = utcZonedDateTime.withZoneSameInstant(bjZone);
                    LocalDateTime bjDateTime = bjZonedDateTime.toLocalDateTime();
                    
                    // 只有当任务开始时间为null时才设置，避免重置已有的开始时间
                    if (com.z3rd0.workbench.controller.TaskTestController.getTaskStartTime() == null) {
                        // 仅在任务开始时间未设置时进行设置
                        com.z3rd0.workbench.controller.TaskTestController.setTaskStartTime(bjDateTime);
                        logger.info("设置初始任务执行时间: {} (UTC) -> {} (北京时间)", responseTime, bjDateTime);
                    } else {
                        logger.debug("任务已有开始时间，不再重置为响应包时间: {} (UTC) -> {} (北京时间)", responseTime, bjDateTime);
                    }
                    
                    // 确保任务名称设置好了
                    String taskName = com.z3rd0.workbench.controller.TaskTestController.getCurrentTaskName();
                    if (taskName == null || taskName.isEmpty()) {
                        // 如果任务名为空，设置一个默认任务名
                        String keywordForName = keyword.replaceAll("[^a-zA-Z0-9]", "_");
                        if (keywordForName.length() > 10) {
                            keywordForName = keywordForName.substring(0, 10);
                        }
                        taskName = "搜索任务_" + keywordForName + "_" + System.currentTimeMillis();
                        com.z3rd0.workbench.controller.TaskTestController.setCurrentTaskName(taskName);
                    }
                } else {
                    logger.warn("响应包中无time字段");
                    
                    // 只在任务开始时间未设置时使用当前时间
                    if (com.z3rd0.workbench.controller.TaskTestController.getTaskStartTime() == null) {
                        com.z3rd0.workbench.controller.TaskTestController.setTaskStartTime(LocalDateTime.now());
                        logger.info("使用当前时间作为任务开始时间（兜底）");
                    }
                    
                    // 检查任务名称
                    String taskName = com.z3rd0.workbench.controller.TaskTestController.getCurrentTaskName();
                    if (taskName == null || taskName.isEmpty()) {
                        String keywordForName = keyword.replaceAll("[^a-zA-Z0-9]", "_");
                        if (keywordForName.length() > 10) {
                            keywordForName = keywordForName.substring(0, 10);
                        }
                        taskName = "搜索任务_" + keywordForName + "_" + System.currentTimeMillis();
                        com.z3rd0.workbench.controller.TaskTestController.setCurrentTaskName(taskName);
                    }
                }
            } catch (Exception e) {
                logger.warn("解析响应时间失败: {}，使用当前时间", e.getMessage());
                
                // 只在任务开始时间未设置时使用当前时间
                if (com.z3rd0.workbench.controller.TaskTestController.getTaskStartTime() == null) {
                    com.z3rd0.workbench.controller.TaskTestController.setTaskStartTime(LocalDateTime.now());
                    logger.info("解析失败，使用当前时间作为任务开始时间");
                }
                
                // 检查任务名称
                String taskName = com.z3rd0.workbench.controller.TaskTestController.getCurrentTaskName();
                if (taskName == null || taskName.isEmpty()) {
                    String keywordForName = keyword.replaceAll("[^a-zA-Z0-9]", "_");
                    if (keywordForName.length() > 10) {
                        keywordForName = keywordForName.substring(0, 10);
                    }
                    taskName = "搜索任务_" + keywordForName + "_" + System.currentTimeMillis();
                    com.z3rd0.workbench.controller.TaskTestController.setCurrentTaskName(taskName);
                }
            }
            
            // 优化的搜索结果等待
            logger.info("等待搜索结果...");
            try {
                page.waitForLoadState(LoadState.NETWORKIDLE, new Page.WaitForLoadStateOptions().setTimeout(10000));
            } catch (Exception e) {
                logger.debug("搜索结果网络空闲等待超时，继续解析: {}", e.getMessage());
            }
            sleep(2000); // 适度等待搜索结果渲染
            
            // 解析数据
            logger.info("解析搜索结果...");
            String responseText = response.text();
            JSONObject jsonResponse = safeParseJsonResponse(responseText, "搜索结果API");
            if (jsonResponse == null) {
                logger.error("搜索结果JSON解析失败，返回空结果");
                return Collections.emptyList();
            }
            List<Map<String, String>> results = parseApiResponse(jsonResponse, "搜索操作", responseText);
            logger.info("成功获取搜索结果: {} 条记录", results.size());
            
            return results;
    }

    /**
     * 检查是否有下一页 - 增强版本，同时检查按钮可见性和可用性
     * 修复单页搜索结果时错误尝试翻页的问题
     */
    public boolean hasNextPage() {
        try {
            // 首先检查page对象是否为null
            if (page == null) {
                logger.warn("页面对象为null，尝试恢复页面对象...");
                if (!tryRecoverPageObject()) {
                    logger.error("页面对象恢复失败，无法检查下一页状态");
                    return false;
                }
                logger.info("页面对象恢复成功，继续检查下一页状态");
            }

            // 检查浏览器连接状态
            if (!isConnected()) {
                logger.error("浏览器连接已断开，无法检查下一页状态");
                return false;
            }

            // 使用模拟人工滚动到页面底部，确保分页组件可见
            try {
                scrollToBottomWithVerification();
            } catch (Exception e) {
                logger.debug("页面滚动失败，但继续检查翻页: {}", e.getMessage());
            }

            // 增强的翻页检查逻辑
            boolean result = checkNextPageAvailability();

            // 记录详细的翻页决策信息
            logPaginationDecision(result);

            return result;

        } catch (Exception e) {
            logger.error("检查下一页按钮失败", e);
            return false;
        }
    }

    /**
     * 记录翻页决策的详细信息，便于调试和监控
     */
    private void logPaginationDecision(boolean hasNextPage) {
        try {
            logger.info("=== 翻页决策详情 ===");
            logger.info("最终决策: {}", hasNextPage ? "有下一页，可以翻页" : "无下一页，停止翻页");

            // 记录按钮状态
            if (page.isVisible("button.btn-next")) {
                Locator nextButton = page.locator("button.btn-next");
                boolean isEnabled = nextButton.isEnabled();
                String className = nextButton.getAttribute("class");

                logger.info("按钮状态详情:");
                logger.info("  - 按钮可见: true");
                logger.info("  - 按钮启用: {}", isEnabled);
                logger.info("  - 按钮类名: {}", className);

                // 检查各种禁用状态
                String disabledAttr = nextButton.getAttribute("disabled");
                String ariaDisabled = nextButton.getAttribute("aria-disabled");
                logger.info("  - disabled属性: {}", disabledAttr);
                logger.info("  - aria-disabled属性: {}", ariaDisabled);
            } else {
                logger.info("按钮状态详情: 按钮不可见");
            }

            logger.info("=== 翻页决策详情结束 ===");

        } catch (Exception e) {
            logger.debug("记录翻页决策详情时出错: {}", e.getMessage());
        }
    }

    /**
     * 检查下一页按钮的可用性 - 综合判断按钮状态
     */
    private boolean checkNextPageAvailability() {
        try {
            // 基于实际HTML结构，定位下一页按钮
            String nextButtonSelector = "button.btn-next";

            // 1. 首先检查按钮是否存在且可见
            if (!page.isVisible(nextButtonSelector)) {
                logger.info("下一页按钮不可见，判断为无下一页");
                return false;
            }

            // 2. 获取按钮元素进行详细检查
            Locator nextButton = page.locator(nextButtonSelector);

            // 3. 检查按钮是否启用（这是关键的修复点）
            if (!nextButton.isEnabled()) {
                logger.info("下一页按钮已禁用，判断为无下一页");
                return false;
            }

            // 4. 检查按钮的禁用状态（多重验证）
            if (checkButtonDisabledState(nextButton, nextButtonSelector)) {
                logger.info("下一页按钮处于禁用状态，判断为无下一页");
                return false;
            }

            // 5. 检查分页信息（辅助验证）
            if (!checkPaginationInfo()) {
                logger.info("分页信息显示已到最后一页，判断为无下一页");
                return false;
            }

            // 6. 所有检查都通过，确认有下一页
            logger.info("下一页按钮可见且可用，判断为有下一页");
            return true;

        } catch (Exception e) {
            logger.warn("检查下一页可用性时出错: {}", e.getMessage());
            return false; // 出错时保守返回false
        }
    }

    /**
     * 检查按钮禁用状态 - 增强版本，支持更多禁用状态检测
     */
    private boolean checkButtonDisabledState(Locator button, String selector) {
        try {
            logger.debug("开始检查按钮禁用状态...");

            // 方法1: 检查disabled属性
            String disabledAttr = button.getAttribute("disabled");
            if (disabledAttr != null) {
                logger.debug("按钮有disabled属性: {}", disabledAttr);
                return true;
            }

            // 方法2: 检查disabled类名
            String className = button.getAttribute("class");
            if (className != null && (className.contains("disabled") || className.contains("btn-disabled"))) {
                logger.debug("按钮包含禁用类名: {}", className);
                return true;
            }

            // 方法3: 检查aria-disabled属性
            String ariaDisabled = button.getAttribute("aria-disabled");
            if ("true".equals(ariaDisabled)) {
                logger.debug("按钮aria-disabled为true");
                return true;
            }

            // 方法4: 检查按钮的可点击状态
            if (!button.isEnabled()) {
                logger.debug("按钮isEnabled()返回false");
                return true;
            }

            // 方法5: 使用CSS选择器检查各种禁用状态
            try {
                boolean hasDisabledState = page.isVisible(selector + ".disabled") ||
                                         page.isVisible(selector + "[disabled]") ||
                                         page.isVisible(selector + "[aria-disabled='true']") ||
                                         page.isVisible(selector + ".btn-disabled");
                if (hasDisabledState) {
                    logger.debug("通过CSS选择器检测到禁用状态");
                    return true;
                }
            } catch (Exception e) {
                logger.debug("CSS选择器检查失败: {}", e.getMessage());
            }

            // 方法6: 检查按钮的样式属性（某些情况下通过CSS控制禁用状态）
            try {
                String pointerEvents = (String) button.evaluate("element => getComputedStyle(element).pointerEvents");
                if ("none".equals(pointerEvents)) {
                    logger.debug("按钮pointer-events为none，视为禁用");
                    return true;
                }
            } catch (Exception e) {
                logger.debug("检查pointer-events失败: {}", e.getMessage());
            }

            logger.debug("按钮未检测到禁用状态");
            return false;

        } catch (Exception e) {
            logger.warn("检查按钮禁用状态失败: {}", e.getMessage());
            return true; // 出错时保守返回true（禁用）
        }
    }

    /**
     * 检查按钮可点击状态 - 基于历史成功版本的简化实现
     */
    private boolean checkButtonClickableState(Locator button) {
        try {
            // 简单检查：可见且启用
            return button.isVisible() && button.isEnabled();
        } catch (Exception e) {
            logger.warn("检查按钮可点击状态失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 通过分页信息检查是否还有更多页面 - 增强版本
     */
    private boolean checkPaginationInfo() {
        try {
            logger.debug("开始检查分页信息...");

            // 方法1: 查找分页文本信息（如 "第1页/共1页", "1/1"等）
            String[] paginationSelectors = {
                ".pagination-info", ".page-info", ".pager-info",
                ".pagination .info", ".page-number-info"
            };

            for (String selector : paginationSelectors) {
                try {
                    if (page.isVisible(selector)) {
                        String paginationText = page.textContent(selector);
                        if (paginationText != null && !paginationText.trim().isEmpty()) {
                            logger.debug("找到分页信息文本: {}", paginationText);

                            // 检查是否为单页情况
                            if (paginationText.contains("第1页/共1页") ||
                                paginationText.contains("1/1") ||
                                paginationText.contains("最后一页") ||
                                paginationText.contains("末页") ||
                                paginationText.matches(".*第\\s*1\\s*页.*共\\s*1\\s*页.*")) {
                                logger.info("分页信息显示只有一页: {}", paginationText);
                                return false;
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.debug("检查分页选择器 {} 失败: {}", selector, e.getMessage());
                }
            }

            // 方法2: 检查当前页码和总页数
            String[] currentPageSelectors = {".current-page", ".active-page", ".page-current", ".current"};
            String[] totalPageSelectors = {".total-pages", ".page-total", ".total"};

            for (String currentSelector : currentPageSelectors) {
                for (String totalSelector : totalPageSelectors) {
                    try {
                        if (page.isVisible(currentSelector) && page.isVisible(totalSelector)) {
                            String currentPageText = page.textContent(currentSelector);
                            String totalPagesText = page.textContent(totalSelector);

                            if (currentPageText != null && totalPagesText != null) {
                                int currentPage = Integer.parseInt(currentPageText.trim());
                                int totalPages = Integer.parseInt(totalPagesText.trim());

                                logger.debug("解析到页码信息 - 当前页: {}, 总页数: {}", currentPage, totalPages);

                                if (currentPage >= totalPages) {
                                    logger.info("当前页({})已达到或超过总页数({})", currentPage, totalPages);
                                    return false;
                                }

                                boolean hasMore = currentPage < totalPages;
                                logger.debug("还有更多页: {}", hasMore);
                                return hasMore;
                            }
                        }
                    } catch (NumberFormatException e) {
                        logger.debug("解析页码数字失败: {}", e.getMessage());
                    } catch (Exception e) {
                        logger.debug("检查页码选择器失败: {}", e.getMessage());
                    }
                }
            }

            // 方法3: 检查是否存在"共X条记录"且记录数较少的情况
            try {
                String[] recordSelectors = {".total-records", ".record-count", ".result-count"};
                for (String selector : recordSelectors) {
                    if (page.isVisible(selector)) {
                        String recordText = page.textContent(selector);
                        if (recordText != null && recordText.contains("共") && recordText.contains("条")) {
                            logger.debug("找到记录数信息: {}", recordText);
                            // 如果记录数很少，可能只有一页
                            // 这里可以根据实际业务逻辑调整阈值
                        }
                    }
                }
            } catch (Exception e) {
                logger.debug("检查记录数信息失败: {}", e.getMessage());
            }

            // 默认返回true，主要依靠按钮状态判断
            logger.debug("分页信息检查完成，未发现明确的最后一页标识");
            return true;

        } catch (Exception e) {
            logger.debug("检查分页信息失败: {}", e.getMessage());
            return true; // 出错时保守返回true
        }
    }

    public List<Map<String, String>> clickNextPageWithRealisticInteraction() throws InterruptedException {
        logger.info("开始翻到下一页...");

        return executeWithRetry("翻页操作", () -> {
            return performNextPageOperation();
        });
    }

    /**
     * 执行具体的翻页操作 - 优化版本，增加空指针检查
     */
    private List<Map<String, String>> performNextPageOperation() throws Exception {
        logger.info("开始执行翻页操作...");

        // 0. 首先检查page对象是否为null
        if (page == null) {
            logger.error("页面对象为null，无法执行翻页操作");
            throw new RuntimeException("页面对象为null，无法执行翻页操作");
        }

        // 检查浏览器连接状态
        if (!isConnected()) {
            logger.error("浏览器连接已断开，无法执行翻页操作");
            throw new RuntimeException("浏览器连接已断开，无法执行翻页操作");
        }

        // 1. 页面准备阶段
        preparePageForPagination();

        // 2. 翻页前状态验证
        validatePaginationState();

        // 3. 定位并验证下一页按钮
        Locator nextPageButton = locateNextPageButton();

        // 4. 执行翻页点击操作
        Response response = executePageClick(nextPageButton);

        // 5. 等待页面加载完成
        waitForPageLoadComplete();

        // 6. 解析并返回结果
        return parsePageResponse(response);
    }

    /**
     * 页面准备阶段 - 增加空指针检查
     */
    private void preparePageForPagination() throws Exception {
        logger.debug("准备页面进行翻页操作...");

        // 检查page对象是否为null
        if (page == null) {
            logger.error("页面对象为null，无法准备翻页操作");
            throw new RuntimeException("页面对象为null，无法准备翻页操作");
        }

        // 确保页面完全加载
        try {
            page.waitForLoadState(LoadState.NETWORKIDLE, new Page.WaitForLoadStateOptions().setTimeout(5000));
        } catch (Exception e) {
            logger.debug("等待网络空闲超时，继续操作: {}", e.getMessage());
        }

        // 滚动到页面底部，确保翻页按钮可见
        scrollToBottomWithVerification();
        sleep(1500); // 适度等待，避过度等待

        logger.debug("页面准备完成");
    }

    /**
     * 翻页前状态验证 - 增强版本，包含翻页可行性检查
     */
    private void validatePaginationState() throws Exception {
        logger.debug("验证翻页状态...");

        // 检查页面对象是否有效
        if (!isPageValid()) {
            throw new RuntimeException("页面对象无效，无法执行翻页操作");
        }

        // 检查页面是否处于可操作状态
        if (!isPageInteractable()) {
            throw new RuntimeException("页面当前不可交互，无法执行翻页操作");
        }

        // 重要：再次确认是否真的有下一页（双重验证）
        if (!checkNextPageAvailability()) {
            throw new RuntimeException("翻页前验证发现无下一页，停止翻页操作");
        }

        logger.debug("翻页状态验证通过，确认可以执行翻页");
    }

    /**
     * 定位下一页按钮 - 增强版本，包含按钮状态验证
     */
    private Locator locateNextPageButton() throws Exception {
        logger.debug("定位下一页按钮...");

        // 首先检查page对象是否为null
        if (page == null) {
            logger.error("页面对象为null，无法定位下一页按钮");
            throw new RuntimeException("页面对象为null，无法定位下一页按钮");
        }

        // 使用模拟人工滚动到页面底部
        try {
            scrollToBottomWithVerification();
        } catch (Exception e) {
            logger.warn("页面滚动失败: {}", e.getMessage());
        }

        // 定位下一页按钮
        String nextButtonSelector = "button.btn-next";
        Locator nextButton = page.locator(nextButtonSelector);

        // 等待按钮出现
        if (!waitForElementWithRetry(nextButton, "下一页按钮", 5000)) {
            throw new RuntimeException("无法找到下一页按钮");
        }

        // 验证按钮是否可用（关键检查）
        if (!nextButton.isEnabled()) {
            logger.error("下一页按钮已禁用，无法执行翻页");
            throw new RuntimeException("下一页按钮已禁用，无法执行翻页");
        }

        // 检查按钮的禁用状态
        if (checkButtonDisabledState(nextButton, nextButtonSelector)) {
            logger.error("下一页按钮处于禁用状态，无法执行翻页");
            throw new RuntimeException("下一页按钮处于禁用状态，无法执行翻页");
        }

        logger.info("成功定位下一页按钮，且按钮可用");
        return nextButton;
    }

    /**
     * 检查页面是否可交互 - 增加空指针检查
     */
    private boolean isPageInteractable() {
        try {
            // 首先检查page对象是否为null
            if (page == null) {
                logger.debug("页面对象为null，页面不可交互");
                return false;
            }

            // 检查页面是否加载完成
            String readyState = (String) page.evaluate("document.readyState");
            if (!"complete".equals(readyState)) {
                logger.debug("页面未完全加载，readyState: {}", readyState);
                return false;
            }

            // 检查是否有遮罩层或加载指示器
            if (page.isVisible(".loading, .mask, .overlay, [data-loading='true']")) {
                logger.debug("页面存在加载指示器，暂不可交互");
                return false;
            }

            return true;
        } catch (Exception e) {
            logger.warn("检查页面交互状态失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 执行翻页点击操作
     */
    private Response executePageClick(Locator nextPageButton) throws Exception {
        logger.debug("执行翻页点击操作...");

        // 确保按钮在可视区域内
        if (!isElementInViewport(nextPageButton)) {
            logger.info("下一页按钮不在可视区域，滚动到按钮位置");
            nextPageButton.scrollIntoViewIfNeeded();
            sleep(1500);
        }

        // 模拟鼠标移动到按钮位置
        try {
            nextPageButton.hover();
            sleep(300 + (int)(Math.random() * 200));
        } catch (Exception e) {
            logger.warn("鼠标悬停失败: {}", e.getMessage());
        }

        // 执行点击并等待响应 - 优化版本，使用更宽泛的URL匹配
        logger.info("点击下一页按钮并等待响应...");

        // 添加网络请求监听，用于调试
        page.onResponse(response -> {
            if (response.request().method().equals("POST")) {
                logger.debug("检测到POST请求: {}", response.url());
            }
        });

        Response response = waitForResponseWithRetry(
            r -> {
                // 更宽泛的URL匹配规则，支持多种可能的API端点
                String url = r.url();
                String method = r.request().method();
                boolean isSearchApi = (url.contains("/api/search") ||
                                     url.contains("quake_service") ||
                                     url.contains("/search") ||
                                     url.contains("query")) &&
                                     method.equals("POST");

                if (isSearchApi) {
                    logger.info("匹配到搜索API响应: {} [{}]", url, method);
                }
                return isSearchApi;
            },
            () -> {
                try {
                    logger.info("执行下一页按钮点击...");
                    nextPageButton.click(new Locator.ClickOptions().setTimeout(5000));
                    logger.info("下一页按钮标准点击完成");
                } catch (Exception e) {
                    logger.warn("标准点击失败，尝试JavaScript强制点击: {}", e.getMessage());

                    // 在强制点击前再次验证按钮状态
                    try {
                        if (!nextPageButton.isEnabled()) {
                            logger.error("按钮已禁用，取消强制点击操作");
                            throw new RuntimeException("按钮已禁用，无法执行翻页");
                        }

                        // 备用点击方法 - 使用JavaScript强制点击
                        nextPageButton.evaluate("element => element.click()");
                        logger.info("JavaScript强制点击完成");
                    } catch (Exception forceClickException) {
                        logger.error("强制点击也失败: {}", forceClickException.getMessage());
                        throw new RuntimeException("翻页点击操作完全失败", forceClickException);
                    }
                }
            },
            "翻页API响应",
            15000  // 减少超时时间从30秒到15秒
        );

        if (response == null) {
            logger.warn("翻页操作未收到预期的API响应，尝试直接解析页面数据");
            // 如果没有收到API响应，等待页面更新后直接解析
            sleep(3000);
            return null; // 返回null，让调用方处理
        }

        logger.info("翻页点击操作完成，收到响应: {} [{}]", response.status(), response.url());
        return response;
    }

    /**
     * 等待页面加载完成 - 增加空指针检查
     */
    private void waitForPageLoadComplete() throws Exception {
        logger.debug("等待页面加载完成...");

        // 首先检查page对象是否为null
        if (page == null) {
            logger.error("页面对象为null，无法等待页面加载完成");
            throw new RuntimeException("页面对象为null，无法等待页面加载完成");
        }

        try {
            // 等待网络空闲
            page.waitForLoadState(LoadState.NETWORKIDLE, new Page.WaitForLoadStateOptions().setTimeout(10000));
        } catch (Exception e) {
            logger.debug("网络空闲等待超时: {}", e.getMessage());
        }

        // 适度等待内容渲染
        sleep(2000);

        // 等待可能的动画完成
        try {
            page.waitForFunction("() => document.readyState === 'complete'", new Page.WaitForFunctionOptions().setTimeout(5000));
        } catch (Exception e) {
            logger.debug("等待文档完成状态超时: {}", e.getMessage());
        }

        logger.debug("页面加载等待完成");
    }

    /**
     * 解析页面响应 - 支持无响应的情况
     */
    private List<Map<String, String>> parsePageResponse(Response response) throws Exception {
        logger.debug("解析翻页响应数据...");

        // 如果没有API响应，直接从页面解析数据
        if (response == null) {
            logger.info("没有API响应，直接从页面解析数据");
            return parsePageDataDirectly();
        }

        String responseText = response.text();
        JSONObject jsonResponse = safeParseJsonResponse(responseText, "翻页API");
        setLastResponseText(responseText);

        if (jsonResponse == null) {
            logger.warn("翻页数据JSON解析失败，尝试直接从页面解析");
            return parsePageDataDirectly();
        }

        List<Map<String, String>> results = parseApiResponse(jsonResponse, "翻页操作", responseText);
        logger.info("成功解析翻页结果: {} 条记录", results.size());

        // 获取响应包的时间，并更新当前任务执行时间
        try {
            if (jsonResponse.has("time")) {
                String responseTime = jsonResponse.getString("time");
                // 解析响应时间
                DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;
                LocalDateTime responseDateTime = LocalDateTime.parse(responseTime, formatter);

                // 将UTC时间转换为北京时间（UTC+8）
                ZoneId utcZone = ZoneId.of("UTC");
                ZoneId bjZone = ZoneId.of("Asia/Shanghai");
                ZonedDateTime utcZonedDateTime = responseDateTime.atZone(utcZone);
                ZonedDateTime bjZonedDateTime = utcZonedDateTime.withZoneSameInstant(bjZone);
                LocalDateTime bjDateTime = bjZonedDateTime.toLocalDateTime();

                // 只有当任务开始时间为null时才设置，避免重置已有的开始时间
                if (com.z3rd0.workbench.controller.TaskTestController.getTaskStartTime() == null) {
                    com.z3rd0.workbench.controller.TaskTestController.setTaskStartTime(bjDateTime);
                    logger.info("翻页操作：设置初始任务执行时间: {} (UTC) -> {} (北京时间)", responseTime, bjDateTime);
                } else {
                    logger.debug("翻页操作：任务开始时间已存在，不重复设置");
                }
            }
        } catch (Exception e) {
            logger.warn("解析响应时间失败: {}", e.getMessage());
        }

        return results;
    }

    /**
     * 直接从页面解析数据 - 当API响应不可用时使用，增加空指针检查
     */
    private List<Map<String, String>> parsePageDataDirectly() throws Exception {
        logger.info("直接从页面解析数据...");

        // 首先检查page对象是否为null
        if (page == null) {
            logger.error("页面对象为null，无法直接解析页面数据");
            return Collections.emptyList();
        }

        try {
            // 等待页面数据加载完成
            sleep(2000);

            // 尝试解析页面中的数据表格
            List<Map<String, String>> results = new ArrayList<>();

            // 查找数据表格的选择器
            String[] tableSelectors = {
                ".el-table__body tbody tr",
                ".data-table tbody tr",
                "table tbody tr",
                ".search-results .result-item"
            };

            for (String selector : tableSelectors) {
                try {
                    Locator rows = page.locator(selector);
                    int count = rows.count();

                    if (count > 0) {
                        logger.info("找到数据行，选择器: {}, 数量: {}", selector, count);

                        for (int i = 0; i < count; i++) {
                            try {
                                Locator row = rows.nth(i);
                                Map<String, String> rowData = extractRowData(row);
                                if (!rowData.isEmpty()) {
                                    results.add(rowData);
                                }
                            } catch (Exception e) {
                                logger.debug("解析第{}行数据失败: {}", i, e.getMessage());
                            }
                        }

                        if (!results.isEmpty()) {
                            logger.info("直接从页面解析到 {} 条数据", results.size());
                            return results;
                        }
                    }
                } catch (Exception e) {
                    logger.debug("选择器 {} 解析失败: {}", selector, e.getMessage());
                }
            }

            logger.warn("直接从页面解析数据失败，返回空结果");
            return Collections.emptyList();

        } catch (Exception e) {
            logger.error("直接解析页面数据异常: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 从表格行中提取数据
     */
    private Map<String, String> extractRowData(Locator row) {
        Map<String, String> data = new HashMap<>();

        try {
            // 尝试获取行中的文本内容
            String rowText = row.textContent();
            if (rowText != null && !rowText.trim().isEmpty()) {
                // 简单的数据提取，实际项目中可能需要更复杂的解析逻辑
                data.put("content", rowText.trim());
                data.put("extracted_from", "page_direct");
            }
        } catch (Exception e) {
            logger.debug("提取行数据失败: {}", e.getMessage());
        }

        return data;
    }

    /**
     * 检查元素是否在视口内
     */
    private boolean isElementInViewport(Locator element) {
        try {
            Boolean result = (Boolean) element.evaluate("""
                (element) => {
                    const rect = element.getBoundingClientRect();
                    return (
                        rect.top >= 0 &&
                        rect.left >= 0 &&
                        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
                    );
                }
                """);
            return result != null && result;
        } catch (Exception e) {
            logger.debug("检查元素视口状态失败: {}", e.getMessage());
            return false;
        }
    }



    // 确保完全滚动到页面最底部 - 增加空指针检查
    private void scrollToBottomWithVerification() throws InterruptedException {
        logger.info("滚动到页面底部...");

        // 首先检查page对象是否为null
        if (page == null) {
            logger.error("页面对象为null，无法滚动到底部");
            throw new RuntimeException("页面对象为null，无法滚动到底部");
        }

        try {
            Object lastHeight = 0;
            Object currentHeight = page.evaluate("document.body.scrollHeight");
            int attempts = 0;
            final int MAX_ATTEMPTS = 10;

            while (!lastHeight.equals(currentHeight) && attempts < MAX_ATTEMPTS) {
                lastHeight = currentHeight;

                // 模拟人类滚动行为（更自然的分段滚动）
                int scrollSteps = 2 + (int)(Math.random() * 3); // 随机2-4步
                for (int i = 0; i < scrollSteps; i++) {
                    // 获取当前视口高度并计算随机滚动距离
                    Object viewportHeight = page.evaluate("window.innerHeight");
                    int baseScrollDistance = ((Number) viewportHeight).intValue();
                    int scrollDistance = (int)(baseScrollDistance * (0.2 + Math.random() * 0.4)); // 20%-60%的视口高度

                    page.evaluate("(distance) => { window.scrollBy(0, distance); }", scrollDistance);

                    // 随机等待时间，模拟用户阅读和思考
                    int waitTime = 300 + (int)(Math.random() * 500); // 300-800ms随机间隔
                    sleep(waitTime);

                    logger.debug("模拟滚动步骤 {}/{}, 滚动距离: {}px, 等待时间: {}ms",
                               i + 1, scrollSteps, scrollDistance, waitTime);
                }

                // 检查是否真正到达底部
                boolean isAtBottom = (boolean) page.evaluate("() => {"
                        + "  const scrollPosition = window.scrollY + window.innerHeight;"
                        + "  const pageHeight = document.body.scrollHeight;"
                        + "  return scrollPosition >= pageHeight - 10;" // 允许10像素误差
                        + "}");

                if (isAtBottom) {
                    logger.info("已到达页面底部");
                    break;
                }

                currentHeight = page.evaluate("document.body.scrollHeight");
                attempts++;
                sleep(500); // 每次检查间隔
            }
        } catch (Exception e) {
            logger.warn("滚动到页面底部失败: {}", e.getMessage());
            // 不抛出异常，允许继续执行
        }
    }

    /**
     * 模拟自然的页面滚动行为 - 用于一般滚动需求
     * @param targetScrollPosition 目标滚动位置，-1表示滚动到底部
     */
    private void simulateNaturalScroll(int targetScrollPosition) throws InterruptedException {
        if (page == null) {
            logger.warn("页面对象为null，无法执行滚动");
            return;
        }

        try {
            // 获取当前滚动位置
            int currentPosition = ((Number) page.evaluate("window.scrollY")).intValue();

            // 如果目标位置是-1，表示滚动到底部
            if (targetScrollPosition == -1) {
                targetScrollPosition = ((Number) page.evaluate("document.body.scrollHeight")).intValue();
            }

            // 计算需要滚动的距离
            int totalDistance = targetScrollPosition - currentPosition;
            if (Math.abs(totalDistance) < 50) {
                logger.debug("滚动距离太小，跳过滚动");
                return;
            }

            // 分段滚动，模拟真实用户行为
            int steps = Math.min(Math.max(Math.abs(totalDistance) / 200, 2), 8); // 2-8步
            int stepDistance = totalDistance / steps;

            logger.debug("开始模拟自然滚动: 当前位置={}, 目标位置={}, 总距离={}, 分{}步完成",
                        currentPosition, targetScrollPosition, totalDistance, steps);

            for (int i = 0; i < steps; i++) {
                // 添加一些随机性，使滚动更自然
                int randomVariation = (int)(stepDistance * 0.1 * (Math.random() - 0.5)); // ±5%的随机变化
                int actualStepDistance = stepDistance + randomVariation;

                // 最后一步确保到达目标位置
                if (i == steps - 1) {
                    int currentPos = ((Number) page.evaluate("window.scrollY")).intValue();
                    actualStepDistance = targetScrollPosition - currentPos;
                }

                page.evaluate("(distance) => { window.scrollBy(0, distance); }", actualStepDistance);

                // 随机等待时间，模拟用户阅读
                int waitTime = 200 + (int)(Math.random() * 400); // 200-600ms
                sleep(waitTime);

                logger.debug("滚动步骤 {}/{}, 距离: {}px, 等待: {}ms", i + 1, steps, actualStepDistance, waitTime);
            }

            logger.debug("自然滚动完成");

        } catch (Exception e) {
            logger.warn("模拟自然滚动失败: {}", e.getMessage());
        }
    }

    // 模拟人类鼠标移动轨迹（贝塞尔曲线）
    private void simulateHumanMouseMovement(Page page, int targetX, int targetY)
            throws InterruptedException {
        int startX = (int)(Math.random() * 100) + 50;
        int startY = (int)(Math.random() * 100) + 50;

        // 移动过程中添加3个控制点
        int control1X = startX + (targetX - startX) / 3;
        int control1Y = startY + (int)(Math.random() * 100) - 50;

        int control2X = startX + 2 * (targetX - startX) / 3;
        int control2Y = targetY + (int)(Math.random() * 100) - 50;

        // 分多步移动
        int steps = 15 + (int)(Math.random() * 10); // 增加步数使移动更平滑
        for (int i = 0; i <= steps; i++) {
            double t = (double)i / steps;
            // 三次贝塞尔曲线计算
            int x = (int)(Math.pow(1-t, 3) * startX +
                    3 * Math.pow(1-t, 2) * t * control1X +
                    3 * (1-t) * Math.pow(t, 2) * control2X +
                    Math.pow(t, 3) * targetX);
            int y = (int)(Math.pow(1-t, 3) * startY +
                    3 * Math.pow(1-t, 2) * t * control1Y +
                    3 * (1-t) * Math.pow(t, 2) * control2Y +
                    Math.pow(t, 3) * targetY);

            page.mouse().move(x, y);
            sleep(30 + (int)(Math.random() * 30)); // 增加延迟使移动更自然
        }
    }

    public List<Map<String, String>> parseApiResponse(JSONObject jsonResponse) {
        return parseApiResponse(jsonResponse, "未知操作", null);
    }

    /**
     * 增强的API响应解析方法，包含操作类型和原始响应文本
     * @param jsonResponse 解析后的JSON对象
     * @param operationType 操作类型（搜索/翻页/文件解析等）
     * @param originalResponseText 原始响应文本（用于错误日志）
     * @return 解析结果列表
     */
    public List<Map<String, String>> parseApiResponse(JSONObject jsonResponse, String operationType, String originalResponseText) {
        List<Map<String, String>> results = new ArrayList<>();

        // 更新JSON解析统计
        totalJsonParseAttempts++;

        if (jsonResponse == null) {
            jsonParseFailures++;
            jsonParseFailuresByType.merge(operationType + "_null_response", 1, Integer::sum);
            logJsonParseError("JSON响应为null", operationType, originalResponseText, null, null);
            return results;
        }

        try {
            // 检查是否包含数据字段
            if (!jsonResponse.has("data")) {
                jsonParseFailures++;
                jsonParseFailuresByType.merge(operationType + "_missing_data_field", 1, Integer::sum);
                logJsonParseError("API响应中缺少data字段", operationType, originalResponseText, "data字段检查", null);
                return results;
            }

            Object dataObj = jsonResponse.get("data");
            if (!(dataObj instanceof JSONArray)) {
                jsonParseFailures++;
                jsonParseFailuresByType.merge(operationType + "_data_not_array", 1, Integer::sum);
                String errorMsg = String.format("data字段不是数组类型，实际类型: %s, 内容: %s",
                    dataObj.getClass().getSimpleName(), dataObj);
                logJsonParseError(errorMsg, operationType, originalResponseText, "data字段类型检查", null);
                return results;
            }

            JSONArray dataArray = (JSONArray) dataObj;
            logger.info("获取到数据记录数量: {}", dataArray.length());
            
            // 遍历每条数据记录
            for (int i = 0; i < dataArray.length(); i++) {
                try {
                    JSONObject item = dataArray.getJSONObject(i);
                    Map<String, String> resultMap = new HashMap<>();
                
                // 提取基本字段
                safeExtractString(item, "ip", resultMap, "ip");
                safeExtractString(item, "domain", resultMap, "domain");
                safeExtractString(item, "hostname", resultMap, "hostname");
                safeExtractString(item, "transport", resultMap, "transport");
                safeExtractString(item, "org", resultMap, "org");

                // 处理端口，可能是整数
                safeExtractInteger(item, "port", resultMap, "port");
                
                // 生成 originalId，格式为：domain_port_tcp 或 ip_port_tcp
                // 安全获取字段值，处理null情况
                String domain = Optional.ofNullable(resultMap.get("domain")).orElse("");
                String ip = Optional.ofNullable(resultMap.get("ip")).orElse("");
                String port = Optional.ofNullable(resultMap.get("port")).orElse("");

                String originalId;
                if (!domain.isEmpty()) {
                    originalId = domain + "_" + port + "_tcp";
                } else if (!ip.isEmpty()) {
                    originalId = ip + "_" + port + "_tcp";
                } else {
                    // 备用逻辑：如果都没有，才使用原始id（通常不应该发生）
                    safeExtractString(item, "id", resultMap, "originalId");
                    originalId = Optional.ofNullable(resultMap.get("originalId")).orElse("unknown_" + System.currentTimeMillis());
                }
                
                resultMap.put("originalId", originalId);
                logger.debug("生成originalId: {} (domain={}, ip={}, port={})", originalId, domain, ip, port);
                
                // 时间转换为北京时间
                if (item.has("time") && !item.isNull("time")) {
                    String utcTimeStr = item.getString("time");
                    try {
                        // 解析响应时间
                        DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;
                        // 将UTC时间解析为LocalDateTime
                        LocalDateTime responseDateTime = LocalDateTime.parse(utcTimeStr, formatter);
                        
                        // 将UTC时间转换为北京时间（UTC+8）
                        ZoneId utcZone = ZoneId.of("UTC");
                        ZoneId bjZone = ZoneId.of("Asia/Shanghai");
                        ZonedDateTime utcZonedDateTime = responseDateTime.atZone(utcZone);
                        ZonedDateTime bjZonedDateTime = utcZonedDateTime.withZoneSameInstant(bjZone);
                        LocalDateTime bjDateTime = bjZonedDateTime.toLocalDateTime();
                        
                        // 将北京时间格式化为字符串，不包含毫秒部分
                        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        String bjTimeStr = bjDateTime.format(outputFormatter);
                        
                        // 存储北京时间到结果中
                        resultMap.put("time", bjTimeStr);
                        logger.debug("时间转换: {} (UTC) -> {} (北京时间)", utcTimeStr, bjTimeStr);
                    } catch (Exception e) {
                        logger.warn("时间格式转换失败，使用原始时间: {}, 错误: {}", utcTimeStr, e.getMessage());
                        resultMap.put("time", utcTimeStr);
                    }
                }
                
                // 处理位置信息
                JSONObject location = safeGetJSONObject(item, "location");
                if (location != null) {
                    safeExtractString(location, "isp", resultMap, "locationIsp");
                    safeExtractString(location, "scene_cn", resultMap, "locationScene");
                    safeExtractString(location, "country_cn", resultMap, "locationCountry");
                    safeExtractString(location, "province_cn", resultMap, "locationProvince");
                    safeExtractString(location, "city_cn", resultMap, "locationCity");
                } else {
                    // 位置信息不存在，设置为null
                    resultMap.put("locationIsp", null);
                    resultMap.put("locationScene", null);
                    resultMap.put("locationCountry", null);
                    resultMap.put("locationProvince", null);
                    resultMap.put("locationCity", null);
                }

                // 处理网络扩展信息
                safeExtractInteger(item, "asn", resultMap, "asn");

                try {
                    if (item.has("is_ipv6") && !item.isNull("is_ipv6")) {
                        resultMap.put("isIpv6", String.valueOf(item.getBoolean("is_ipv6")));
                    } else {
                        resultMap.put("isIpv6", null);
                    }
                } catch (Exception e) {
                    logger.warn("处理is_ipv6字段时出错: {}, 设置为null", e.getMessage());
                    resultMap.put("isIpv6", null);
                }

                try {
                    if (item.has("sys_tag") && !item.isNull("sys_tag")) {
                        Object sysTagObj = item.get("sys_tag");
                        if (sysTagObj instanceof JSONArray) {
                            JSONArray sysTagArray = (JSONArray) sysTagObj;
                            StringBuilder sysTagBuilder = new StringBuilder();
                            for (int j = 0; j < sysTagArray.length(); j++) {
                                if (j > 0) sysTagBuilder.append(", ");
                                sysTagBuilder.append(sysTagArray.getString(j));
                            }
                            resultMap.put("sysTag", sysTagBuilder.toString());
                        } else {
                            resultMap.put("sysTag", String.valueOf(sysTagObj));
                        }
                    } else {
                        resultMap.put("sysTag", null);
                    }
                } catch (Exception e) {
                    logger.warn("处理sys_tag字段时出错: {}, 设置为null", e.getMessage());
                    resultMap.put("sysTag", null);
                }
                
                // 处理服务信息
                JSONObject service = safeGetJSONObject(item, "service");
                if (service != null) {
                    // 提取服务名称
                    safeExtractString(service, "name", resultMap, "serviceName");

                    // 处理HTTP信息
                    JSONObject http = safeGetJSONObject(service, "http");
                    if (http != null) {
                        safeExtractString(http, "title", resultMap, "title");
                        safeExtractString(http, "path", resultMap, "path");
                        safeExtractString(http, "host", resultMap, "host");
                        safeExtractString(http, "server", resultMap, "server");
                        safeExtractString(http, "x_powered_by", resultMap, "xPoweredBy");
                        safeExtractString(http, "meta_keywords", resultMap, "metaKeywords");

                        // 处理状态码
                        safeExtractInteger(http, "status_code", resultMap, "statusCode");

                        // 处理ICP备案信息
                        JSONObject icp = safeGetJSONObject(http, "icp");
                        if (icp != null) {
                            safeExtractString(icp, "licence", resultMap, "icpLicence");
                            JSONObject mainLicence = safeGetJSONObject(icp, "main_licence");
                            if (mainLicence != null) {
                                safeExtractString(mainLicence, "unit", resultMap, "icpUnit");
                                safeExtractString(mainLicence, "nature", resultMap, "icpNature");
                            } else {
                                resultMap.put("icpUnit", null);
                                resultMap.put("icpNature", null);
                            }
                        } else {
                            resultMap.put("icpLicence", null);
                            resultMap.put("icpUnit", null);
                            resultMap.put("icpNature", null);
                        }

                        // 处理favicon信息
                        JSONObject favicon = safeGetJSONObject(http, "favicon");
                        if (favicon != null) {
                            safeExtractString(favicon, "hash", resultMap, "faviconHash");
                            safeExtractString(favicon, "location", resultMap, "faviconUrl");
                        } else {
                            resultMap.put("faviconHash", null);
                            resultMap.put("faviconUrl", null);
                        }

                        // 如果没有单独的httpLoadUrl字段，可以从其他字段构建URL
                        String httpHost = resultMap.get("host");
                        String httpPath = resultMap.get("path");
                        String httpPort = resultMap.get("port");
                        if (httpHost != null && !httpHost.isEmpty()) {
                            String url = "http://" + httpHost;
                            if (httpPort != null && !httpPort.isEmpty() && !"80".equals(httpPort) && !"443".equals(httpPort)) {
                                url += ":" + httpPort;
                            }
                            if (httpPath != null && !httpPath.isEmpty()) {
                                url += httpPath;
                            }
                            resultMap.put("url", url);
                        }
                    } else {
                        // 没有HTTP信息的情况，设置相关字段为null
                        logger.debug("服务中没有HTTP信息");
                        setHttpFieldsToNull(resultMap);
                    }
                } else {
                    // 没有服务信息的情况，设置相关字段为null
                    logger.debug("没有服务信息");
                    resultMap.put("serviceName", null);
                    setHttpFieldsToNull(resultMap);
                }

                // 处理技术栈信息 (components)
                try {
                    if (item.has("components") && !item.isNull("components")) {
                        Object componentsObj = item.get("components");
                        if (componentsObj instanceof JSONArray) {
                            JSONArray components = (JSONArray) componentsObj;
                            if (components.length() > 0) {
                                JSONArray techStackArray = new JSONArray();
                                for (int j = 0; j < components.length(); j++) {
                                    try {
                                        JSONObject component = components.getJSONObject(j);
                                        JSONObject techItem = new JSONObject();

                                        // 安全提取技术栈字段
                                        if (component.has("product_name_cn") && !component.isNull("product_name_cn")) {
                                            techItem.put("name", component.getString("product_name_cn"));
                                        }
                                        if (component.has("product_vendor") && !component.isNull("product_vendor")) {
                                            techItem.put("vendor", component.getString("product_vendor"));
                                        }
                                        if (component.has("version") && !component.isNull("version")) {
                                            techItem.put("version", component.getString("version"));
                                        }
                                        if (component.has("product_catalog") && !component.isNull("product_catalog")) {
                                            Object catalogObj = component.get("product_catalog");
                                            if (catalogObj instanceof JSONArray) {
                                                techItem.put("catalog", (JSONArray) catalogObj);
                                            }
                                        }
                                        techStackArray.put(techItem);
                                    } catch (Exception e) {
                                        logger.warn("处理技术栈组件 {} 时出错: {}", j, e.getMessage());
                                    }
                                }
                                resultMap.put("techStack", techStackArray.toString());
                            } else {
                                resultMap.put("techStack", null);
                            }
                        } else {
                            logger.debug("components字段不是数组类型: {}", componentsObj.getClass().getSimpleName());
                            resultMap.put("techStack", null);
                        }
                    } else {
                        resultMap.put("techStack", null);
                    }
                } catch (Exception e) {
                    logger.warn("处理技术栈信息时出错: {}, 设置为null", e.getMessage());
                    resultMap.put("techStack", null);
                }

                    results.add(resultMap);
                    logger.debug("处理数据项 {}/{}: {}", i+1, dataArray.length(), resultMap);

                } catch (Exception e) {
                    String errorMsg = String.format("解析第%d条数据记录失败: %s", i+1, e.getMessage());
                    String itemJson = null;
                    try {
                        itemJson = dataArray.getJSONObject(i).toString();
                    } catch (Exception ex) {
                        itemJson = "无法获取数据项JSON内容";
                    }

                    logJsonParseError(errorMsg, operationType, itemJson,
                        String.format("数据项[%d/%d]", i+1, dataArray.length()), e);

                    // 继续处理下一条记录，不中断整个解析过程
                    logger.warn("跳过第{}条数据记录，继续处理后续数据", i+1);
                }
            }
            
            logger.info("成功解析 {} 条数据记录", results.size());
            return results;
        } catch (Exception e) {
            jsonParseFailures++;
            jsonParseFailuresByType.merge(operationType + "_parse_exception", 1, Integer::sum);
            String errorMsg = String.format("解析API响应失败: %s - 已解析记录数: %d", e.getMessage(), results.size());
            logJsonParseError(errorMsg, operationType, originalResponseText, "数据解析主循环", e);
            return results;
        }
    }
    
    /**
     * 安全地从JSON对象中提取字符串字段
     * @param json JSON对象
     * @param jsonKey 源字段名
     * @param resultMap 目标Map
     * @param mapKey 目标字段名
     */
    private void safeExtractString(JSONObject json, String jsonKey, Map<String, String> resultMap, String mapKey) {
        try {
            if (json != null && json.has(jsonKey) && !json.isNull(jsonKey)) {
                Object value = json.get(jsonKey);
                if (value instanceof String) {
                    String strValue = (String) value;
                    // 检查是否为权限不足的提示信息
                    if (!"暂无权限".equals(strValue) && !"无权限".equals(strValue) && !"权限不足".equals(strValue)) {
                        resultMap.put(mapKey, strValue);
                    } else {
                        logger.debug("字段 {} 权限不足，设置为null", mapKey);
                        resultMap.put(mapKey, null);
                    }
                } else {
                    // 非字符串类型，转换为字符串
                    resultMap.put(mapKey, String.valueOf(value));
                }
            } else {
                // 字段不存在或为null，设置为null
                resultMap.put(mapKey, null);
            }
        } catch (Exception e) {
            logger.warn("提取字段 {} 时出错: {}, 设置为null", mapKey, e.getMessage());
            resultMap.put(mapKey, null);
        }
    }

    /**
     * 安全地从JSON对象中提取JSON对象字段
     * @param json JSON对象
     * @param jsonKey 源字段名
     * @return JSON对象或null
     */
    private JSONObject safeGetJSONObject(JSONObject json, String jsonKey) {
        try {
            if (json != null && json.has(jsonKey) && !json.isNull(jsonKey)) {
                Object value = json.get(jsonKey);
                if (value instanceof JSONObject) {
                    return (JSONObject) value;
                } else {
                    logger.debug("字段 {} 不是JSON对象类型: {}", jsonKey, value.getClass().getSimpleName());
                    return null;
                }
            }
        } catch (Exception e) {
            logger.warn("获取JSON对象字段 {} 时出错: {}", jsonKey, e.getMessage());
        }
        return null;
    }

    /**
     * 安全地从JSON对象中提取整数字段
     * @param json JSON对象
     * @param jsonKey 源字段名
     * @param resultMap 目标Map
     * @param mapKey 目标字段名
     */
    private void safeExtractInteger(JSONObject json, String jsonKey, Map<String, String> resultMap, String mapKey) {
        try {
            if (json != null && json.has(jsonKey) && !json.isNull(jsonKey)) {
                Object value = json.get(jsonKey);
                resultMap.put(mapKey, String.valueOf(value));
            } else {
                resultMap.put(mapKey, null);
            }
        } catch (Exception e) {
            logger.warn("提取整数字段 {} 时出错: {}, 设置为null", mapKey, e.getMessage());
            resultMap.put(mapKey, null);
        }
    }

    /**
     * 设置HTTP相关字段为null
     * @param resultMap 目标Map
     */
    private void setHttpFieldsToNull(Map<String, String> resultMap) {
        resultMap.put("title", null);
        resultMap.put("path", null);
        resultMap.put("host", null);
        resultMap.put("server", null);
        resultMap.put("xPoweredBy", null);
        resultMap.put("statusCode", null);
        resultMap.put("metaKeywords", null);
        resultMap.put("icpLicence", null);
        resultMap.put("icpUnit", null);
        resultMap.put("icpNature", null);
        resultMap.put("faviconHash", null);
        resultMap.put("faviconUrl", null);
    }
    
    // 判断搜索结果时间是否在指定范围内
    public boolean isTimeInRange(String resultTime, String timeRange) {
        logger.debug("检查时间范围: 结果时间={}, 指定范围={}", resultTime, timeRange);
        
        if (resultTime == null || timeRange == null || timeRange.isEmpty()) {
            logger.debug("时间或范围为空，默认返回true");
            return true;  // 如果没有时间范围限制，则默认符合条件
        }
        
        try {
            // 解析搜索结果的时间 - 支持多种格式
            LocalDateTime resultDateTime = null;
            
            try {
                logger.debug("尝试解析结果时间: {}", resultTime);
                if (resultTime.contains("T") && resultTime.contains("Z")) {
                    // 处理ISO格式 (如 "2025-04-11T22:58:36.292Z")
                    try {
                        // 先尝试直接用ISO标准格式解析
                        resultDateTime = LocalDateTime.ofInstant(
                                Instant.parse(resultTime), 
                                ZoneId.systemDefault());
                        logger.debug("使用ISO格式(带Z)解析成功: {}", resultDateTime);
                    } catch (Exception e) {
                        // 如果直接解析失败，则做字符串处理后再解析
                        String normalizedTime = resultTime.replace("Z", "");
                        if (normalizedTime.contains(".")) {
                            // 处理毫秒
                            normalizedTime = normalizedTime.substring(0, normalizedTime.lastIndexOf("."));
                        }
                        resultDateTime = LocalDateTime.parse(normalizedTime, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                        logger.debug("使用处理后的ISO格式解析成功: {}", resultDateTime);
                    }
                } else if (resultTime.contains("-") && resultTime.contains(":")) {
                    // 处理标准格式 (如 "2023-01-01 00:00:00")
                    try {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        resultDateTime = LocalDateTime.parse(resultTime, formatter);
                        logger.debug("使用标准日期时间格式解析成功: {}", resultDateTime);
                    } catch (DateTimeParseException e) {
                        // 尝试其他常见格式
                        try {
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
                            resultDateTime = LocalDateTime.parse(resultTime, formatter);
                            logger.debug("使用带T的格式解析成功: {}", resultDateTime);
                        } catch (DateTimeParseException e2) {
                            // 如果只有日期没有时间
                            try {
                                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                                LocalDate date = LocalDate.parse(resultTime, formatter);
                                resultDateTime = date.atStartOfDay();
                                logger.debug("只有日期部分，解析成功: {}", resultDateTime);
                            } catch (DateTimeParseException e3) {
                                logger.warn("所有日期格式解析失败: {}", resultTime);
                                return true; // 解析失败默认通过
                            }
                        }
                    }
                } else {
                    // 无法识别的格式，默认符合条件
                    logger.warn("无法识别的时间格式: {}", resultTime);
                    return true;
                }
            } catch (Exception e) {
                logger.warn("时间格式解析失败: {}, 错误: {}", resultTime, e.getMessage());
                return true;  // 无法解析时间格式，默认符合条件
            }
            
            if (resultDateTime == null) {
                logger.warn("结果时间解析为null，默认返回true");
                return true;
            }
            
            // 解析时间范围 (格式: "2023-01-01 00:00:00,2023-12-31 23:59:59")
            try {
                String[] rangeParts = timeRange.split(",");
                logger.debug("检查时间范围格式，部分数量: {}", rangeParts.length);
                
                if (rangeParts.length == 2) {
                    DateTimeFormatter rangeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    logger.debug("解析时间范围: 开始={}, 结束={}", rangeParts[0].trim(), rangeParts[1].trim());
                    
                    LocalDateTime startTime = null;
                    try {
                        startTime = LocalDateTime.parse(rangeParts[0].trim(), rangeFormatter);
                        logger.debug("解析开始时间成功: {}", startTime);
                    } catch (DateTimeParseException e) {
                        logger.debug("使用标准格式解析开始时间失败: {}", e.getMessage());
                        // 尝试只有日期的格式
                        try {
                            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                            LocalDate startDate = LocalDate.parse(rangeParts[0].trim(), dateFormatter);
                            startTime = startDate.atStartOfDay();
                            logger.debug("使用日期格式解析开始时间成功: {}", startTime);
                        } catch (Exception ex) {
                            logger.warn("开始时间解析失败，默认不限制开始时间");
                            startTime = LocalDateTime.of(1970, 1, 1, 0, 0); // 设置一个很早的时间作为默认值
                        }
                    }
                    
                    LocalDateTime endTime = null;
                    try {
                        endTime = LocalDateTime.parse(rangeParts[1].trim(), rangeFormatter);
                        logger.debug("解析结束时间成功: {}", endTime);
                    } catch (DateTimeParseException e) {
                        logger.debug("使用标准格式解析结束时间失败: {}", e.getMessage());
                        // 尝试只有日期的格式
                        try {
                            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                            LocalDate endDate = LocalDate.parse(rangeParts[1].trim(), dateFormatter);
                            endTime = endDate.atTime(23, 59, 59);
                            logger.debug("使用日期格式解析结束时间成功: {}", endTime);
                        } catch (Exception ex) {
                            logger.warn("结束时间解析失败，默认不限制结束时间");
                            endTime = LocalDateTime.now().plusYears(100); // 设置一个很晚的时间作为默认值
                        }
                    }
                    
                    // 确保有开始和结束时间
                    if (startTime == null || endTime == null) {
                        logger.warn("开始时间或结束时间解析为null，默认返回true");
                        return true;
                    }
                    
                    // 检查结果时间是否在范围内
                    boolean afterOrEqualStart = !resultDateTime.isBefore(startTime);
                    boolean beforeOrEqualEnd = !resultDateTime.isAfter(endTime);
                    boolean inRange = afterOrEqualStart && beforeOrEqualEnd;
                    
                    logger.info("时间范围检查详情 - 结果时间: {}, 开始时间: {}, 结束时间: {}, 是否在范围内: {}, 大于等于开始时间: {}, 小于等于结束时间: {}", 
                              resultDateTime, startTime, endTime, inRange, afterOrEqualStart, beforeOrEqualEnd);
                    
                    return inRange;
                } else {
                    logger.warn("时间范围格式不正确: {}, 期望格式为'startTime,endTime'", timeRange);
                    return true; // 格式不正确默认通过
                }
            } catch (Exception e) {
                logger.warn("时间范围解析失败: {}, 错误: {}", timeRange, e.getMessage());
                return true; // 解析失败默认通过
            }
        } catch (Exception e) {
            logger.error("时间范围检查异常: {}", e.getMessage());
            return true;  // 解析错误时默认通过
        }
    }
    
    // 辅助方法：线程休眠
    private void sleep(int millis) throws InterruptedException {
        if (millis > 0) {
            Thread.sleep(millis);
        }
    }
    
    // 关闭浏览器资源
    public void close() {
        logger.info("清理浏览器资源...");
        boolean hasException = false;
        
        try {
            if (page != null) {
                try {
                    page.close();
                    logger.info("页面已关闭");
                } catch (Exception e) {
                    logger.error("关闭页面出错: {}", e.getMessage(), e);
                    hasException = true;
                }
                page = null;
            }
            
            if (context != null) {
                try {
                    context.close();
                    logger.info("浏览器上下文已关闭");
                } catch (Exception e) {
                    logger.error("关闭浏览器上下文出错: {}", e.getMessage(), e);
                    hasException = true;
                }
                context = null;
            }
            
            if (browser != null) {
                try {
                    browser.close();
                    logger.info("浏览器已关闭");
                } catch (Exception e) {
                    logger.error("关闭浏览器出错: {}", e.getMessage(), e);
                    hasException = true;
                }
                browser = null;
            }
            
            if (playwright != null) {
                try {
                    playwright.close();
                    logger.info("Playwright已关闭");
                } catch (Exception e) {
                    logger.error("关闭Playwright出错: {}", e.getMessage(), e);
                    hasException = true;
                }
                playwright = null;
            }
            
            if (!hasException) {
                logger.info("所有资源已清理完毕");
            } else {
                logger.warn("资源清理过程中出现异常，部分资源可能未完全释放");
            }
        } catch (Exception e) {
            logger.error("资源清理异常: {}", e.getMessage(), e);
            // 最后的兜底处理，确保所有引用置空
            page = null;
            context = null;
            browser = null;
            playwright = null;
        }
    }
    
    /**
     * 检查浏览器是否已初始化 - 优化版本，更宽松的检查
     */
    public boolean isInitialized() {
        // 优化检查逻辑：只要核心对象存在就认为已初始化
        // 页面对象可能在某些情况下需要重新创建，但不应该影响整体初始化状态
        boolean coreInitialized = playwright != null && browser != null && context != null;

        if (!coreInitialized) {
            logger.debug("核心浏览器对象未初始化: playwright={}, browser={}, context={}",
                        playwright != null, browser != null, context != null);
            return false;
        }

        // 页面对象检查更宽松
        if (page == null) {
            logger.debug("页面对象为null，但核心浏览器已初始化");
            return true; // 核心已初始化，页面对象可以重新创建
        }

        logger.debug("浏览器完全初始化检查通过");
        return true;
    }
    
    /**
     * 检查浏览器连接是否正常 - 增加空指针检查
     */
    public boolean isConnected() {
        try {
            if (!isInitialized()) {
                return false;
            }

            // 检查page对象是否为null
            if (page == null) {
                logger.debug("页面对象为null，浏览器连接无效");
                return false;
            }

            // 尝试执行一个简单操作来检查连接状态
            page.evaluate("1+1");
            return true;
        } catch (Exception e) {
            logger.warn("浏览器连接检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 尝试恢复页面对象 - 当page为null时使用
     */
    public boolean tryRecoverPageObject() {
        try {
            logger.info("尝试恢复页面对象...");

            // 检查核心浏览器对象是否存在
            if (playwright == null || browser == null || context == null) {
                logger.error("核心浏览器对象缺失，无法恢复页面对象");
                return false;
            }

            // 检查浏览器是否仍然连接
            if (browser.isConnected()) {
                // 尝试创建新的页面对象
                page = context.newPage();
                page.setDefaultTimeout(pageLoadTimeout);

                // 设置对话框处理
                page.onDialog(dialog -> {
                    logger.info("页面对话框: {}", dialog.message());
                    dialog.dismiss();
                });

                logger.info("页面对象恢复成功");
                return true;
            } else {
                logger.error("浏览器连接已断开，无法恢复页面对象");
                return false;
            }
        } catch (Exception e) {
            logger.error("恢复页面对象失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查页面对象是否有效 - 公共方法，优化版本
     */
    public boolean isPageValid() {
        try {
            if (page == null) {
                logger.debug("页面对象为null");
                return false;
            }

            // 简化检查，只验证页面对象本身
            // 避免过度检查导致不必要的页面重新初始化
            try {
                // 尝试获取页面URL，如果成功说明页面对象有效
                String url = page.url();
                if (url != null) {
                    logger.debug("页面对象有效，当前URL: {}", url);
                    return true;
                }
            } catch (Exception e) {
                logger.debug("页面对象无效: {}", e.getMessage());
                return false;
            }

            return false;
        } catch (Exception e) {
            logger.debug("页面有效性检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 重新初始化页面对象 - 当页面对象无效时使用
     */
    public boolean reinitializePage() {
        try {
            logger.info("尝试重新初始化页面对象...");

            if (context == null || browser == null) {
                logger.error("浏览器上下文或实例为null，无法重新初始化页面");
                return false;
            }

            // 关闭现有页面（如果存在）
            if (page != null) {
                try {
                    page.close();
                } catch (Exception e) {
                    logger.warn("关闭现有页面失败: {}", e.getMessage());
                }
            }

            // 创建新页面
            page = context.newPage();
            page.setDefaultTimeout(pageLoadTimeout);

            // 重新设置对话框处理
            page.onDialog(dialog -> {
                logger.info("页面对话框: {}", dialog.message());
                dialog.dismiss();
            });

            logger.info("页面对象重新初始化成功");
            return true;

        } catch (Exception e) {
            logger.error("重新初始化页面对象失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    // 设置方法
    public void setDefaultDelay(int defaultDelay) {
        this.defaultDelay = defaultDelay;
    }
    
    public void setTypeDelay(int typeDelay) {
        this.typeDelay = typeDelay;
    }
    
    public void setSlowMode(boolean slowMode) {
        this.slowMode = slowMode;
    }
    
    public void setPageLoadTimeout(int pageLoadTimeout) {
        this.pageLoadTimeout = pageLoadTimeout;
        if (page != null) {
            page.setDefaultTimeout(pageLoadTimeout);
        }
    }

    /**
     * 测试方法：从文件中读取API响应并解析
     * 用于调试和测试parseApiResponse方法的结果
     * @param filePath response.txt文件路径
     * @return 解析的结果列表
     */
    public List<Map<String, String>> testParseResponseFromFile(String filePath) {
        try {
            // 读取文件内容
            java.nio.file.Path path = java.nio.file.Paths.get(filePath);
            String content = new String(java.nio.file.Files.readAllBytes(path), java.nio.charset.StandardCharsets.UTF_8);
            
            // 提取JSON部分
            int jsonStart = content.indexOf('{');
            if (jsonStart == -1) {
                logger.error("文件中未找到JSON数据: {}", filePath);
                return Collections.emptyList();
            }
            
            String jsonContent = content.substring(jsonStart);
            JSONObject jsonResponse = safeParseJsonResponse(jsonContent, "文件JSON");
            if (jsonResponse == null) {
                logger.error("文件JSON解析失败: {}", filePath);
                return Collections.emptyList();
            }

            // 调用parseApiResponse方法解析数据
            List<Map<String, String>> results = parseApiResponse(jsonResponse, "文件解析", jsonContent);
            
            // 打印解析结果样例（用于调试）
            logger.info("从文件解析到 {} 条数据", results.size());
            if (!results.isEmpty()) {
                logger.info("第一条数据示例: {}", results.get(0));
            }
            
            return results;
        } catch (Exception e) {
            logger.error("从文件解析响应失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 处理确认对话框
     * @return 是否成功处理
     */
    public boolean handleConfirmDialog() {
        try {
            logger.info("开始处理确认对话框");
            sleep(1000); // 等待对话框出现
            
            // 尝试点击确认按钮
            Locator confirmButton = page.locator("div.el-message-box__btns button.el-button--primary:has-text('确定')");
            if (confirmButton.count() > 0) {
                logger.info("通过primary button选择器找到确认按钮，点击中...");
                confirmButton.click();
                sleep(1000);
                return true;
            }
            
            // 如果没找到primary按钮，尝试通过文本查找
            Locator textButton = page.locator("div.el-message-box__btns button:has-text('确定')");
            if (textButton.count() > 0) {
                logger.info("通过文本内容找到确认按钮，点击中...");
                textButton.click();
                sleep(1000);
                return true;
            }
            
            // 如果还是找不到，尝试直接通过CSS选择器
            Locator anyButton = page.locator("div.el-message-box__btns button");
            if (anyButton.count() > 0) {
                logger.info("找到对话框按钮，点击第一个按钮");
                anyButton.first().click();
                sleep(1000);
                return true;
            }
            
            // 最后尝试JavaScript点击
            logger.info("通过常规方式未找到确认按钮，尝试JavaScript方式");
            boolean jsClicked = (boolean) page.evaluate("() => {" +
                "  try {" +
                "    const confirmButton = document.querySelector('div.el-message-box__btns button.el-button--primary');" +
                "    if (confirmButton) {" +
                "      confirmButton.click();" +
                "      console.log('通过JS点击了确认按钮');" +
                "      return true;" +
                "    }" +
                "    " +
                "    // 尝试查找包含\"确定\"文本的按钮" +
                "    const buttons = document.querySelectorAll('div.el-message-box__btns button.el-button');" +
                "    for (const btn of buttons) {" +
                "      if (btn.textContent.trim().includes('确定')) {" +
                "        btn.click();" +
                "        console.log('通过JS文本匹配点击了确认按钮');" +
                "        return true;" +
                "      }" +
                "    }" +
                "    " +
                "    // 如果还是找不到，点击最后一个按钮" +
                "    if (buttons.length > 0) {" +
                "      buttons[buttons.length - 1].click();" +
                "      console.log('通过JS点击了最后一个按钮');" +
                "      return true;" +
                "    }" +
                "    " +
                "    return false;" +
                "  } catch (error) {" +
                "    console.error('JS点击确认按钮出错:', error);" +
                "    return false;" +
                "  }" +
                "}");
                
            if (jsClicked) {
                logger.info("通过JavaScript成功点击了确认按钮");
                sleep(3000);
                return true;
            }
            
            logger.warn("未找到确认按钮，可能对话框结构已变化");
            return false;
        } catch (Exception e) {
            logger.warn("处理确认对话框时出错: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 实际执行设备移除操作
     * 注意：这是一个直接执行的方法，会点击界面上的移除按钮并处理确认对话框
     * @param deviceIndex 设备在表格中的索引位置
     * @return 是否成功移除
     */
    public boolean removeDeviceByIndex(int deviceIndex) {
        try {
            logger.info("准备移除第 {} 行的设备", deviceIndex);
            
            // 定位设备表格
            Locator fixedRows = page.locator(".el-table__fixed-right .el-table__fixed-body-wrapper tr.el-table__row");
            int rowCount = fixedRows.count();
            
            if (deviceIndex >= rowCount || deviceIndex < 0) {
                logger.error("设备索引 {} 超出范围，总行数: {}", deviceIndex, rowCount);
                return false;
            }
            
            // 获取设备ID，便于日志记录
            String deviceId = (String) page.evaluate(
                "rowIndex => {" +
                "  const mainRows = document.querySelectorAll('.el-table__body-wrapper tbody tr');" +
                "  if (rowIndex < mainRows.length) {" +
                "    const idCell = mainRows[rowIndex].querySelector('.el-table_1_column_2 .cell');" +
                "    return idCell ? idCell.textContent.trim() : '未知ID';" +
                "  }" +
                "  return '未知ID';" +
                "}", deviceIndex
            );
            
            // 定位并点击移除按钮
            Locator removeButton = fixedRows.nth(deviceIndex)
                .locator("td.el-table_1_column_5 button[data-v-5522e23e].el-button--text");
            
            if (removeButton.count() > 0) {
                logger.info("找到第 {} 行设备 {} 的移除按钮，准备点击", deviceIndex, deviceId);
                
                // 点击按钮
                removeButton.click();
                
                // 处理确认对话框
                boolean confirmed = handleConfirmDialog();
                if (confirmed) {
                    logger.info("成功移除设备: {}", deviceId);
                    return true;
                } else {
                    logger.warn("确认对话框处理失败，设备 {} 可能未被移除", deviceId);
                    return false;
                }
            } else {
                logger.warn("未找到第 {} 行设备 {} 的移除按钮", deviceIndex, deviceId);
                return false;
            }
        } catch (Exception e) {
            logger.error("移除设备时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据设备ID移除设备
     * @param deviceId 设备ID
     * @return 是否成功移除
     */
    public boolean removeDeviceById(String deviceId) {
        try {
            logger.info("准备移除设备ID: {}", deviceId);
            
            // 定位设备表格
            Locator fixedRows = page.locator(".el-table__fixed-right .el-table__fixed-body-wrapper tr.el-table__row");
            int rowCount = fixedRows.count();
            logger.info("固定列中找到 {} 行数据", rowCount);
            
            // 遍历表格查找匹配的设备ID
            Integer targetRowIndex = null;
            for (int i = 0; i < rowCount; i++) {
                String currentId = (String) page.evaluate(
                    "rowIndex => {" +
                    "  const mainRows = document.querySelectorAll('.el-table__body-wrapper tbody tr');" +
                    "  if (rowIndex < mainRows.length) {" +
                    "    const idCell = mainRows[rowIndex].querySelector('.el-table_1_column_2 .cell');" +
                    "    return idCell ? idCell.textContent.trim() : '';" +
                    "  }" +
                    "  return '';" +
                    "}", i
                );
                
                if (deviceId.equals(currentId)) {
                    targetRowIndex = i;
                    logger.info("找到匹配的设备ID: {} 在第 {} 行", deviceId, i);
                    break;
                }
            }
            
            // 如果找到匹配的行，执行移除操作
            if (targetRowIndex != null) {
                return removeDeviceByIndex(targetRowIndex);
            } else {
                logger.warn("未找到设备ID为 {} 的行", deviceId);
                return false;
            }
        } catch (Exception e) {
            logger.error("根据设备ID移除设备时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 自动移除设备
     * 注意：此方法会导航到设备管理页面并根据账号等级决定移除策略
     * - 普通账号：设备数量>1时，移除登录时间最长的设备
     * - VIP账号：设备数量>4时，移除登录时间最长的设备
     * @param skipNavigateToHomepage 是否跳过导航回主页（默认为false）
     * @return 是否成功移除了设备
     */
    public boolean autoRemoveDevices(boolean skipNavigateToHomepage) {
        try {
            logger.info("开始自动移除设备流程...");
            
            // 判断账号等级，从配置中获取设备保留阈值
            DeviceThresholdInfo thresholdInfo = getDeviceThresholdInfo();
            int deviceThreshold = thresholdInfo.threshold;
            
            logger.info("账号等级: {}, 设备保留阈值: {}", thresholdInfo.levelName, deviceThreshold);
            
            // 访问设备管理页面
            logger.info("正在访问设备管理页面...");
            page.navigate("https://quake.360.net/quake/#/personal?tab=Tequipment");
            
            // 优化的设备管理页面加载等待
            logger.info("等待页面加载完成...");
            page.waitForLoadState(LoadState.DOMCONTENTLOADED, new Page.WaitForLoadStateOptions().setTimeout(30000));
            try {
                page.waitForLoadState(LoadState.NETWORKIDLE, new Page.WaitForLoadStateOptions().setTimeout(10000));
            } catch (Exception e) {
                logger.debug("设备管理页面网络空闲等待超时，继续执行: {}", e.getMessage());
            }
            sleep(2000); // 适度等待页面渲染
            
            // 等待固定列表格加载
            try {
                page.waitForSelector(".el-table__fixed-right", new Page.WaitForSelectorOptions().setTimeout(60000));
                logger.info("固定列表格已加载完成");
            } catch (Exception e) {
                logger.warn("等待固定列表格超时: {}", e.getMessage());
                return false;
            }
            
            // 检查是否有移除按钮
            Locator removeButtons = page.locator(".el-table__fixed-right .el-table__fixed-body-wrapper td.el-table_1_column_5 button[data-v-5522e23e].el-button--text");
            int buttonCount = removeButtons.count();
            logger.info("找到 {} 个设备", buttonCount);
            
            if (buttonCount == 0) {
                logger.info("没有找到可移除的设备");
                return false;
            }
            
            // 根据账号等级判断是否需要移除设备
            if (buttonCount <= deviceThreshold) {
                logger.info("当前设备数量 {} 不超过阈值 {}，无需移除设备", buttonCount, deviceThreshold);
                return false;
            }
            
            logger.info("当前设备数量 {} 超过阈值 {}，准备移除登录时间最长的设备", buttonCount, deviceThreshold);
            
            // 获取所有设备信息，包括登录时间
            List<Map<String, Object>> deviceList = new ArrayList<>();
            try {
                // 使用JavaScript获取设备信息，包括登录时间
                Object devicesInfo = page.evaluate("() => {" +
                    "  const rows = document.querySelectorAll('.el-table__body-wrapper tbody tr');" +
                    "  return Array.from(rows).map((row, index) => {" +
                    "    const idCell = row.querySelector('.el-table_1_column_2 .cell');" +
                    "    const deviceId = idCell ? idCell.textContent.trim() : 'unknown';" +
                    "    const loginTimeCell = row.querySelector('.el-table_1_column_3 .cell');" + // 明确指定第3列为登录时间
                    "    const loginTime = loginTimeCell ? loginTimeCell.textContent.trim() : '';" +
                    "    const loginLocationCell = row.querySelector('.el-table_1_column_4 .cell');" + // 获取第4列作为登录地点
                    "    const loginLocation = loginLocationCell ? loginLocationCell.textContent.trim() : '';" +
                    "    return { index, deviceId, loginTime, loginLocation };" + // 同时返回登录地点信息以便日志
                    "  });" +
                    "}");
                
                // 使用Jackson解析JavaScript返回的对象数组
                deviceList = jsonMapper.readValue(
                    jsonMapper.writeValueAsString(devicesInfo),
                    new com.fasterxml.jackson.core.type.TypeReference<List<Map<String, Object>>>() {}
                );
                
                logger.info("获取到 {} 个设备信息", deviceList.size());
                deviceList.forEach(device -> 
                    logger.info("设备信息 - 索引: {}, ID: {}, 登录时间: {}, 登录地点: {}", 
                        device.get("index"), device.get("deviceId"), 
                        device.get("loginTime"), device.get("loginLocation"))
                );
                
                // 找出登录时间最长的设备(列表中登录时间最早的设备)
                Map<String, Object> oldestDevice = findOldestDevice(deviceList);
                if (oldestDevice != null) {
                    int deviceIndex = ((Number) oldestDevice.get("index")).intValue();
                    String deviceId = (String) oldestDevice.get("deviceId");
                    String loginTime = (String) oldestDevice.get("loginTime");
                    String loginLocation = (String) oldestDevice.get("loginLocation");
                    
                    logger.info("找到登录时间最长的设备 - 索引: {}, ID: {}, 登录时间: {}, 登录地点: {}", 
                        deviceIndex, deviceId, loginTime, loginLocation);
                    
                    // 只移除登录时间最长的一个设备
                    logger.info("开始移除设备 ID: {} (索引: {})", deviceId, deviceIndex);
                    
                    // 点击对应索引的移除按钮
                    Locator removeButton = removeButtons.nth(deviceIndex);
                    removeButton.click();
                    
                    // 处理确认对话框
                    boolean confirmed = handleConfirmDialog();
                    if (confirmed) {
                        logger.info("成功移除设备: {}", deviceId);
                        sleep(3000); // 等待页面刷新
                        return true;
                    } else {
                        logger.warn("确认对话框处理失败，设备 {} 可能未被移除", deviceId);
                        return false;
                    }
                } else {
                    logger.warn("未能找到有效的设备信息");
                    return false;
                }
            } catch (Exception e) {
                logger.error("获取或处理设备信息时出错: {}", e.getMessage(), e);
                return false;
            } finally {
                // 操作完成后可选是否导航回主页
                if (!skipNavigateToHomepage) {
                    try {
                        logger.info("设备操作完成，导航回主页...");
                        navigateToHomepage();
                    } catch (Exception e) {
                        logger.warn("导航回主页时发生异常: {}", e.getMessage());
                    }
                } else {
                    logger.info("设备操作完成，按需求跳过导航回主页");
                }
            }
        } catch (Exception e) {
            logger.error("自动移除设备流程出错: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 自动移除设备（默认导航回主页）
     * @return 是否成功移除了设备
     */
    public boolean autoRemoveDevices() {
        return autoRemoveDevices(false);
    }

    /**
     * 找出登录时间最长的设备(时间最早的设备)
     * @param deviceList 设备列表
     * @return 登录时间最长的设备
     */
    private Map<String, Object> findOldestDevice(List<Map<String, Object>> deviceList) {
        if (deviceList == null || deviceList.isEmpty()) {
            return null;
        }
        
        // 找出有登录时间的设备中，登录时间最早的那个
        Map<String, Object> oldestDevice = null;
        LocalDateTime oldestTime = null;
        boolean foundValidTime = false;
        
        // 尝试不同的日期时间格式 - 确保首先尝试表格中实际使用的格式
        List<DateTimeFormatter> formatters = new ArrayList<>();
        formatters.add(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"));  // 表格中显示的实际格式
        formatters.add(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        formatters.add(DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss"));
        
        for (Map<String, Object> device : deviceList) {
            String loginTimeStr = (String) device.get("loginTime");
            String loginLocation = (String) device.get("loginLocation"); // 获取登录地点信息
            
            if (loginTimeStr != null && !loginTimeStr.trim().isEmpty()) {
                boolean parsedSuccessfully = false;
                
                // 如果字符串看起来不像日期(如只包含地名)，跳过此次解析
                if (loginTimeStr.matches(".*中国.*省.*") || !loginTimeStr.matches(".*\\d+.*")) {
                    logger.warn("登录时间字段包含非日期内容: {}", loginTimeStr);
                    continue;
                }
                
                // 尝试各种时间格式解析
                for (DateTimeFormatter formatter : formatters) {
                    try {
                        LocalDateTime loginTime = LocalDateTime.parse(loginTimeStr, formatter);
                        if (oldestTime == null || loginTime.isBefore(oldestTime)) {
                            oldestTime = loginTime;
                            oldestDevice = device;
                            foundValidTime = true;
                            logger.info("成功解析登录时间: {}, 设备索引: {}, 登录地点: {}", 
                                loginTimeStr, device.get("index"), loginLocation);
                        }
                        parsedSuccessfully = true;
                        break; // 成功解析，跳出内循环
                    } catch (Exception e) {
                        // 当前格式解析失败，尝试下一个格式
                        if (formatter.equals(formatters.get(formatters.size() - 1))) {
                            // 所有格式都尝试过了还是失败
                            logger.debug("使用所有时间格式解析 '{}' 都失败", loginTimeStr);
                        }
                    }
                }
                
                if (!parsedSuccessfully) {
                    logger.warn("无法解析登录时间: '{}' - 格式不支持，设备索引: {}, 登录地点: '{}'", 
                        loginTimeStr, device.get("index"), loginLocation);
                    // 第一次遇到的设备作为默认选择（如果之前没有成功解析过时间）
                    if (oldestDevice == null) {
                        oldestDevice = device;
                        logger.info("由于时间解析失败，将使用第一个设备（索引: {}）作为移除目标", device.get("index"));
                    }
                }
            }
        }
        
        // 如果没有找到任何可解析时间的设备，但已经有一个默认设备
        if (!foundValidTime && oldestDevice != null) {
            logger.info("未找到有效的时间格式，将使用索引为 {} 的设备作为移除目标", oldestDevice.get("index"));
            return oldestDevice;
        }
        
        // 极端情况：如果列表中有设备但都没有可用的登录时间信息，返回第一个设备
        if (oldestDevice == null && !deviceList.isEmpty()) {
            oldestDevice = deviceList.get(0);
            logger.info("无有效登录时间信息，将使用列表中第一个设备（索引: {}）作为移除目标", oldestDevice.get("index"));
        }
        
        return oldestDevice;
    }

    /**
     * 检测并等待验证码处理
     * @return true 如果检测到验证码并等待处理完成，false 如果没有验证码
     */
    private boolean detectAndWaitForCaptcha() {
        try {
            // 检查是否启用验证码检测
            if (staticCaptchaConfig != null && !staticCaptchaConfig.isEnabled()) {
                logger.info("验证码检测已禁用，跳过检测");
                return false;
            }

            logger.info("检测是否存在验证码...");

            // 常见的验证码元素选择器
            List<String> captchaSelectorsList = new ArrayList<>(Arrays.asList(
                ".captcha",           // 通用验证码类名
                "#captcha",           // 通用验证码ID
                ".verify-code",       // 验证码类名
                ".verification",      // 验证类名
                ".geetest",          // 极验验证码
                ".nc-container",     // 阿里云验证码
                ".yidun",            // 网易验证码
                "iframe[src*='captcha']", // 验证码iframe
                "iframe[src*='verify']",  // 验证iframe
                "[class*='captcha']", // 包含captcha的类名
                "[id*='captcha']",    // 包含captcha的ID
                "[class*='verify']",  // 包含verify的类名
                "[id*='verify']",     // 包含verify的ID
                "canvas",             // 可能的验证码画布
                ".slider",            // 滑块验证
                ".puzzle"             // 拼图验证
            ));

            // 添加自定义选择器
            if (staticCaptchaConfig != null && staticCaptchaConfig.getCustomSelectors() != null) {
                captchaSelectorsList.addAll(Arrays.asList(staticCaptchaConfig.getCustomSelectors()));
            }

            String[] captchaSelectors = captchaSelectorsList.toArray(new String[0]);

            // 检测验证码
            boolean captchaFound = false;
            String foundSelector = null;

            for (String selector : captchaSelectors) {
                try {
                    Locator captchaElement = page.locator(selector);
                    if (captchaElement.count() > 0 && captchaElement.first().isVisible()) {
                        captchaFound = true;
                        foundSelector = selector;
                        logger.info("检测到验证码元素: {}", selector);
                        break;
                    }
                } catch (Exception e) {
                    // 忽略单个选择器的错误，继续检测其他选择器
                    logger.debug("检测选择器 {} 时出错: {}", selector, e.getMessage());
                }
            }

            if (!captchaFound) {
                logger.info("未检测到验证码，继续正常登录流程");
                return false;
            }

            // 发现验证码，等待人工处理
            logger.warn("检测到验证码！需要人工处理");
            logger.warn("验证码元素选择器: {}", foundSelector);
            logger.warn("请在浏览器中手动完成验证码验证");

            // 等待验证码消失或登录成功
            return waitForCaptchaCompletion(foundSelector);

        } catch (Exception e) {
            logger.error("验证码检测过程中发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 通过网络请求检测等待验证码完成
     * @return true 如果验证码处理完成
     */
    private boolean waitForCaptchaCompletionByRequest() {
        try {
            logger.info("CAPTCHA WAITING: Starting to wait for captcha completion (network request based)");
            System.out.println("CAPTCHA WAITING: Starting to wait for captcha completion (network request based)");

            // 从配置中获取参数，如果配置不存在则使用默认值
            int maxWaitTime = staticCaptchaConfig != null ? staticCaptchaConfig.getWaitTimeout() : 300;
            int checkInterval = staticCaptchaConfig != null ? staticCaptchaConfig.getCheckInterval() : 2;
            boolean showProgress = staticCaptchaConfig != null ? staticCaptchaConfig.isShowProgress() : true;
            int progressInterval = staticCaptchaConfig != null ? staticCaptchaConfig.getProgressInterval() : 10;
            int waitedTime = 0;

            logger.info("CAPTCHA WAITING: Max wait time = {}s, Check interval = {}s", maxWaitTime, checkInterval);
            System.out.println("CAPTCHA WAITING: Max wait time = " + maxWaitTime + "s, Check interval = " + checkInterval + "s");

            while (waitedTime < maxWaitTime) {
                try {
                    // 检查是否已经登录成功
                    boolean loginSuccess = isAlreadyLoggedIn();

                    if (loginSuccess) {
                        logger.info("CAPTCHA WAITING: Login success detected, captcha verification completed");
                        System.out.println("CAPTCHA WAITING: Login success detected, captcha verification completed");
                        return true;
                    }

                    // 检查是否有错误提示
                    if (checkForLoginErrors()) {
                        logger.warn("CAPTCHA WAITING: Login error detected, captcha verification may have failed");
                        System.out.println("CAPTCHA WAITING: Login error detected, captcha verification may have failed");
                        return false;
                    }

                    // 显示等待进度
                    if (showProgress && waitedTime % progressInterval == 0) {
                        logger.info("CAPTCHA WAITING: Waiting for captcha... {}s elapsed, max wait {}s", waitedTime, maxWaitTime);
                        System.out.println("CAPTCHA WAITING: Waiting for captcha... " + waitedTime + "s elapsed, max wait " + maxWaitTime + "s");
                        System.out.println("Please complete the captcha verification manually in the browser");
                    }

                    sleep(checkInterval * 1000);
                    waitedTime += checkInterval;

                } catch (Exception e) {
                    logger.debug("CAPTCHA WAITING: Error checking captcha status: {}", e.getMessage());
                    sleep(checkInterval * 1000);
                    waitedTime += checkInterval;
                }
            }

            logger.warn("CAPTCHA WAITING: Timeout ({}s), continuing login process", maxWaitTime);
            System.out.println("CAPTCHA WAITING: Timeout (" + maxWaitTime + "s), continuing login process");
            return false;

        } catch (Exception e) {
            logger.error("CAPTCHA WAITING: Error occurred while waiting for captcha completion: {}", e.getMessage(), e);
            System.out.println("CAPTCHA WAITING: Error occurred while waiting for captcha completion: " + e.getMessage());
            return false;
        }
    }

    /**
     * 等待验证码完成（原DOM检测方法，保留作为备用）
     * @param captchaSelector 验证码选择器
     * @return true 如果验证码处理完成
     */
    private boolean waitForCaptchaCompletion(String captchaSelector) {
        try {
            logger.info("等待验证码处理完成...");

            // 从配置中获取参数，如果配置不存在则使用默认值
            int maxWaitTime = staticCaptchaConfig != null ? staticCaptchaConfig.getWaitTimeout() : 300;
            int checkInterval = staticCaptchaConfig != null ? staticCaptchaConfig.getCheckInterval() : 2;
            boolean showProgress = staticCaptchaConfig != null ? staticCaptchaConfig.isShowProgress() : true;
            int progressInterval = staticCaptchaConfig != null ? staticCaptchaConfig.getProgressInterval() : 10;
            int waitedTime = 0;

            while (waitedTime < maxWaitTime) {
                try {
                    // 检查验证码是否还存在
                    Locator captchaElement = page.locator(captchaSelector);
                    boolean captchaStillExists = captchaElement.count() > 0 && captchaElement.first().isVisible();

                    // 检查是否已经登录成功
                    boolean loginSuccess = isAlreadyLoggedIn();

                    if (!captchaStillExists || loginSuccess) {
                        if (loginSuccess) {
                            logger.info("检测到登录成功，验证码处理完成");
                        } else {
                            logger.info("验证码元素已消失，可能处理完成");
                        }
                        return true;
                    }

                    // 检查是否有错误提示
                    if (checkForLoginErrors()) {
                        logger.warn("检测到登录错误，可能验证码验证失败");
                        return false;
                    }

                    // 显示等待进度
                    if (showProgress && waitedTime % progressInterval == 0) {
                        logger.info("等待验证码处理中... 已等待 {} 秒，最大等待 {} 秒", waitedTime, maxWaitTime);
                    }

                    sleep(checkInterval * 1000);
                    waitedTime += checkInterval;

                } catch (Exception e) {
                    logger.debug("检查验证码状态时出错: {}", e.getMessage());
                    sleep(checkInterval * 1000);
                    waitedTime += checkInterval;
                }
            }

            logger.warn("验证码等待超时（{}秒），继续登录流程", maxWaitTime);
            return false;

        } catch (Exception e) {
            logger.error("等待验证码完成时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查登录错误信息
     * @return true 如果检测到错误
     */
    private boolean checkForLoginErrors() {
        try {
            String[] errorSelectors = {
                ".error-message",
                ".login-error",
                ".alert-danger",
                ".error-tip",
                "[class*='error']",
                "text=用户名或密码错误",
                "text=验证码错误",
                "text=登录失败"
            };

            for (String selector : errorSelectors) {
                try {
                    Locator errorElement = page.locator(selector);
                    if (errorElement.count() > 0 && errorElement.first().isVisible()) {
                        String errorText = errorElement.first().textContent();
                        if (errorText != null && !errorText.trim().isEmpty()) {
                            logger.warn("检测到登录错误信息: {}", errorText);
                            return true;
                        }
                    }
                } catch (Exception e) {
                    // 忽略单个选择器的错误
                }
            }

            return false;
        } catch (Exception e) {
            logger.debug("检查登录错误时出错: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 通过网络请求检测验证码
     * @param submitButton 登录按钮
     */
    private void detectCaptchaByNetworkRequest(Locator submitButton) {
        try {
            logger.info("=== CAPTCHA DETECTION: Using network request detection ===");
            System.out.println("=== CAPTCHA DETECTION: Using network request detection ===");

            // 获取配置参数
            String captchaKeyword = staticCaptchaConfig != null ?
                staticCaptchaConfig.getCaptchaRequestKeyword() : "captcha.bpd.360.cn";
            String verifyKeyword = staticCaptchaConfig != null ?
                staticCaptchaConfig.getCaptchaVerifyKeyword() : "captcha.bpd.360.cn/v1/verify";
            int detectionDelay = staticCaptchaConfig != null ?
                staticCaptchaConfig.getRequestDetectionDelay() : 5000;

            logger.info("CAPTCHA DETECTION: Keyword = {}, Verify Keyword = {}, Delay = {}ms",
                captchaKeyword, verifyKeyword, detectionDelay);
            System.out.println("CAPTCHA DETECTION: Keyword = " + captchaKeyword +
                ", Verify Keyword = " + verifyKeyword + ", Delay = " + detectionDelay + "ms");

            // 重置检测标记
            this.captchaRequestDetected = false;
            this.captchaVerifyDetected = false;

            // 监听网络请求
            page.onRequest(request -> {
                String url = request.url();
                if (url.contains(captchaKeyword)) {
                    logger.warn("*** CAPTCHA REQUEST DETECTED: {} ***", url);
                    System.out.println("*** CAPTCHA REQUEST DETECTED: " + url + " ***");
                    // 设置标记，表示检测到验证码
                    synchronized (this) {
                        this.captchaRequestDetected = true;
                    }
                } else if (url.contains(verifyKeyword)) {
                    logger.info("*** CAPTCHA VERIFY DETECTED: {} ***", url);
                    System.out.println("*** CAPTCHA VERIFY DETECTED: " + url + " ***");
                    // 设置标记，表示验证码验证完成
                    synchronized (this) {
                        this.captchaVerifyDetected = true;
                    }
                }
            });

            // 点击登录按钮
            submitButton.click();
            logger.info("CAPTCHA DETECTION: Login button clicked, waiting {}ms for captcha requests...", detectionDelay);
            System.out.println("CAPTCHA DETECTION: Login button clicked, waiting " + detectionDelay + "ms for captcha requests...");

            // 等待一段时间让网络请求发出
            sleep(detectionDelay);

            // 检查是否检测到验证码请求
            boolean captchaDetected;
            synchronized (this) {
                captchaDetected = this.captchaRequestDetected;
                // 不重置标记，因为后续等待过程中还需要继续监听
            }

            if (captchaDetected) {
                logger.warn("*** CAPTCHA DETECTED! Manual verification required ***");
                System.out.println("*** CAPTCHA DETECTED! Manual verification required ***");
                System.out.println("=== WAITING FOR MANUAL CAPTCHA VERIFICATION ===");
                logger.warn("Please complete the captcha verification manually in the browser");
                System.out.println("Please complete the captcha verification manually in the browser");
                if (waitForCaptchaVerifyRequest()) {
                    logger.info("CAPTCHA VERIFICATION: Completed successfully, continuing login process");
                    System.out.println("CAPTCHA VERIFICATION: Completed successfully, continuing login process");
                } else {
                    logger.warn("CAPTCHA VERIFICATION: Timeout or failed");
                    System.out.println("CAPTCHA VERIFICATION: Timeout or failed");
                }
            } else {
                logger.info("CAPTCHA DETECTION: No captcha request detected, continuing normal login flow");
                System.out.println("CAPTCHA DETECTION: No captcha request detected, continuing normal login flow");
            }

        } catch (Exception e) {
            logger.error("CAPTCHA DETECTION ERROR: {}", e.getMessage(), e);
            System.out.println("CAPTCHA DETECTION ERROR: " + e.getMessage());
        }
    }

    /**
     * 等待验证码验证完成请求
     * @return true 如果检测到验证完成请求
     */
    private boolean waitForCaptchaVerifyRequest() {
        try {
            logger.info("CAPTCHA WAITING: Waiting for verify request (captcha.bpd.360.cn/v1/verify)");
            System.out.println("CAPTCHA WAITING: Waiting for verify request (captcha.bpd.360.cn/v1/verify)");

            // 从配置中获取参数，如果配置不存在则使用默认值
            int maxWaitTime = staticCaptchaConfig != null ? staticCaptchaConfig.getWaitTimeout() : 300;
            int checkInterval = staticCaptchaConfig != null ? staticCaptchaConfig.getCheckInterval() : 2;
            boolean showProgress = staticCaptchaConfig != null ? staticCaptchaConfig.isShowProgress() : true;
            int progressInterval = staticCaptchaConfig != null ? staticCaptchaConfig.getProgressInterval() : 10;
            int waitedTime = 0;

            logger.info("CAPTCHA WAITING: Max wait time = {}s, Check interval = {}s", maxWaitTime, checkInterval);
            System.out.println("CAPTCHA WAITING: Max wait time = " + maxWaitTime + "s, Check interval = " + checkInterval + "s");

            while (waitedTime < maxWaitTime) {
                try {
                    // 检查是否检测到验证完成请求
                    boolean verifyDetected;
                    synchronized (this) {
                        verifyDetected = this.captchaVerifyDetected;
                    }

                    if (verifyDetected) {
                        logger.info("CAPTCHA WAITING: Verify request detected, captcha verification completed");
                        System.out.println("CAPTCHA WAITING: Verify request detected, captcha verification completed");
                        return true;
                    }

                    // 检查是否已经登录成功
                    boolean loginSuccess = isAlreadyLoggedIn();

                    if (loginSuccess) {
                        logger.info("CAPTCHA WAITING: Login success detected, captcha verification completed");
                        System.out.println("CAPTCHA WAITING: Login success detected, captcha verification completed");
                        return true;
                    }

                    // 检查是否有错误提示
                    if (checkForLoginErrors()) {
                        logger.warn("CAPTCHA WAITING: Login error detected, captcha verification may have failed");
                        System.out.println("CAPTCHA WAITING: Login error detected, captcha verification may have failed");
                        return false;
                    }

                    // 显示等待进度
                    if (showProgress && waitedTime % progressInterval == 0) {
                        logger.info("CAPTCHA WAITING: Waiting for verify request... {}s elapsed, max wait {}s", waitedTime, maxWaitTime);
                        System.out.println("CAPTCHA WAITING: Waiting for verify request... " + waitedTime + "s elapsed, max wait " + maxWaitTime + "s");
                        System.out.println("Please complete the captcha verification manually in the browser");
                    }

                    sleep(checkInterval * 1000);
                    waitedTime += checkInterval;

                } catch (Exception e) {
                    logger.debug("CAPTCHA WAITING: Error checking verify status: {}", e.getMessage());
                    sleep(checkInterval * 1000);
                    waitedTime += checkInterval;
                }
            }

            logger.warn("CAPTCHA WAITING: Timeout ({}s), no verify request detected", maxWaitTime);
            System.out.println("CAPTCHA WAITING: Timeout (" + maxWaitTime + "s), no verify request detected");
            return false;

        } catch (Exception e) {
            logger.error("CAPTCHA WAITING: Error occurred while waiting for verify request: {}", e.getMessage(), e);
            System.out.println("CAPTCHA WAITING: Error occurred while waiting for verify request: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取重试统计报告
     */
    public String getRetryStatisticsReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 重试统计报告 ===\n");
        report.append(String.format("总重试次数: %d\n", totalRetryCount));
        report.append(String.format("重试后成功次数: %d\n", successAfterRetryCount));

        if (!operationRetryStats.isEmpty()) {
            report.append("各操作重试详情:\n");
            operationRetryStats.forEach((operation, retries) ->
                report.append(String.format("  %s: %d 次重试\n", operation, retries))
            );
        }

        double successRate = totalRetryCount > 0 ?
            (double) successAfterRetryCount / totalRetryCount * 100 : 100.0;
        report.append(String.format("重试成功率: %.2f%%\n", successRate));

        return report.toString();
    }

    /**
     * 重置重试统计
     */
    public void resetRetryStatistics() {
        totalRetryCount = 0;
        successAfterRetryCount = 0;
        operationRetryStats.clear();
        logger.info("重试统计已重置");
    }

    /**
     * 获取JSON解析统计报告
     */
    public String getJsonParseStatisticsReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== JSON解析统计报告 ===\n");
        report.append(String.format("总解析尝试次数: %d\n", totalJsonParseAttempts));
        report.append(String.format("解析失败次数: %d\n", jsonParseFailures));

        double failureRate = totalJsonParseAttempts > 0 ?
            (double) jsonParseFailures / totalJsonParseAttempts * 100 : 0.0;
        report.append(String.format("解析失败率: %.2f%%\n", failureRate));

        if (!jsonParseFailuresByType.isEmpty()) {
            report.append("各类型解析失败详情:\n");
            jsonParseFailuresByType.forEach((type, count) ->
                report.append(String.format("  %s: %d 次失败\n", type, count))
            );
        }

        return report.toString();
    }

    /**
     * 重置JSON解析统计
     */
    public void resetJsonParseStatistics() {
        totalJsonParseAttempts = 0;
        jsonParseFailures = 0;
        jsonParseFailuresByType.clear();
        logger.info("JSON解析统计已重置");
    }

    /**
     * 获取综合统计报告
     */
    public String getComprehensiveStatisticsReport() {
        StringBuilder report = new StringBuilder();
        report.append(getRetryStatisticsReport()).append("\n");
        report.append(getJsonParseStatisticsReport());
        return report.toString();
    }

    /**
     * 测试JSON解析错误处理机制
     * 仅用于开发和调试目的
     */
    public void testJsonParseErrorHandling() {
        logger.info("开始测试JSON解析错误处理机制...");

        // 测试null响应
        parseApiResponse(null, "测试操作", null);

        // 测试缺少data字段的JSON
        JSONObject testJson1 = new JSONObject();
        testJson1.put("status", "success");
        parseApiResponse(testJson1, "测试操作", testJson1.toString());

        // 测试data字段不是数组的JSON
        JSONObject testJson2 = new JSONObject();
        testJson2.put("data", "not an array");
        parseApiResponse(testJson2, "测试操作", testJson2.toString());

        logger.info("JSON解析错误处理测试完成");
        logger.info(getJsonParseStatisticsReport());
    }

    /**
     * 记录JSON解析失败的专门日志
     * @param errorMessage 错误信息
     * @param operationType 操作类型
     * @param originalResponseText 原始响应文本
     * @param parseLocation 解析失败的具体位置
     * @param exception 异常对象
     */
    private void logJsonParseError(String errorMessage, String operationType, String originalResponseText,
                                   String parseLocation, Exception exception) {

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
        String location = parseLocation != null ? parseLocation : "未知位置";
        String exceptionInfo = exception != null ? exception.getClass().getSimpleName() + ": " + exception.getMessage() : "无异常信息";

        // 构建错误日志头部
        StringBuilder logBuilder = new StringBuilder();
        logBuilder.append("\n").append("=".repeat(80)).append("\n");
        logBuilder.append("JSON解析失败 - 操作类型: [").append(operationType).append("] - 时间: [").append(timestamp).append("]\n");
        logBuilder.append("解析位置: [").append(location).append("] - 错误: [").append(errorMessage).append("]\n");
        logBuilder.append("异常信息: [").append(exceptionInfo).append("]\n");
        logBuilder.append("-".repeat(80)).append("\n");

        // 处理原始JSON响应
        if (originalResponseText != null && !originalResponseText.trim().isEmpty()) {
            String responseToLog = originalResponseText.trim();

            // 大JSON包的截断策略
            if (responseToLog.length() > 10000) {
                logBuilder.append("原始JSON响应 (截断前10000字符):\n");
                logBuilder.append(responseToLog.substring(0, 10000));
                logBuilder.append("\n... [响应内容过长，已截断，总长度: ").append(responseToLog.length()).append(" 字符] ...\n");
            } else {
                logBuilder.append("原始JSON响应 (完整内容):\n");
                logBuilder.append(responseToLog).append("\n");
            }
        } else {
            logBuilder.append("原始JSON响应: [无响应内容或响应为空]\n");
        }

        logBuilder.append("=".repeat(80));

        // 记录ERROR级别日志
        logger.error(logBuilder.toString());

        // 同时输出到控制台，便于实时监控
        System.err.println(logBuilder.toString());

        // 如果有异常，记录完整的堆栈跟踪
        if (exception != null) {
            logger.error("JSON解析异常堆栈跟踪:", exception);
        }

        // 保存失败的JSON响应到文件，便于后期分析和数据恢复
        saveFailedJsonResponse(operationType, originalResponseText, timestamp);
    }

    /**
     * 保存失败的JSON响应到文件
     * @param operationType 操作类型
     * @param responseText 原始响应文本
     * @param timestamp 时间戳
     */
    private void saveFailedJsonResponse(String operationType, String responseText, String timestamp) {
        if (responseText == null || responseText.trim().isEmpty()) {
            return;
        }

        try {
            // 创建失败响应保存目录
            java.nio.file.Path failedDir = java.nio.file.Paths.get("logs", "failed-json");
            if (!java.nio.file.Files.exists(failedDir)) {
                java.nio.file.Files.createDirectories(failedDir);
            }

            // 生成文件名：操作类型_时间戳.json
            String safeTimestamp = timestamp.replaceAll("[:\\s]", "_").replaceAll("\\.", "_");
            String safeOperationType = operationType.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "_");
            String fileName = String.format("%s_%s.json", safeOperationType, safeTimestamp);

            java.nio.file.Path filePath = failedDir.resolve(fileName);

            // 写入文件
            java.nio.file.Files.write(filePath, responseText.getBytes(java.nio.charset.StandardCharsets.UTF_8));

            logger.info("失败的JSON响应已保存到文件: {}", filePath.toAbsolutePath());

        } catch (Exception e) {
            logger.warn("保存失败的JSON响应时出错: {}", e.getMessage());
        }
    }
}