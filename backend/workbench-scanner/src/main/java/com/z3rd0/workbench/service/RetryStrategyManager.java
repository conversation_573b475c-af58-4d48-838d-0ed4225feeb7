package com.z3rd0.workbench.service;

import com.z3rd0.workbench.exception.TaskProcessingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 智能重试策略管理器
 * 根据异常类型和历史重试情况，动态调整重试策略
 */
@Service
public class RetryStrategyManager {
    
    private static final Logger logger = LoggerFactory.getLogger(RetryStrategyManager.class);
    
    // 存储任务的重试历史信息
    private final ConcurrentMap<Long, TaskRetryHistory> retryHistories = new ConcurrentHashMap<>();
    
    /**
     * 判断是否应该重试
     * 
     * @param taskId 任务ID
     * @param exception 异常信息
     * @param currentRetryCount 当前重试次数
     * @return 重试决策结果
     */
    public RetryDecision shouldRetry(Long taskId, Exception exception, int currentRetryCount) {
        logger.info("评估任务重试策略 - 任务ID: {}, 当前重试次数: {}, 异常类型: {}", 
                   taskId, currentRetryCount, exception.getClass().getSimpleName());
        
        // 获取或创建重试历史
        TaskRetryHistory history = retryHistories.computeIfAbsent(taskId, k -> new TaskRetryHistory(taskId));
        
        // 记录本次失败
        history.recordFailure(exception);
        
        // 根据异常类型确定重试策略
        RetryStrategy strategy = determineRetryStrategy(exception);
        
        // 检查是否超过最大重试次数
        if (currentRetryCount >= strategy.getMaxRetries()) {
            logger.warn("任务 {} 已达到最大重试次数 {}, 不再重试", taskId, strategy.getMaxRetries());
            return RetryDecision.noRetry("已达到最大重试次数");
        }
        
        // 检查是否在冷却期内
        if (history.isInCooldownPeriod()) {
            logger.info("任务 {} 仍在冷却期内，延迟重试", taskId);
            return RetryDecision.delayRetry("任务在冷却期内", calculateNextRetryDelay(strategy, currentRetryCount));
        }
        
        // 计算下次重试延迟
        long delayMs = calculateNextRetryDelay(strategy, currentRetryCount);
        
        logger.info("任务 {} 将在 {}ms 后重试", taskId, delayMs);
        return RetryDecision.retry("根据异常类型重试", delayMs);
    }
    
    /**
     * 根据异常类型确定重试策略
     */
    private RetryStrategy determineRetryStrategy(Exception exception) {
        if (exception instanceof TaskProcessingException) {
            TaskProcessingException tpe = (TaskProcessingException) exception;
            return determineRetryStrategyByErrorType(tpe.getErrorType());
        }
        
        // 根据异常类名判断
        String exceptionName = exception.getClass().getSimpleName();
        
        if (exceptionName.contains("Network") || exceptionName.contains("Timeout") || 
            exceptionName.contains("Connection")) {
            return RetryStrategy.NETWORK_ERROR;
        }
        
        if (exceptionName.contains("Browser") || exceptionName.contains("Playwright")) {
            return RetryStrategy.BROWSER_ERROR;
        }
        
        // 默认策略
        return RetryStrategy.DEFAULT;
    }
    
    /**
     * 根据TaskProcessingException的错误类型确定重试策略
     */
    private RetryStrategy determineRetryStrategyByErrorType(TaskProcessingException.ErrorType errorType) {
        switch (errorType) {
            case NETWORK_ERROR:
            case TIMEOUT:
                return RetryStrategy.NETWORK_ERROR;
                
            case BROWSER_ERROR:
                return RetryStrategy.BROWSER_ERROR;
                
            case LOGIN_FAILED:
                return RetryStrategy.LOGIN_ERROR;
                
            case SYSTEM_BUSY:
                return RetryStrategy.SYSTEM_BUSY;
                
            case INVALID_RULE:
            case INVALID_TIME_RANGE:
            case ACCOUNT_LIMIT_REACHED:
            case NO_DATA_FOUND:
                return RetryStrategy.NO_RETRY; // 业务错误不重试
                
            default:
                return RetryStrategy.DEFAULT;
        }
    }
    
    /**
     * 计算下次重试延迟（指数退避算法）
     */
    private long calculateNextRetryDelay(RetryStrategy strategy, int retryCount) {
        long baseDelay = strategy.getBaseDelayMs();
        double multiplier = strategy.getBackoffMultiplier();
        
        // 指数退避：delay = baseDelay * (multiplier ^ retryCount)
        long delay = (long) (baseDelay * Math.pow(multiplier, retryCount));
        
        // 限制最大延迟
        return Math.min(delay, strategy.getMaxDelayMs());
    }
    
    /**
     * 清理过期的重试历史
     */
    public void cleanupExpiredHistories() {
        LocalDateTime cutoff = LocalDateTime.now().minus(24, ChronoUnit.HOURS);
        retryHistories.entrySet().removeIf(entry -> 
            entry.getValue().getLastFailureTime().isBefore(cutoff));
        
        logger.debug("清理过期重试历史，当前活跃任务数: {}", retryHistories.size());
    }
    
    /**
     * 获取任务重试统计信息
     */
    public TaskRetryStats getRetryStats(Long taskId) {
        TaskRetryHistory history = retryHistories.get(taskId);
        if (history == null) {
            return new TaskRetryStats(taskId, 0, null, null);
        }
        
        return new TaskRetryStats(
            taskId,
            history.getFailureCount(),
            history.getLastFailureTime(),
            history.getLastException()
        );
    }
    
    /**
     * 重试策略枚举
     */
    public enum RetryStrategy {
        // 网络错误：短间隔多次重试
        NETWORK_ERROR(5, 1000L, 1.5, 30000L),
        
        // 浏览器错误：中等间隔重试
        BROWSER_ERROR(3, 5000L, 2.0, 60000L),
        
        // 登录失败：长间隔少次重试
        LOGIN_ERROR(2, 10000L, 2.0, 120000L),
        
        // 系统繁忙：长间隔重试
        SYSTEM_BUSY(3, 15000L, 2.0, 300000L),
        
        // 默认策略
        DEFAULT(3, 5000L, 1.5, 60000L),
        
        // 不重试
        NO_RETRY(0, 0L, 1.0, 0L);
        
        private final int maxRetries;
        private final long baseDelayMs;
        private final double backoffMultiplier;
        private final long maxDelayMs;
        
        RetryStrategy(int maxRetries, long baseDelayMs, double backoffMultiplier, long maxDelayMs) {
            this.maxRetries = maxRetries;
            this.baseDelayMs = baseDelayMs;
            this.backoffMultiplier = backoffMultiplier;
            this.maxDelayMs = maxDelayMs;
        }
        
        public int getMaxRetries() { return maxRetries; }
        public long getBaseDelayMs() { return baseDelayMs; }
        public double getBackoffMultiplier() { return backoffMultiplier; }
        public long getMaxDelayMs() { return maxDelayMs; }
    }
    
    /**
     * 任务重试历史
     */
    private static class TaskRetryHistory {
        private final Long taskId;
        private int failureCount = 0;
        private LocalDateTime lastFailureTime;
        private Exception lastException;
        private LocalDateTime cooldownUntil;
        
        public TaskRetryHistory(Long taskId) {
            this.taskId = taskId;
        }
        
        public void recordFailure(Exception exception) {
            this.failureCount++;
            this.lastFailureTime = LocalDateTime.now();
            this.lastException = exception;
            
            // 设置冷却期（连续失败次数越多，冷却期越长）
            if (failureCount >= 3) {
                long cooldownMinutes = Math.min(failureCount * 2, 30); // 最多30分钟
                this.cooldownUntil = LocalDateTime.now().plus(cooldownMinutes, ChronoUnit.MINUTES);
            }
        }
        
        public boolean isInCooldownPeriod() {
            return cooldownUntil != null && LocalDateTime.now().isBefore(cooldownUntil);
        }
        
        // Getters
        public Long getTaskId() { return taskId; }
        public int getFailureCount() { return failureCount; }
        public LocalDateTime getLastFailureTime() { return lastFailureTime; }
        public Exception getLastException() { return lastException; }
    }
    
    /**
     * 重试决策结果
     */
    public static class RetryDecision {
        private final boolean shouldRetry;
        private final String reason;
        private final long delayMs;
        
        private RetryDecision(boolean shouldRetry, String reason, long delayMs) {
            this.shouldRetry = shouldRetry;
            this.reason = reason;
            this.delayMs = delayMs;
        }
        
        public static RetryDecision retry(String reason, long delayMs) {
            return new RetryDecision(true, reason, delayMs);
        }
        
        public static RetryDecision delayRetry(String reason, long delayMs) {
            return new RetryDecision(true, reason, delayMs);
        }
        
        public static RetryDecision noRetry(String reason) {
            return new RetryDecision(false, reason, 0);
        }
        
        // Getters
        public boolean shouldRetry() { return shouldRetry; }
        public String getReason() { return reason; }
        public long getDelayMs() { return delayMs; }
    }
    
    /**
     * 任务重试统计信息
     */
    public static class TaskRetryStats {
        private final Long taskId;
        private final int failureCount;
        private final LocalDateTime lastFailureTime;
        private final Exception lastException;
        
        public TaskRetryStats(Long taskId, int failureCount, LocalDateTime lastFailureTime, Exception lastException) {
            this.taskId = taskId;
            this.failureCount = failureCount;
            this.lastFailureTime = lastFailureTime;
            this.lastException = lastException;
        }
        
        // Getters
        public Long getTaskId() { return taskId; }
        public int getFailureCount() { return failureCount; }
        public LocalDateTime getLastFailureTime() { return lastFailureTime; }
        public Exception getLastException() { return lastException; }
    }
}
