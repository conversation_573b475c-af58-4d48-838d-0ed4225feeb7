package com.z3rd0.workbench.event;

/**
 * 任务数据统计事件
 * 用于通知数据处理统计更新
 */
public class TaskDataStatsEvent extends TaskEvent {
    private final int itemsInRange;
    private final int itemsOutOfRange;
    private final int duplicateItems;
    
    public TaskDataStatsEvent(String taskName, int itemsInRange, int itemsOutOfRange, int duplicateItems) {
        super(taskName);
        this.itemsInRange = itemsInRange;
        this.itemsOutOfRange = itemsOutOfRange;
        this.duplicateItems = duplicateItems;
    }
    
    public int getItemsInRange() {
        return itemsInRange;
    }
    
    public int getItemsOutOfRange() {
        return itemsOutOfRange;
    }
    
    public int getDuplicateItems() {
        return duplicateItems;
    }
} 