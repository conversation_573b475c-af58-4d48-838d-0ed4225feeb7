package com.z3rd0.workbench.service;

import com.z3rd0.common.model.CookieRecord;
import com.z3rd0.workbench.repository.CookieRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Cookie服务类，用于管理cookie的存储和获取
 */
@Service
public class CookieService {
    private static final Logger logger = LoggerFactory.getLogger(CookieService.class);
    
    @Autowired
    private CookieRepository cookieRepository;
    
    /**
     * 更新cert_common cookie
     * @param certCommonValue cert_common的值
     */
    public boolean updateCertCommon(String certCommonValue) {
        try {
            logger.info("更新cert_common cookie值: {}", certCommonValue);
            Optional<CookieRecord> latestCookieOpt = cookieRepository.findLatestCookie();
            
            if (latestCookieOpt.isPresent()) {
                CookieRecord cookieRecord = latestCookieOpt.get();
                String currentCookie = cookieRecord.getCookie();
                
                // 检查当前cookie格式
                boolean isDirectValue = !currentCookie.contains("=") && !currentCookie.contains("{") && !currentCookie.contains("[");
                
                // 如果是直接存储的cert_common值，直接替换
                if (isDirectValue) {
                    logger.info("当前存储的是直接cert_common值，直接替换");
                    cookieRecord.setCookie(certCommonValue);
                } else {
                    // 使用正则表达式更新或添加cert_common
                    Pattern pattern = Pattern.compile("cert_common=([^;]+)");
                    Matcher matcher = pattern.matcher(currentCookie);
                    
                    String updatedCookie;
                    if (matcher.find()) {
                        // 如果已存在cert_common，则替换它的值
                        updatedCookie = currentCookie.replaceAll("cert_common=[^;]+", "cert_common=" + certCommonValue);
                        logger.info("更新现有cert_common值");
                    } else {
                        // 如果不存在cert_common，则添加它
                        updatedCookie = currentCookie + "; cert_common=" + certCommonValue;
                        logger.info("添加新的cert_common值");
                    }
                    
                    cookieRecord.setCookie(updatedCookie);
                }
                
                // 设置更新时间为当前时间
                LocalDateTime now = LocalDateTime.now();
                cookieRecord.setUpdateTime(now);
                logger.info("设置更新时间为: {}", now);
                CookieRecord savedRecord = cookieRepository.save(cookieRecord);
                logger.info("成功更新cookie记录，更新后的记录: ID={}, updateTime={}", 
                           savedRecord.getId(), savedRecord.getUpdateTime());
                return true;
            } else {
                // 如果没有现有记录，创建新记录
                logger.info("没有找到现有的cookie记录，创建新记录");
                CookieRecord newRecord = new CookieRecord(certCommonValue);
                cookieRepository.save(newRecord);
                logger.info("成功创建新的cookie记录");
                return true;
            }
        } catch (Exception e) {
            logger.error("更新cert_common时发生错误", e);
            return false;
        }
    }
    
    /**
     * 保存cookie
     * @param cookieValue cookie值
     */
    public void saveCookie(String cookieValue) {
        try {
            logger.info("保存新的cookie记录");
            CookieRecord cookieRecord = new CookieRecord(cookieValue);
            cookieRepository.save(cookieRecord);
            logger.info("成功保存cookie记录");
        } catch (Exception e) {
            logger.error("保存cookie时发生错误", e);
        }
    }
    
    /**
     * 获取最新的cookie记录
     * @return 包含最新cookie的Optional对象
     */
    public Optional<CookieRecord> getLatestCookie() {
        try {
            Optional<CookieRecord> latestCookieOpt = cookieRepository.findLatestCookie();
            if (latestCookieOpt.isPresent()) {
                logger.info("找到最新的cookie记录，ID: {}, 创建时间: {}, 更新时间: {}",
                           latestCookieOpt.get().getId(),
                           latestCookieOpt.get().getCreateTime(),
                           latestCookieOpt.get().getUpdateTime());
            } else {
                logger.info("数据库中没有找到cookie记录");
            }
            return latestCookieOpt;
        } catch (Exception e) {
            logger.error("获取最新cookie时发生错误", e);
            return Optional.empty();
        }
    }

    /**
     * 获取有效的cookie记录（检查时效性）
     * @return 包含有效cookie的Optional对象，如果cookie过期则返回空
     */
    public Optional<CookieRecord> getValidCookie() {
        try {
            logger.info("开始查询最新的cookie记录...");
            Optional<CookieRecord> latestCookieOpt = cookieRepository.findLatestCookie();

            if (latestCookieOpt.isPresent()) {
                CookieRecord cookieRecord = latestCookieOpt.get();
                logger.info("找到最新的cookie记录，ID: {}, Cookie值: {}, 创建时间: {}, 更新时间: {}",
                           cookieRecord.getId(),
                           cookieRecord.getCookie(),
                           cookieRecord.getCreateTime(),
                           cookieRecord.getUpdateTime());

                // 检查cookie是否过期（3天有效期）
                if (isCookieValid(cookieRecord)) {
                    logger.info("Cookie仍然有效，可以使用");
                    return latestCookieOpt;
                } else {
                    logger.warn("Cookie已过期（超过3天），需要重新登录");
                    return Optional.empty();
                }
            } else {
                logger.warn("数据库中没有找到cookie记录，可能表为空或查询失败");
                // 添加额外的调试信息
                try {
                    long totalCount = cookieRepository.count();
                    logger.info("数据库中cookie记录总数: {}", totalCount);
                } catch (Exception countEx) {
                    logger.error("查询cookie记录总数时发生错误", countEx);
                }
                return Optional.empty();
            }
        } catch (Exception e) {
            logger.error("获取有效cookie时发生错误", e);
            return Optional.empty();
        }
    }

    /**
     * 检查cookie是否有效（3天内）
     * @param cookieRecord cookie记录
     * @return true如果cookie有效，false如果已过期
     */
    public boolean isCookieValid(CookieRecord cookieRecord) {
        if (cookieRecord == null) {
            logger.warn("Cookie记录为null，无法检查有效性");
            return false;
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime cookieTime = cookieRecord.getUpdateTime() != null ?
                                  cookieRecord.getUpdateTime() : cookieRecord.getCreateTime();

        logger.info("Cookie时间检查详情:");
        logger.info("  - Cookie ID: {}", cookieRecord.getId());
        logger.info("  - 创建时间: {}", cookieRecord.getCreateTime());
        logger.info("  - 更新时间: {}", cookieRecord.getUpdateTime());
        logger.info("  - 使用的时间: {}", cookieTime);
        logger.info("  - 当前时间: {}", now);

        if (cookieTime == null) {
            logger.warn("Cookie记录没有有效的时间信息");
            return false;
        }

        // 计算时间差（小时和分钟）
        java.time.Duration duration = java.time.Duration.between(cookieTime, now);
        long hoursDiff = duration.toHours();
        long minutesDiff = duration.toMinutes();
        long daysDiff = hoursDiff / 24;

        logger.info("Cookie时间差计算:");
        logger.info("  - 相差{}分钟", minutesDiff);
        logger.info("  - 相差{}小时", hoursDiff);
        logger.info("  - 相差{}天", daysDiff);
        logger.info("  - 限制: 72小时(3天)");

        // 3天 = 72小时
        boolean isValid = hoursDiff <= 72;

        if (!isValid) {
            logger.warn("Cookie已过期: 相差{}小时({}天)，超过72小时(3天)限制", hoursDiff, daysDiff);
        } else {
            logger.info("Cookie仍然有效: 相差{}小时({}天)，在72小时(3天)限制内", hoursDiff, daysDiff);
        }

        return isValid;
    }
    
    /**
     * 清空所有cookie记录
     */
    public void clearAllCookies() {
        try {
            logger.info("清空所有cookie记录");
            cookieRepository.deleteAllCookies();
            logger.info("成功清空所有cookie记录");
        } catch (Exception e) {
            logger.error("清空cookie记录时发生错误", e);
        }
    }
}