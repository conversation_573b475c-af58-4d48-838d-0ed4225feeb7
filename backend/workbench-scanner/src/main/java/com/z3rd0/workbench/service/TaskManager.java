package com.z3rd0.workbench.service;

import com.z3rd0.common.model.Task;
import com.z3rd0.workbench.repository.TaskRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 任务管理服务
 * 用于创建、查询、更新任务
 */
@Service
public class TaskManager {

    private static final Logger logger = LoggerFactory.getLogger(TaskManager.class);

    @Autowired
    private TaskRepository taskRepository;

    @Autowired
    private TaskPublisher taskPublisher;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    // 统计信息
    private final AtomicLong totalProcessedTasks = new AtomicLong(0);
    private final AtomicReference<LocalDateTime> lastTaskExecutionTime = new AtomicReference<>();
    private final AtomicLong processingStartTime = new AtomicLong(System.currentTimeMillis());
    
    /**
     * 创建新任务并发送到队列
     * @param name 任务名称
     * @param rule 搜索规则
     * @param timeRange 时间范围
     * @return 创建的任务
     */
    @Transactional
    public Task createAndPublishTask(String name, String rule, String timeRange) {
        // 创建任务实体
        Task task = new Task();
        task.setName(name);
        task.setRule(rule);
        // 不设置筛选范围，这个由执行时更新
        task.setStatus("PENDING");
        task.setRetryCount(0);
        
        // 保存任务
        task = taskRepository.save(task);
        logger.info("已创建新任务: ID={}, 名称={}", task.getId(), name);
        
        // 发布任务到消息队列
        taskPublisher.publishSearchTaskWithId(task.getId(), name, rule, timeRange);
        
        return task;
    }
    
    /**
     * 根据ID查找任务
     * @param id 任务ID
     * @return 任务（可能不存在）
     */
    public Optional<Task> findTaskById(Long id) {
        return taskRepository.findById(id);
    }
    
    /**
     * 查询所有任务
     * @return 任务列表
     */
    public List<Task> findAllTasks() {
        return taskRepository.findAll();
    }
    
    /**
     * 查询特定状态的任务
     * @param status 任务状态
     * @return 任务列表
     */
    public List<Task> findTasksByStatus(String status) {
        return taskRepository.findByStatus(status);
    }
    
    /**
     * 查询未完成的任务（包括待处理和处理中的任务）
     * @return 未完成任务列表
     */
    public List<Task> findPendingTasks() {
        return taskRepository.findByStatusIn(Arrays.asList("PENDING", "PROCESSING"));
    }
    
    /**
     * 更新任务状态
     * @param id 任务ID
     * @param status 新状态
     * @param errorMessage 错误信息（可选）
     * @return 更新后的任务
     */
    @Transactional
    public Task updateTaskStatus(Long id, String status, String errorMessage) {
        Optional<Task> taskOpt = taskRepository.findById(id);
        
        if (taskOpt.isPresent()) {
            Task task = taskOpt.get();
            task.setStatus(status);
            
            if (errorMessage != null) {
                task.setErrorMessage(errorMessage);
            }
            
            // 如果任务已完成，设置完成时间
            if ("COMPLETED".equals(status)) {
                task.setCompletedAt(LocalDateTime.now());
            }
            
            taskRepository.save(task);
            logger.info("已更新任务状态: ID={}, 状态={}", id, status);
            return task;
        } else {
            logger.warn("未找到要更新的任务: ID={}", id);
            throw new RuntimeException("未找到任务: " + id);
        }
    }
    
    /**
     * 重新发布已失败的任务
     * @param taskId 任务ID
     * @return 重新发布的任务
     */
    @Transactional
    public Task republishTask(Long taskId) {
        Optional<Task> taskOpt = taskRepository.findById(taskId);
        
        if (taskOpt.isPresent()) {
            Task task = taskOpt.get();
            
            // 更新任务状态
            task.setStatus("PENDING");
            task.setRetryCount(task.getRetryCount() + 1);
            task.setErrorMessage(null);
            task = taskRepository.save(task);
            
            // 重新发布到消息队列需要时间范围，这里使用默认值
            String defaultTimeRange = "2023-01-01 00:00:00,2023-12-31 23:59:59";
            taskPublisher.publishSearchTaskWithId(task.getId(), task.getName(), task.getRule(), defaultTimeRange);
            
            logger.info("已重新发布任务: ID={}, 名称={}, 重试次数={}", 
                      task.getId(), task.getName(), task.getRetryCount());
            
            return task;
        } else {
            logger.warn("未找到要重新发布的任务: ID={}", taskId);
            throw new RuntimeException("未找到任务: " + taskId);
        }
    }
    
    /**
     * 删除任务
     * @param id 任务ID
     */
    @Transactional
    public void deleteTask(Long id) {
        if (taskRepository.existsById(id)) {
            taskRepository.deleteById(id);
            logger.info("已删除任务: ID={}", id);
        } else {
            logger.warn("未找到要删除的任务: ID={}", id);
            throw new RuntimeException("未找到任务: " + id);
        }
    }

    /**
     * 获取队列大小
     * @return 队列中等待的任务数量
     */
    public int getQueueSize() {
        try {
            // 从RabbitMQ获取队列大小
            Integer messageCount = rabbitTemplate.execute(channel -> {
                try {
                    com.rabbitmq.client.AMQP.Queue.DeclareOk declareOk =
                        channel.queueDeclarePassive("search.task.queue");
                    return declareOk.getMessageCount();
                } catch (Exception e) {
                    logger.warn("获取队列信息失败: {}", e.getMessage());
                    return null;
                }
            });

            if (messageCount != null) {
                return messageCount;
            }

            // 如果无法从RabbitMQ获取，从数据库获取待处理任务数
            List<Task> pendingTasks = findPendingTasks();
            return pendingTasks.size();

        } catch (Exception e) {
            logger.warn("获取队列大小失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 获取处理速度（每分钟处理的任务数）
     * @return 处理速度
     */
    public double getProcessingRate() {
        try {
            long currentTime = System.currentTimeMillis();
            long elapsedTime = currentTime - processingStartTime.get();
            long elapsedMinutes = elapsedTime / (1000 * 60);

            if (elapsedMinutes > 0) {
                return (double) totalProcessedTasks.get() / elapsedMinutes;
            }

            return 0.0;
        } catch (Exception e) {
            logger.warn("获取处理速度失败: {}", e.getMessage());
            return 0.0;
        }
    }

    /**
     * 获取最后任务执行时间
     * @return 最后任务执行时间字符串
     */
    public String getLastTaskExecutionTime() {
        try {
            LocalDateTime lastTime = lastTaskExecutionTime.get();
            if (lastTime != null) {
                return lastTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            return null;
        } catch (Exception e) {
            logger.warn("获取最后任务执行时间失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 记录任务执行
     * 在任务开始执行时调用此方法
     */
    public void recordTaskExecution() {
        totalProcessedTasks.incrementAndGet();
        lastTaskExecutionTime.set(LocalDateTime.now());
        logger.debug("记录任务执行，总处理数: {}", totalProcessedTasks.get());
    }

    /**
     * 重置统计信息
     */
    public void resetStats() {
        totalProcessedTasks.set(0);
        lastTaskExecutionTime.set(null);
        processingStartTime.set(System.currentTimeMillis());
        logger.info("已重置任务统计信息");
    }
}