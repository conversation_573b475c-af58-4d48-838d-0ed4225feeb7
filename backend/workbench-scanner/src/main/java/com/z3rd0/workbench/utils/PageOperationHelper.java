package com.z3rd0.workbench.utils;

import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.WaitForSelectorState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 页面操作辅助工具类
 * 提供常用的页面操作方法
 */
@Component
public class PageOperationHelper {
    
    private static final Logger logger = LoggerFactory.getLogger(PageOperationHelper.class);
    
    /**
     * 等待元素出现
     */
    public boolean waitForElement(Locator locator, String elementName, int timeoutMs) {
        try {
            logger.debug("等待元素出现: {}", elementName);
            locator.waitFor(new Locator.WaitForOptions()
                    .setState(WaitForSelectorState.VISIBLE)
                    .setTimeout(timeoutMs));
            logger.debug("元素已出现: {}", elementName);
            return true;
        } catch (Exception e) {
            logger.warn("等待元素超时: {}, 错误: {}", elementName, e.getMessage());
            return false;
        }
    }
    
    /**
     * 安全的睡眠方法
     */
    public void sleep(int milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("睡眠被中断: {}", e.getMessage());
        }
    }
    
    /**
     * 检查元素是否在视口内
     */
    public boolean isElementInViewport(Locator locator) {
        try {
            // 使用JavaScript检查元素是否在视口内
            Boolean result = (Boolean) locator.evaluate(
                "element => {" +
                "  const rect = element.getBoundingClientRect();" +
                "  return (" +
                "    rect.top >= 0 &&" +
                "    rect.left >= 0 &&" +
                "    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&" +
                "    rect.right <= (window.innerWidth || document.documentElement.clientWidth)" +
                "  );" +
                "}"
            );
            return result != null && result;
        } catch (Exception e) {
            logger.warn("检查元素是否在视口内时出错: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 滚动到页面底部并验证
     */
    public void scrollToBottomWithVerification(Page page) {
        try {
            logger.debug("开始滚动到页面底部...");
            
            // 获取初始滚动位置
            int initialScrollY = (Integer) page.evaluate("window.scrollY");
            
            // 执行滚动
            page.evaluate("window.scrollTo(0, document.body.scrollHeight)");
            sleep(1000); // 等待滚动完成
            
            // 验证滚动是否成功
            int finalScrollY = (Integer) page.evaluate("window.scrollY");
            int documentHeight = (Integer) page.evaluate("document.body.scrollHeight");
            int windowHeight = (Integer) page.evaluate("window.innerHeight");
            
            logger.debug("滚动验证 - 初始位置: {}, 最终位置: {}, 文档高度: {}, 窗口高度: {}", 
                       initialScrollY, finalScrollY, documentHeight, windowHeight);
            
            // 如果没有滚动到底部，再次尝试
            if (finalScrollY + windowHeight < documentHeight - 100) {
                logger.debug("未完全滚动到底部，再次尝试...");
                page.evaluate("window.scrollTo(0, document.body.scrollHeight)");
                sleep(1000);
            }
            
            logger.debug("滚动到页面底部完成");
        } catch (Exception e) {
            logger.warn("滚动到页面底部时出错: {}", e.getMessage());
        }
    }
    
    /**
     * 平滑滚动到指定位置
     */
    public void smoothScrollTo(Page page, int targetY) {
        try {
            page.evaluate("window.scrollTo({top: " + targetY + ", behavior: 'smooth'})");
            sleep(1500); // 等待平滑滚动完成
        } catch (Exception e) {
            logger.warn("平滑滚动时出错: {}", e.getMessage());
        }
    }
    
    /**
     * 检查页面是否加载完成
     */
    public boolean isPageLoaded(Page page) {
        try {
            String readyState = (String) page.evaluate("document.readyState");
            return "complete".equals(readyState);
        } catch (Exception e) {
            logger.warn("检查页面加载状态时出错: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 等待页面加载完成
     */
    public boolean waitForPageLoad(Page page, int timeoutMs) {
        long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime < timeoutMs) {
            if (isPageLoaded(page)) {
                return true;
            }
            sleep(100);
        }
        return false;
    }
    
    /**
     * 安全点击元素
     */
    public boolean safeClick(Locator locator, String elementName) {
        try {
            if (waitForElement(locator, elementName, 5000)) {
                locator.click();
                logger.debug("成功点击元素: {}", elementName);
                return true;
            } else {
                logger.warn("元素不可见，无法点击: {}", elementName);
                return false;
            }
        } catch (Exception e) {
            logger.error("点击元素时出错: {}, 错误: {}", elementName, e.getMessage());
            return false;
        }
    }
    
    /**
     * 安全填写输入框
     */
    public boolean safeFill(Locator locator, String value, String fieldName) {
        try {
            if (waitForElement(locator, fieldName, 5000)) {
                locator.fill(value);
                logger.debug("成功填写字段: {}", fieldName);
                return true;
            } else {
                logger.warn("输入框不可见，无法填写: {}", fieldName);
                return false;
            }
        } catch (Exception e) {
            logger.error("填写输入框时出错: {}, 错误: {}", fieldName, e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取元素文本内容
     */
    public String getElementText(Locator locator, String elementName) {
        try {
            if (waitForElement(locator, elementName, 5000)) {
                String text = locator.textContent();
                logger.debug("获取元素文本: {} = {}", elementName, text);
                return text;
            } else {
                logger.warn("元素不可见，无法获取文本: {}", elementName);
                return null;
            }
        } catch (Exception e) {
            logger.error("获取元素文本时出错: {}, 错误: {}", elementName, e.getMessage());
            return null;
        }
    }
    
    /**
     * 检查元素是否存在
     */
    public boolean isElementPresent(Locator locator) {
        try {
            return locator.count() > 0;
        } catch (Exception e) {
            logger.warn("检查元素是否存在时出错: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 等待元素消失
     */
    public boolean waitForElementToDisappear(Locator locator, String elementName, int timeoutMs) {
        try {
            logger.debug("等待元素消失: {}", elementName);
            locator.waitFor(new Locator.WaitForOptions()
                    .setState(WaitForSelectorState.HIDDEN)
                    .setTimeout(timeoutMs));
            logger.debug("元素已消失: {}", elementName);
            return true;
        } catch (Exception e) {
            logger.warn("等待元素消失超时: {}, 错误: {}", elementName, e.getMessage());
            return false;
        }
    }
}
