package com.z3rd0.workbench.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.*;
import java.util.concurrent.TimeUnit;

/**
 * 网络诊断工具类
 * 用于检测网络连接状态和诊断网络问题
 */
public class NetworkDiagnostic {
    
    private static final Logger logger = LoggerFactory.getLogger(NetworkDiagnostic.class);
    
    /**
     * 检测网络连接状态
     */
    public static boolean checkNetworkConnection() {
        logger.info("开始网络连接检测...");
        
        // 检测列表
        String[] testUrls = {
            "https://www.baidu.com",
            "https://quake.360.net",
            "https://www.google.com",
            "http://httpbin.org"
        };
        
        boolean hasConnection = false;
        
        for (String url : testUrls) {
            try {
                if (testUrlConnection(url)) {
                    logger.info("网络连接正常 - 可以访问: {}", url);
                    hasConnection = true;
                    break;
                } else {
                    logger.warn("无法访问: {}", url);
                }
            } catch (Exception e) {
                logger.warn("测试 {} 时发生异常: {}", url, e.getMessage());
            }
        }
        
        if (!hasConnection) {
            logger.error("网络连接检测失败 - 所有测试URL都无法访问");
            diagnoseNetworkIssues();
        }
        
        return hasConnection;
    }
    
    /**
     * 测试单个URL的连接
     */
    private static boolean testUrlConnection(String urlString) {
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            connection.setRequestProperty("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            
            int responseCode = connection.getResponseCode();
            connection.disconnect();
            
            return responseCode >= 200 && responseCode < 400;
        } catch (Exception e) {
            logger.debug("URL连接测试失败 {}: {}", urlString, e.getMessage());
            return false;
        }
    }
    
    /**
     * 诊断网络问题
     */
    private static void diagnoseNetworkIssues() {
        logger.info("开始网络问题诊断...");
        
        // 1. 检查DNS解析
        checkDNSResolution();
        
        // 2. 检查代理设置
        checkProxySettings();
        
        // 3. 检查防火墙
        checkFirewallSettings();
        
        // 4. 提供解决建议
        provideSolutions();
    }
    
    /**
     * 检查DNS解析
     */
    private static void checkDNSResolution() {
        try {
            logger.info("检查DNS解析...");
            InetAddress address = InetAddress.getByName("www.baidu.com");
            logger.info("DNS解析正常 - www.baidu.com -> {}", address.getHostAddress());
        } catch (UnknownHostException e) {
            logger.error("DNS解析失败: {}", e.getMessage());
            logger.error("建议检查DNS设置，可尝试使用*******或***************");
        }
    }
    
    /**
     * 检查代理设置
     */
    private static void checkProxySettings() {
        logger.info("检查代理设置...");
        
        String httpProxy = System.getProperty("http.proxyHost");
        String httpsProxy = System.getProperty("https.proxyHost");
        
        if (httpProxy != null || httpsProxy != null) {
            logger.warn("检测到代理设置:");
            if (httpProxy != null) {
                logger.warn("HTTP代理: {}:{}", httpProxy, System.getProperty("http.proxyPort"));
            }
            if (httpsProxy != null) {
                logger.warn("HTTPS代理: {}:{}", httpsProxy, System.getProperty("https.proxyPort"));
            }
            logger.warn("代理设置可能影响浏览器网络连接");
        } else {
            logger.info("未检测到系统代理设置");
        }
    }
    
    /**
     * 检查防火墙设置
     */
    private static void checkFirewallSettings() {
        logger.info("检查防火墙设置...");
        
        // 尝试连接常用端口
        int[] testPorts = {80, 443, 8080};
        String testHost = "www.baidu.com";
        
        for (int port : testPorts) {
            try (Socket socket = new Socket()) {
                socket.connect(new InetSocketAddress(testHost, port), 3000);
                logger.info("端口 {} 连接正常", port);
            } catch (IOException e) {
                logger.warn("端口 {} 连接失败: {}", port, e.getMessage());
            }
        }
    }
    
    /**
     * 提供解决方案建议
     */
    private static void provideSolutions() {
        logger.info("=== 网络问题解决建议 ===");
        logger.info("1. 检查网络连接是否正常");
        logger.info("2. 检查防火墙是否阻止了Java应用的网络访问");
        logger.info("3. 检查是否有代理软件影响网络连接");
        logger.info("4. 尝试关闭VPN或代理软件");
        logger.info("5. 检查DNS设置，可尝试更换DNS服务器");
        logger.info("6. 如果在企业网络环境，请联系网络管理员");
        logger.info("7. 尝试重启网络适配器或重启计算机");
        logger.info("========================");
    }
    
    /**
     * 获取网络状态报告
     */
    public static String getNetworkStatusReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 网络状态报告 ===\n");
        
        // 基本连接测试
        boolean connected = checkNetworkConnection();
        report.append("网络连接状态: ").append(connected ? "正常" : "异常").append("\n");
        
        // 系统网络信息
        try {
            report.append("本机IP地址: ").append(InetAddress.getLocalHost().getHostAddress()).append("\n");
        } catch (Exception e) {
            report.append("本机IP地址: 获取失败\n");
        }
        
        // 代理信息
        String httpProxy = System.getProperty("http.proxyHost");
        if (httpProxy != null) {
            report.append("HTTP代理: ").append(httpProxy).append(":").append(System.getProperty("http.proxyPort")).append("\n");
        } else {
            report.append("HTTP代理: 未设置\n");
        }
        
        report.append("==================");
        return report.toString();
    }
}
