package com.z3rd0.workbench.repository;

import com.z3rd0.common.model.Task;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 任务仓库接口
 * 提供对Task实体的数据访问操作
 */
@Repository
public interface TaskRepository extends JpaRepository<Task, Long> {
    
    /**
     * 根据任务状态查找任务
     * @param status 任务状态
     * @return 任务列表
     */
    List<Task> findByStatus(String status);
    
    /**
     * 根据任务名称查找任务（模糊匹配）
     * @param name 任务名称
     * @return 任务列表
     */
    List<Task> findByNameContaining(String name);
    
    /**
     * 查找所有未完成的任务（包括待处理和处理中的任务）
     * @return 未完成任务列表
     */
    List<Task> findByStatusIn(List<String> statusList);
    
    /**
     * 根据名称和状态查找任务
     * @param name 任务名称
     * @param status 任务状态
     * @return 任务列表
     */
    List<Task> findByNameAndStatus(String name, String status);

    /**
     * 根据状态查找任务，按优先级和创建时间排序
     * @param status 任务状态
     * @return 按优先级排序的任务列表（优先级数字越小越靠前）
     */
    List<Task> findByStatusOrderByPriorityAscCreatedAtAsc(String status);

    /**
     * 根据状态列表查找任务，按优先级和创建时间排序
     * @param statusList 任务状态列表
     * @return 按优先级排序的任务列表
     */
    List<Task> findByStatusInOrderByPriorityAscCreatedAtAsc(List<String> statusList);

    /**
     * 查找指定优先级范围内的待处理任务
     * @param minPriority 最小优先级（数字越小优先级越高）
     * @param maxPriority 最大优先级
     * @return 任务列表
     */
    List<Task> findByStatusAndPriorityBetweenOrderByPriorityAscCreatedAtAsc(
        String status, Integer minPriority, Integer maxPriority);
} 