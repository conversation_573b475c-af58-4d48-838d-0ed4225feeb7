package com.z3rd0.workbench.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 浏览器启动配置
 */
@Configuration
@ConfigurationProperties(prefix = "crawler.browser.startup")
public class BrowserStartupConfig {
    
    /**
     * 是否在应用启动时预初始化浏览器
     */
    private boolean preInitialize = false;
    
    /**
     * 启动时浏览器初始化失败是否阻塞应用启动
     */
    private boolean failFast = false;
    
    /**
     * 启动时浏览器初始化超时时间(秒)
     */
    private int initTimeout = 120;
    
    /**
     * 启动时浏览器初始化重试次数
     */
    private int initRetryCount = 2;
    
    /**
     * 启动时浏览器初始化重试间隔(毫秒)
     */
    private int initRetryDelay = 15000;

    // Getters and Setters
    public boolean isPreInitialize() {
        return preInitialize;
    }

    public void setPreInitialize(boolean preInitialize) {
        this.preInitialize = preInitialize;
    }

    public boolean isFailFast() {
        return failFast;
    }

    public void setFailFast(boolean failFast) {
        this.failFast = failFast;
    }

    public int getInitTimeout() {
        return initTimeout;
    }

    public void setInitTimeout(int initTimeout) {
        this.initTimeout = initTimeout;
    }

    public int getInitRetryCount() {
        return initRetryCount;
    }

    public void setInitRetryCount(int initRetryCount) {
        this.initRetryCount = initRetryCount;
    }

    public int getInitRetryDelay() {
        return initRetryDelay;
    }

    public void setInitRetryDelay(int initRetryDelay) {
        this.initRetryDelay = initRetryDelay;
    }
}
