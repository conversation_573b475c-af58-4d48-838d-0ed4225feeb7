package com.z3rd0.workbench.controller;

import com.z3rd0.workbench.service.TaskManager;
import com.z3rd0.workbench.service.TaskStateManager;
import com.z3rd0.workbench.utils.SingleBrowserManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * Scanner服务状态控制器
 * 提供Scanner模块的运行状态和监控信息
 */
@RestController
@RequestMapping("/api/scanner")
public class ScannerStatusController {
    
    private static final Logger logger = LoggerFactory.getLogger(ScannerStatusController.class);
    
    @Autowired
    private SingleBrowserManager browserManager;
    
    @Autowired
    private TaskStateManager taskStateManager;
    
    @Autowired
    private TaskManager taskManager;
    
    /**
     * 获取Scanner服务状态
     * 提供Scanner模块的详细运行状态信息
     * @return Scanner状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getScannerStatus() {
        try {
            logger.debug("获取Scanner服务状态");
            
            Map<String, Object> scannerStatus = new HashMap<>();
            
            // 基本服务信息
            scannerStatus.put("service", "workbench-scanner");
            scannerStatus.put("status", "运行中");
            scannerStatus.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            
            // 浏览器状态信息
            Map<String, Object> browserInfo = new HashMap<>();
            browserInfo.put("initialized", browserManager.isBrowserInitialized());
            browserInfo.put("status", browserManager.getBrowserStatus());
            browserInfo.put("processing", browserManager.isProcessingTask());
            browserInfo.put("instances", browserManager.isBrowserInitialized() ? 1 : 0);
            scannerStatus.put("browser", browserInfo);
            
            // 任务状态信息
            Map<String, Object> taskInfo = new HashMap<>();
            try {
                // 获取当前正在执行的任务名称
                String currentTaskName = getCurrentTaskName();
                taskInfo.put("currentTaskName", currentTaskName);

                // 获取活跃任务数量（正在处理的任务）
                int activeTasks = browserManager.isProcessingTask() ? 1 : 0;
                taskInfo.put("activeTasks", activeTasks);

                // 获取队列大小（从RabbitMQ获取实际队列大小）
                int queueSize = getQueueSize();
                taskInfo.put("queueSize", queueSize);

                // 获取最近任务执行时间（持久化记录）
                String lastTaskTime = getLastTaskExecutionTime();
                taskInfo.put("lastTaskTime", lastTaskTime);

                // 获取内存使用情况
                String memoryUsage = getMemoryUsage();
                taskInfo.put("memoryUsage", memoryUsage);

                // 获取处理速度
                double processingRate = getProcessingRate();
                taskInfo.put("processingRate", processingRate);

            } catch (Exception e) {
                logger.warn("获取任务状态信息失败: {}", e.getMessage());
                taskInfo.put("currentTaskName", "无");
                taskInfo.put("activeTasks", 0);
                taskInfo.put("queueSize", 0);
                taskInfo.put("lastTaskTime", "无");
                taskInfo.put("memoryUsage", "N/A");
                taskInfo.put("processingRate", 0.0);
            }
            scannerStatus.put("tasks", taskInfo);
            
            // 服务健康状态评估
            String healthStatus = evaluateHealthStatus(browserInfo, taskInfo);
            scannerStatus.put("health", healthStatus);
            
            // 构建前端期望的数据格式
            Map<String, Object> frontendData = new HashMap<>();
            frontendData.put("status", scannerStatus.get("status"));
            frontendData.put("currentTaskName", taskInfo.get("currentTaskName"));
            frontendData.put("activeTasks", taskInfo.get("activeTasks"));
            frontendData.put("queueSize", taskInfo.get("queueSize"));
            frontendData.put("browserInstances", browserInfo.get("instances"));
            frontendData.put("lastTaskTime", taskInfo.get("lastTaskTime"));
            frontendData.put("memoryUsage", taskInfo.get("memoryUsage"));
            frontendData.put("processingRate", taskInfo.get("processingRate"));

            // 构建响应格式，与system模块保持一致
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Scanner状态获取成功");
            response.put("data", frontendData);
            
            logger.debug("Scanner状态获取成功: 浏览器状态={}, 活跃任务={}", 
                        browserManager.getBrowserStatus(), taskInfo.get("activeTasks"));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取Scanner状态失败: {}", e.getMessage(), e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取Scanner状态失败: " + e.getMessage());
            errorResponse.put("data", null);
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
    
    /**
     * 获取当前正在执行的任务名称
     * @return 当前任务名称，如果没有则返回"无"
     */
    private String getCurrentTaskName() {
        try {
            if (browserManager.isProcessingTask()) {
                // 获取当前正在执行的任务名称（最新的未完成任务）
                String currentTaskName = taskStateManager.getCurrentExecutingTaskName();
                if (currentTaskName != null && !currentTaskName.isEmpty()) {
                    logger.debug("获取到当前执行任务: {}", currentTaskName);
                    return currentTaskName;
                }

                // 如果没有找到当前执行任务，但浏览器显示正在处理，可能是状态不同步
                logger.warn("浏览器显示正在处理任务，但没有找到当前执行任务");
            }
            return "无";
        } catch (Exception e) {
            logger.warn("获取当前任务名称失败: {}", e.getMessage());
            return "无";
        }
    }

    /**
     * 获取队列大小
     * @return 队列中等待的任务数量
     */
    private int getQueueSize() {
        try {
            // 从TaskManager获取队列大小
            return taskManager.getQueueSize();
        } catch (Exception e) {
            logger.warn("获取队列大小失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 获取内存使用情况
     * @return 内存使用情况字符串
     */
    private String getMemoryUsage() {
        try {
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;

            // 转换为MB并格式化
            double usedMB = usedMemory / (1024.0 * 1024.0);
            return String.format("%.1f MB", usedMB);
        } catch (Exception e) {
            logger.warn("获取内存使用情况失败: {}", e.getMessage());
            return "N/A";
        }
    }

    /**
     * 获取处理速度
     * @return 每分钟处理的任务数
     */
    private double getProcessingRate() {
        try {
            // 从TaskManager获取处理速度统计
            return taskManager.getProcessingRate();
        } catch (Exception e) {
            logger.warn("获取处理速度失败: {}", e.getMessage());
            return 0.0;
        }
    }

    /**
     * 获取最近任务执行时间
     * @return 最近任务执行时间字符串
     */
    private String getLastTaskExecutionTime() {
        try {
            // 从TaskManager获取最后任务执行时间
            String lastTaskTime = taskManager.getLastTaskExecutionTime();
            if (lastTaskTime != null && !lastTaskTime.isEmpty()) {
                return lastTaskTime;
            }

            // 如果没有历史记录，但当前正在执行任务，返回当前时间
            if (browserManager.isProcessingTask()) {
                return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }

            return "无";
        } catch (Exception e) {
            logger.warn("获取最近任务执行时间失败: {}", e.getMessage());
            return "无";
        }
    }
    
    /**
     * 评估服务健康状态
     * @param browserInfo 浏览器信息
     * @param taskInfo 任务信息
     * @return 健康状态字符串
     */
    private String evaluateHealthStatus(Map<String, Object> browserInfo, Map<String, Object> taskInfo) {
        try {
            boolean browserInitialized = (Boolean) browserInfo.get("initialized");
            String browserStatus = (String) browserInfo.get("status");
            
            // 如果浏览器未初始化或状态异常，标记为警告
            if (!browserInitialized || !"ready".equals(browserStatus)) {
                return "warning";
            }
            
            // 其他情况认为是健康的
            return "healthy";
            
        } catch (Exception e) {
            logger.warn("评估健康状态失败: {}", e.getMessage());
            return "unknown";
        }
    }
}
