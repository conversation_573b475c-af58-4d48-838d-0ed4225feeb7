package com.z3rd0.workbench.debug;

import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Cookie调试工具，用于直接查询数据库中的cookie数据
 */
public class CookieDebugger {
    
    // 安全提示：请使用环境变量或配置文件来存储敏感信息
    private static final String DB_URL = System.getenv("DB_URL") != null ?
        System.getenv("DB_URL") : "************************************************************************************************************************************";
    private static final String DB_USER = System.getenv("DB_USER") != null ?
        System.getenv("DB_USER") : "root";
    private static final String DB_PASSWORD = System.getenv("DB_PASSWORD") != null ?
        System.getenv("DB_PASSWORD") : "请设置DB_PASSWORD环境变量";
    
    public static void main(String[] args) {
        try {
            // 加载MySQL驱动
            Class.forName("com.mysql.cj.jdbc.Driver");
            
            // 建立连接
            Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
            System.out.println("数据库连接成功！");
            
            // 查询cookie_records表
            String sql = "SELECT * FROM cookie_records ORDER BY update_time DESC";
            PreparedStatement stmt = conn.prepareStatement(sql);
            ResultSet rs = stmt.executeQuery();
            
            System.out.println("=== Cookie Records ===");
            boolean hasData = false;
            while (rs.next()) {
                hasData = true;
                long id = rs.getLong("id");
                String cookie = rs.getString("cookie");
                Timestamp createTime = rs.getTimestamp("create_time");
                Timestamp updateTime = rs.getTimestamp("update_time");
                
                System.out.println("ID: " + id);
                System.out.println("Cookie: " + cookie);
                System.out.println("Create Time: " + createTime);
                System.out.println("Update Time: " + updateTime);
                
                // 计算时间差
                if (updateTime != null) {
                    LocalDateTime updateDateTime = updateTime.toLocalDateTime();
                    LocalDateTime now = LocalDateTime.now();
                    long hoursDiff = java.time.Duration.between(updateDateTime, now).toHours();
                    System.out.println("Hours since update: " + hoursDiff);
                    System.out.println("Is valid (< 72 hours): " + (hoursDiff <= 72));
                }
                System.out.println("---");
            }
            
            if (!hasData) {
                System.out.println("没有找到任何cookie记录");
            }
            
            // 关闭连接
            rs.close();
            stmt.close();
            conn.close();
            
        } catch (Exception e) {
            System.err.println("数据库操作失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
