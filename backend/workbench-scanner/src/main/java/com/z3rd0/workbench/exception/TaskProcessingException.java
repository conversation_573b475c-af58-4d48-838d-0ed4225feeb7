package com.z3rd0.workbench.exception;

/**
 * 任务处理异常
 * 包含更多任务处理相关的异常信息
 */
public class TaskProcessingException extends Exception {

    private final String taskName;
    private final String errorCode;
    private final boolean isRetryable;
    private final ErrorType errorType;
    
    /**
     * 异常类型枚举
     */
    public enum ErrorType {
        // 系统错误
        SYSTEM_BUSY("SYSTEM_BUSY", "系统繁忙", false),
        BROWSER_ERROR("BROWSER_ERROR", "浏览器错误", true),
        LOGIN_FAILED("LOGIN_FAILED", "登录失败", true),
        NETWORK_ERROR("NETWORK_ERROR", "网络错误", true),
        TIMEOUT("TIMEOUT", "操作超时", true),
        
        // 业务错误
        INVALID_RULE("INVALID_RULE", "无效的搜索规则", false),
        INVALID_TIME_RANGE("INVALID_TIME_RANGE", "无效的时间范围", false),
        ACCOUNT_LIMIT_REACHED("ACCOUNT_LIMIT_REACHED", "账号数据量限制已达到", false),
        NO_DATA_FOUND("NO_DATA_FOUND", "未找到数据", false),
        
        // 其他错误
        UNKNOWN_ERROR("UNKNOWN_ERROR", "未知错误", true);
        
        private final String code;
        private final String message;
        private final boolean retryable;
        
        ErrorType(String code, String message, boolean retryable) {
            this.code = code;
            this.message = message;
            this.retryable = retryable;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getMessage() {
            return message;
        }
        
        public boolean isRetryable() {
            return retryable;
        }
    }
    
    /**
     * 创建任务处理异常
     * @param taskName 任务名称
     * @param message 错误消息
     * @param errorType 错误类型
     */
    public TaskProcessingException(String taskName, String message, ErrorType errorType) {
        super(message);
        this.taskName = taskName;
        this.errorCode = errorType.getCode();
        this.isRetryable = errorType.isRetryable();
        this.errorType = errorType;
    }
    
    /**
     * 创建任务处理异常（使用错误码字符串）
     * @param taskName 任务名称
     * @param message 错误消息
     * @param errorCode 错误码
     */
    public TaskProcessingException(String taskName, String message, String errorCode) {
        super(message);
        this.taskName = taskName;
        this.errorCode = errorCode;

        // 根据错误码判断是否可重试
        boolean canRetry = true;
        ErrorType foundType = ErrorType.UNKNOWN_ERROR;
        for (ErrorType type : ErrorType.values()) {
            if (type.getCode().equals(errorCode)) {
                canRetry = type.isRetryable();
                foundType = type;
                break;
            }
        }
        this.isRetryable = canRetry;
        this.errorType = foundType;
    }
    
    /**
     * 创建带有原因的任务处理异常
     * @param taskName 任务名称
     * @param message 错误消息
     * @param errorType 错误类型
     * @param cause 原因异常
     */
    public TaskProcessingException(String taskName, String message, ErrorType errorType, Throwable cause) {
        super(message, cause);
        this.taskName = taskName;
        this.errorCode = errorType.getCode();
        this.isRetryable = errorType.isRetryable();
        this.errorType = errorType;
    }
    
    /**
     * 获取任务名称
     * @return 任务名称
     */
    public String getTaskName() {
        return taskName;
    }
    
    /**
     * 获取错误码
     * @return 错误码
     */
    public String getErrorCode() {
        return errorCode;
    }
    
    /**
     * 判断是否可重试
     * @return 是否可重试
     */
    public boolean isRetryable() {
        return isRetryable;
    }
    
    /**
     * 根据异常类型和消息创建异常
     * @param taskName 任务名称
     * @param errorType 错误类型
     * @return 任务处理异常
     */
    public static TaskProcessingException of(String taskName, ErrorType errorType) {
        return new TaskProcessingException(taskName, errorType.getMessage(), errorType);
    }
    
    /**
     * 根据异常类型、消息和原因创建异常
     * @param taskName 任务名称
     * @param errorType 错误类型
     * @param cause 原因异常
     * @return 任务处理异常
     */
    public static TaskProcessingException of(String taskName, ErrorType errorType, Throwable cause) {
        return new TaskProcessingException(taskName, errorType.getMessage(), errorType, cause);
    }
    
    /**
     * 带自定义消息的异常创建
     * @param taskName 任务名称
     * @param errorType 错误类型
     * @param message 自定义消息
     * @return 任务处理异常
     */
    public static TaskProcessingException of(String taskName, ErrorType errorType, String message) {
        return new TaskProcessingException(taskName, message, errorType);
    }

    /**
     * 获取错误类型
     * @return 错误类型
     */
    public ErrorType getErrorType() {
        return errorType;
    }
}