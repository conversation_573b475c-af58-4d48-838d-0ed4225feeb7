package com.z3rd0.workbench.event;

/**
 * 任务日志事件
 * 用于通知新的任务日志
 */
public class TaskLogEvent extends TaskEvent {
    private final String message;
    private final String type;
    
    public TaskLogEvent(String taskName, String message, String type) {
        super(taskName);
        this.message = message;
        this.type = type;
    }
    
    public TaskLogEvent(String taskName, String message) {
        this(taskName, message, "info");
    }
    
    public String getMessage() {
        return message;
    }
    
    public String getType() {
        return type;
    }
} 