package com.z3rd0.workbench.repository;

import com.z3rd0.common.model.SearchResult;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SearchResultRepository extends JpaRepository<SearchResult, Long> {
    /**
     * 根据任务名称查找搜索结果
     * @param taskName 任务名称
     * @return 搜索结果列表
     */
    List<SearchResult> findByTaskName(String taskName);
    
    /**
     * 根据URL和任务名称检查是否存在记录
     * @param url URL
     * @param taskName 任务名称
     * @return 是否存在
     */
    boolean existsByUrlAndTaskName(String url, String taskName);
    
    /**
     * 根据域名和标题检查是否存在记录（传统查询，向后兼容）
     * @param domain 域名
     * @param title 标题
     * @return 是否存在
     */
    @Query("SELECT COUNT(s) > 0 FROM SearchResult s WHERE s.domain = :domain AND s.title = :title")
    boolean existsByDomainAndTitle(
        @Param("domain") String domain, 
        @Param("title") String title
    );
    
    /**
     * 根据原始ID和标题检查是否存在记录（用于去重）
     * @param originalId 原始数据中的ID
     * @param title 标题
     * @return 是否存在
     */
    boolean existsByOriginalIdAndTitle(String originalId, String title);
    
    /**
     * 根据原始ID、标题和路径检查是否存在记录（更精确的去重）
     * @param originalId 原始数据中的ID
     * @param title 标题
     * @param path 路径
     * @return 是否存在
     */
    boolean existsByOriginalIdAndTitleAndPath(String originalId, String title, String path);
    
    /**
     * 根据任务名称查询搜索结果，按ID降序排序
     * @param taskName 任务名称
     * @return 搜索结果列表
     */
    List<SearchResult> findByTaskNameOrderByIdDesc(String taskName);
    
    /**
     * 查询所有不同的任务名称
     * @return 任务名称列表
     */
    @Query("SELECT DISTINCT sr.taskName FROM SearchResult sr ORDER BY sr.taskName")
    List<String> findAllDistinctTaskNames();
    
    /**
     * 根据任务名称和时间范围查询搜索结果
     * @param taskName 任务名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 搜索结果列表
     */
    @Query("SELECT s FROM SearchResult s WHERE s.taskName = :taskName AND s.time BETWEEN :startTime AND :endTime")
    List<SearchResult> findByTaskNameAndTimeRange(
        @Param("taskName") String taskName,
        @Param("startTime") String startTime,
        @Param("endTime") String endTime
    );

    /**
     * 根据IP地址查询搜索结果
     * @param ip IP地址
     * @return 搜索结果列表
     */
    List<SearchResult> findByIp(String ip);

    /**
     * 查询指定时间后未关联IP资产的搜索结果
     * @param since 时间阈值
     * @return 搜索结果列表
     */
    @Query("SELECT s FROM SearchResult s WHERE s.ipAssetId IS NULL AND s.ip IS NOT NULL AND s.createdAt >= :since")
    List<SearchResult> findUnlinkedIpAssets(@Param("since") java.time.LocalDateTime since);
}