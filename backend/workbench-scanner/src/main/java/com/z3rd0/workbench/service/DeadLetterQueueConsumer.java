package com.z3rd0.workbench.service;

import com.z3rd0.workbench.config.RabbitMQConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Component
public class DeadLetterQueueConsumer {
    
    private static final Logger logger = LoggerFactory.getLogger(DeadLetterQueueConsumer.class);
    private static final int MAX_RETRY_COUNT = 3; // 最大重试次数
    
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    /**
     * 监听死信队列
     * @param message 失败的消息
     */
    @RabbitListener(queues = RabbitMQConfig.DEAD_LETTER_QUEUE)
    public void receiveFromDeadLetter(Message message) {
        try {
            Map<String, Object> headers = message.getMessageProperties().getHeaders();
            String taskName = getHeaderAsString(headers, "name", "未命名任务");
            int retryCount = getHeaderAsInt(headers, "retryCount", 0);
            String error = getHeaderAsString(headers, "error", "未知错误");
            
            logger.info("从死信队列收到失败任务: {}, 重试次数: {}, 错误: {}", taskName, retryCount, error);
            
            // 检查重试次数是否达到阈值
            if (retryCount < MAX_RETRY_COUNT) {
                // 未达到最大重试次数，重新发送到正常队列
                retryCount++;
                logger.info("任务: {} 将重新发送到正常队列, 重试次数: {}/{}", taskName, retryCount, MAX_RETRY_COUNT);
                
                // 创建新的消息头
                Map<String, Object> newHeaders = new HashMap<>(headers);
                newHeaders.put("retryCount", retryCount);
                newHeaders.put("lastRetryTime", LocalDateTime.now().toString());
                
                // 创建新的消息
                org.springframework.amqp.core.MessageProperties properties = 
                        new org.springframework.amqp.core.MessageProperties();
                properties.setContentType("application/json");
                properties.setHeaders(newHeaders);
                
                // 创建消息体
                String messageBody = message.getBody() != null ? 
                        new String(message.getBody(), StandardCharsets.UTF_8) : "{}";
                
                org.springframework.amqp.core.Message retryMessage = 
                        new org.springframework.amqp.core.Message(
                                messageBody.getBytes(StandardCharsets.UTF_8), properties);
                
                // 重新发送到正常队列
                rabbitTemplate.send(RabbitMQConfig.EXCHANGE_NAME, 
                                   RabbitMQConfig.ROUTING_KEY, 
                                   retryMessage);
                
                logger.info("已重新发送任务到队列: {}, 当前重试次数: {}", taskName, retryCount);
            } else {
                // 如果达到最大重试次数，记录日志但不再处理
                logger.warn("任务: {} 重试次数已达上限 {}，放弃处理", taskName, MAX_RETRY_COUNT);
            }
        } catch (Exception e) {
            logger.error("处理死信队列消息时发生错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 从headers中安全获取字符串值
     */
    private String getHeaderAsString(Map<String, Object> headers, String key, String defaultValue) {
        if (headers != null && headers.containsKey(key)) {
            Object value = headers.get(key);
            return value != null ? value.toString() : defaultValue;
        }
        return defaultValue;
    }
    
    /**
     * 从headers中安全获取整数值
     */
    private int getHeaderAsInt(Map<String, Object> headers, String key, int defaultValue) {
        if (headers != null && headers.containsKey(key)) {
            Object value = headers.get(key);
            if (value != null) {
                try {
                    if (value instanceof Number) {
                        return ((Number) value).intValue();
                    } else {
                        return Integer.parseInt(value.toString());
                    }
                } catch (NumberFormatException e) {
                    // 忽略解析错误
                }
            }
        }
        return defaultValue;
    }
} 