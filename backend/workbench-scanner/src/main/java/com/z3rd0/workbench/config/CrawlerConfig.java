package com.z3rd0.workbench.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 爬虫引擎配置类
 */
@Configuration
@ConfigurationProperties(prefix = "crawler")
public class CrawlerConfig {
    
    private Account account;
    private Task task;
    private Browser browser;
    
    public Account getAccount() {
        return account;
    }
    
    public void setAccount(Account account) {
        this.account = account;
    }
    
    public Task getTask() {
        return task;
    }
    
    public void setTask(Task task) {
        this.task = task;
    }
    
    public Browser getBrowser() {
        return browser;
    }
    
    public void setBrowser(Browser browser) {
        this.browser = browser;
    }
    
    /**
     * 获取连续超出时间范围的数据量阈值
     * @return 配置的阈值，默认为9
     */
    public int getOutOfRangeThreshold() {
        return task != null && task.getOutOfRangeThreshold() != null
               ? task.getOutOfRangeThreshold() : 9;
    }
    
    /**
     * 获取浏览器空闲超时时间
     * @return 配置的超时时间（毫秒），默认为30分钟
     */
    public long getBrowserIdleTimeout() {
        return browser != null && browser.getIdleTimeout() != null 
               ? browser.getIdleTimeout() : 30 * 60 * 1000;
    }
    
    /**
     * 获取浏览器空闲超时时间（分钟）
     * @return 配置的超时时间（分钟），默认为30分钟
     */
    public long getBrowserIdleTimeoutMinutes() {
        return getBrowserIdleTimeout() / (60 * 1000);
    }
    
    /**
     * 获取夜间模式开始时间
     * @return 夜间模式开始小时，默认为0（午夜）
     */
    public int getNightModeStartHour() {
        return browser != null && browser.getNightModeStartHour() != null 
               ? browser.getNightModeStartHour() : 0;
    }
    
    /**
     * 获取夜间模式结束时间
     * @return 夜间模式结束小时，默认为6（早上6点）
     */
    public int getNightModeEndHour() {
        return browser != null && browser.getNightModeEndHour() != null 
               ? browser.getNightModeEndHour() : 6;
    }
    
    /**
     * 获取当前账号最大数据量限制
     * @return 当前配置的账号等级对应的最大数据量
     */
    public int getCurrentAccountLimit() {
        if (account == null) {
            return 500; // 默认使用普通账号限制
        }
        
        String level = account.getLevel();
        Map<String, Integer> limits = account.getLimit();
        
        if ("vip".equalsIgnoreCase(level) && limits.containsKey("vip")) {
            return limits.get("vip");
        } else {
            return limits.getOrDefault("normal", 500);
        }
    }
    
    /**
     * 获取爬虫登录用户名
     * @return 配置的用户名，如未配置则返回null
     */
    public String getUsername() {
        return account != null ? account.getUsername() : null;
    }
    
    /**
     * 获取爬虫登录密码
     * @return 配置的密码，如未配置则返回null
     */
    public String getPassword() {
        return account != null ? account.getPassword() : null;
    }
    
    /**
     * 获取当前账号等级对应的设备保留阈值
     * @return 配置的设备阈值，VIP账号默认为4，普通账号默认为1
     * @deprecated 此方法已弃用，系统现在使用PlaywrightUtils中的静态阈值。保留此方法仅为向后兼容。
     */
    @Deprecated
    public int getDeviceThreshold() {
        if (account == null) {
            return 1; // 默认使用普通账号阈值
        }
        
        String level = account.getLevel();
        Map<String, Integer> thresholds = account.getDeviceThreshold();
        
        if (thresholds == null) {
            // 默认阈值
            return "vip".equalsIgnoreCase(level) ? 4 : 1;
        }
        
        if ("vip".equalsIgnoreCase(level) && thresholds.containsKey("vip")) {
            return thresholds.get("vip");
        } else {
            return thresholds.getOrDefault("normal", 1);
        }
    }
    
    /**
     * 账号配置
     */
    public static class Account {
        private String level;
        private Map<String, Integer> limit;
        private String username;
        private String password;
        private Map<String, Integer> deviceThreshold;
        
        public String getLevel() {
            return level;
        }
        
        public void setLevel(String level) {
            this.level = level;
        }
        
        public Map<String, Integer> getLimit() {
            return limit;
        }
        
        public void setLimit(Map<String, Integer> limit) {
            this.limit = limit;
        }
        
        public String getUsername() {
            return username;
        }
        
        public void setUsername(String username) {
            this.username = username;
        }
        
        public String getPassword() {
            return password;
        }
        
        public void setPassword(String password) {
            this.password = password;
        }
        
        public Map<String, Integer> getDeviceThreshold() {
            return deviceThreshold;
        }
        
        public void setDeviceThreshold(Map<String, Integer> deviceThreshold) {
            this.deviceThreshold = deviceThreshold;
        }
    }
    
    /**
     * 任务处理配置
     */
    public static class Task {
        private Integer outOfRangeThreshold;
        
        public Integer getOutOfRangeThreshold() {
            return outOfRangeThreshold;
        }
        
        public void setOutOfRangeThreshold(Integer outOfRangeThreshold) {
            this.outOfRangeThreshold = outOfRangeThreshold;
        }
    }
    
    /**
     * 浏览器配置
     */
    public static class Browser {
        private Long idleTimeout;
        private Integer nightModeStartHour;
        private Integer nightModeEndHour;
        
        public Long getIdleTimeout() {
            return idleTimeout;
        }
        
        public void setIdleTimeout(Long idleTimeout) {
            this.idleTimeout = idleTimeout;
        }
        
        public Integer getNightModeStartHour() {
            return nightModeStartHour;
        }
        
        public void setNightModeStartHour(Integer nightModeStartHour) {
            if (nightModeStartHour != null && (nightModeStartHour < 0 || nightModeStartHour > 23)) {
                throw new IllegalArgumentException("夜间模式开始时间必须在0-23之间，当前值：" + nightModeStartHour);
            }
            this.nightModeStartHour = nightModeStartHour;
        }
        
        public Integer getNightModeEndHour() {
            return nightModeEndHour;
        }
        
        public void setNightModeEndHour(Integer nightModeEndHour) {
            if (nightModeEndHour != null && (nightModeEndHour < 0 || nightModeEndHour > 23)) {
                throw new IllegalArgumentException("夜间模式结束时间必须在0-23之间，当前值：" + nightModeEndHour);
            }
            this.nightModeEndHour = nightModeEndHour;
        }
    }
} 