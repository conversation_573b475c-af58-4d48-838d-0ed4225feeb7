package com.z3rd0.workbench.utils;

import com.z3rd0.workbench.service.CookieService;
import com.z3rd0.workbench.service.ResourceMonitor;
import com.z3rd0.workbench.config.CrawlerConfig;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 浏览器管理器 - 确保全局只有一个浏览器实例
 */
@Component
public class BrowserManager {
    private static final Logger logger = LoggerFactory.getLogger(BrowserManager.class);
    
    // 单例的PlaywrightUtils实例
    private static PlaywrightUtils playwrightUtils;
    
    // 注入CookieService
    @Autowired
    private CookieService cookieService;
    
    // 注入配置类
    @Autowired
    private CrawlerConfig crawlerConfig;

    // 注入资源监控器
    @Autowired
    private ResourceMonitor resourceMonitor;

    // 静态引用
    private static CookieService staticCookieService;
    private static ResourceMonitor staticResourceMonitor;
    
    // 浏览器状态标志
    private static final AtomicBoolean browserInitialized = new AtomicBoolean(false);
    
    // 标记是否正在处理任务
    private static final AtomicBoolean processingTask = new AtomicBoolean(false);
    
    // 浏览器初始化和关闭的互斥锁
    private static final ReentrantLock browserLock = new ReentrantLock();
    
    /**
     * 应用启动时检查是否已存在浏览器实例
     */
    @PostConstruct
    public void init() {
        logger.info("浏览器管理器初始化");
        // 将服务设置到静态引用以便在非Spring上下文中使用
        staticCookieService = cookieService;
        staticResourceMonitor = resourceMonitor;

        if (staticCookieService != null) {
            logger.info("已将CookieService设置到静态引用");
            // 同时设置到CookieManager
            CookieManager.setCookieService(staticCookieService);
        } else {
            logger.warn("CookieService注入为null，无法设置静态引用");
        }

        if (staticResourceMonitor != null) {
            logger.info("已将ResourceMonitor设置到静态引用");
        } else {
            logger.warn("ResourceMonitor注入为null，无法设置静态引用");
        }
        
        // 设置静态账号凭据并验证配置完整性
        if (crawlerConfig != null && crawlerConfig.getAccount() != null) {
            String username = crawlerConfig.getUsername();
            String password = crawlerConfig.getPassword();

            // 验证配置完整性
            if (username == null || username.trim().isEmpty()) {
                String errorMsg = "配置错误：crawler.account.username 未配置或为空，请检查application.yml配置文件";
                logger.error(errorMsg);
                throw new IllegalStateException(errorMsg);
            }

            if (password == null || password.trim().isEmpty()) {
                String errorMsg = "配置错误：crawler.account.password 未配置或为空，请检查application.yml配置文件";
                logger.error(errorMsg);
                throw new IllegalStateException(errorMsg);
            }

            // 同时设置两个类的静态凭据，确保配置同步
            BrowserConfigManager.setStaticCredentials(username.trim(), password.trim());
            PlaywrightUtils.setStaticCredentials(username.trim(), password.trim());
            logger.info("已从配置文件设置静态账号凭据到BrowserConfigManager和PlaywrightUtils，用户名: {}", username.trim());
        } else {
            String errorMsg = "配置错误：crawler.account 配置节点不存在，请检查application.yml配置文件";
            logger.error(errorMsg);
            throw new IllegalStateException(errorMsg);
        }
        
        // 设置设备阈值静态配置
        loadDeviceThresholds();
        
        // 检查是否已存在浏览器实例，如果有则关闭
        if (browserInitialized.get() && playwrightUtils != null) {
            logger.warn("检测到已存在的浏览器实例，尝试关闭");
            closeBrowser();
        }
    }
    
    /**
     * 应用关闭时确保浏览器关闭
     */
    @PreDestroy
    public void cleanup() {
        logger.info("应用关闭，清理浏览器资源");
        closeBrowser();
    }
    
    /**
     * 获取浏览器实例 - 如果不存在则创建
     * @return PlaywrightUtils实例
     */
    public PlaywrightUtils getBrowser() {
        // 如果浏览器已初始化，直接返回
        if (browserInitialized.get() && playwrightUtils != null) {
            // 检查浏览器连接是否正常
            try {
                if (playwrightUtils.isInitialized() && playwrightUtils.isConnected()) {
                    logger.debug("复用现有浏览器实例");
                    return playwrightUtils;
                } else {
                    logger.warn("浏览器实例已存在但状态异常，将重新初始化");
                    // 继续执行创建逻辑
                }
            } catch (Exception e) {
                logger.error("检查浏览器状态出错: {}", e.getMessage());
                // 继续执行创建逻辑
            }
        }
        
        // 尝试获取锁，避免多线程同时初始化
        boolean lockAcquired = false;
        try {
            lockAcquired = browserLock.tryLock();
            if (!lockAcquired) {
                logger.info("浏览器正在由其他线程初始化，等待...");
                browserLock.lock();
                lockAcquired = true;
                
                // 再次检查是否已初始化（可能在获取锁期间已被其他线程初始化）
                if (browserInitialized.get() && playwrightUtils != null && 
                    playwrightUtils.isInitialized() && playwrightUtils.isConnected()) {
                    return playwrightUtils;
                }
            }
            
            logger.info("开始初始化浏览器...");
            // 先检查全局是否已有实例并尝试关闭
            if (playwrightUtils != null) {
                try {
                    logger.warn("检测到旧的浏览器实例，尝试关闭");
                    playwrightUtils.close();
                } catch (Exception e) {
                    logger.error("关闭旧浏览器实例失败: {}", e.getMessage());
                }
            }
            
            // 创建新实例 - 注意这里不会自动初始化浏览器
            playwrightUtils = new PlaywrightUtils();
            
            // 确保设置CookieService
            if (staticCookieService != null) {
                CookieManager.setCookieService(staticCookieService);
                logger.info("已将静态CookieService设置到CookieManager");
            } else if (cookieService != null) {
                CookieManager.setCookieService(cookieService);
                staticCookieService = cookieService;
                logger.info("已将实例CookieService设置到CookieManager");
            } else {
                logger.warn("无法设置CookieService到CookieManager，cookie功能可能不可用");
            }
            
            // 显式调用初始化和登录
            playwrightUtils.initialize();
            boolean loginSuccess = playwrightUtils.login();

            if (!loginSuccess) {
                logger.error("浏览器登录失败，无法继续操作");
                throw new RuntimeException("浏览器登录失败");
            }

            // 注册浏览器到资源监控器
            if (staticResourceMonitor != null) {
                String browserId = "browser-" + System.currentTimeMillis();
                staticResourceMonitor.registerBrowser(browserId, "system");
                logger.info("浏览器已注册到资源监控器: {}", browserId);
            }

            browserInitialized.set(true);
            logger.info("浏览器初始化并登录成功");
            
            return playwrightUtils;
        } catch (Exception e) {
            logger.error("浏览器初始化失败: {}", e.getMessage(), e);
            browserInitialized.set(false);
            if (playwrightUtils != null) {
                try {
                    playwrightUtils.close();
                } catch (Exception ex) {
                    logger.error("关闭失败的浏览器实例出错: {}", ex.getMessage());
                }
                playwrightUtils = null;
            }
            throw new RuntimeException("浏览器初始化失败", e);
        } finally {
            if (lockAcquired) {
                browserLock.unlock();
            }
        }
    }
    
    /**
     * 关闭浏览器
     */
    public void closeBrowser() {
        // 使用锁确保安全关闭
        browserLock.lock();
        try {
            if (browserInitialized.get() && playwrightUtils != null) {
                if (processingTask.get()) {
                    logger.info("浏览器正在处理任务中，暂不关闭");
                    return;
                }
                
                logger.info("正在关闭浏览器...");
                try {
                    playwrightUtils.close();
                } catch (Exception e) {
                    logger.error("关闭浏览器出错: {}", e.getMessage(), e);
                } finally {
                    browserInitialized.set(false);
                    playwrightUtils = null;
                    logger.info("浏览器已关闭");
                }
            } else {
                logger.info("浏览器未初始化，跳过关闭操作");
            }
        } finally {
            browserLock.unlock();
        }
    }
    
    /**
     * 标记任务处理开始
     */
    public synchronized boolean markTaskProcessing() {
        // 如果浏览器未初始化或已有任务在处理中，则不能开始新任务
        if (!isBrowserInitialized() || processingTask.get()) {
            logger.warn("无法标记任务处理：浏览器未初始化或已有任务处理中. 浏览器状态: {}", getBrowserStatus());
            return false;
        }
        processingTask.set(true);
        logger.info("标记任务开始处理, 浏览器状态: {}", getBrowserStatus());
        return true;
    }
    
    /**
     * 标记任务处理完成
     */
    public synchronized void markTaskCompleted() {
        processingTask.set(false);
        logger.info("标记任务处理完成, 浏览器状态: {}", getBrowserStatus());
    }
    
    /**
     * 检查浏览器是否已初始化
     */
    public boolean isBrowserInitialized() {
        return browserInitialized.get() && playwrightUtils != null && 
               playwrightUtils.isInitialized();
    }
    
    /**
     * 检查是否正在处理任务
     */
    public boolean isProcessingTask() {
        return processingTask.get();
    }
    
    /**
     * 获取浏览器状态
     */
    public String getBrowserStatus() {
        if (processingTask.get()) {
            return "BUSY"; // 如果正在处理任务，优先返回BUSY状态
        } else if (!browserInitialized.get() || playwrightUtils == null) {
            return "CLOSED";
        } else {
            return "IDLE";
        }
    }
    
    /**
     * 设置任务处理状态（仅供内部和调试使用）
     */
    public synchronized void setProcessingTask(boolean isProcessing) {
        if (isProcessing != processingTask.get()) {
            processingTask.set(isProcessing);
            logger.info("手动设置任务处理状态为: {}, 当前浏览器状态: {}", isProcessing, getBrowserStatus());
        }
    }
    
    /**
     * 重置浏览器状态（出现错误时）
     */
    public void resetBrowserState() {
        closeBrowser();
    }
    
    /**
     * 设置静态设备阈值（不再从配置文件读取）
     */
    private void loadDeviceThresholds() {
        try {
            // 直接使用静态定义的阈值
            int normalThreshold = 1; // 普通账号固定使用1个设备
            int vipThreshold = 4;    // VIP账号固定使用4个设备
            
            // 设置到BrowserConfigManager
            BrowserConfigManager.setStaticDeviceThresholds(normalThreshold, vipThreshold);
            logger.info("已设置静态设备阈值 - 普通账号: {}, VIP账号: {}", normalThreshold, vipThreshold);
        } catch (Exception e) {
            logger.error("设置设备阈值出错: {}", e.getMessage(), e);
            BrowserConfigManager.setStaticDeviceThresholds(1, 4); // 使用默认值
        }
    }
} 