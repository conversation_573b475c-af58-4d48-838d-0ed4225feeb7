package com.z3rd0.workbench.service;

import com.z3rd0.workbench.utils.BrowserManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 资源监控调度器
 * 定期检查系统资源使用情况，并在必要时采取保护措施
 */
@Service
public class ResourceMonitorScheduler {
    
    private static final Logger logger = LoggerFactory.getLogger(ResourceMonitorScheduler.class);
    
    @Autowired
    private ResourceMonitor resourceMonitor;
    
    @Autowired
    private BrowserManager browserManager;
    
    /**
     * 每5分钟检查一次内存使用情况
     */
    @Scheduled(fixedRate = 300000) // 5分钟 = 300000毫秒
    public void checkMemoryUsage() {
        try {
            logger.debug("执行定时内存检查...");
            ResourceMonitor.MemoryMonitorResult result = resourceMonitor.checkMemoryUsage();
            
            if (result.getStatus() == ResourceMonitor.MemoryStatus.CRITICAL) {
                logger.warn("内存使用严重超限: 堆内存使用率={:.2f}%, 建议={}",
                           result.getHeapUsageRatio() * 100, result.getRecommendation());
                
                // 执行紧急垃圾回收
                resourceMonitor.performGarbageCollection();
                
                // 如果内存仍然严重不足，重启浏览器
                if (resourceMonitor.checkMemoryUsage().getStatus() == ResourceMonitor.MemoryStatus.CRITICAL) {
                    logger.warn("垃圾回收后内存仍然严重不足，尝试重启浏览器");
                    restartBrowser();
                }
            } else if (result.getStatus() == ResourceMonitor.MemoryStatus.WARNING) {
                logger.warn("内存使用警告: 堆内存使用率={:.2f}%, 建议={}",
                           result.getHeapUsageRatio() * 100, result.getRecommendation());
                
                // 执行垃圾回收
                resourceMonitor.performGarbageCollection();
            }
        } catch (Exception e) {
            logger.error("内存检查任务异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 每15分钟检查一次浏览器资源使用情况
     */
    @Scheduled(fixedRate = 900000) // 15分钟 = 900000毫秒
    public void checkBrowserResources() {
        try {
            logger.debug("执行定时浏览器资源检查...");
            ResourceMonitor.BrowserMonitorResult result = resourceMonitor.checkBrowserResources();
            
            if (result.hasRestartNeeded()) {
                logger.warn("检测到需要重启的浏览器: 数量={}", result.getRestartBrowsers().size());
                
                for (ResourceMonitor.BrowserMonitorResult.BrowserRestartInfo info : result.getRestartBrowsers()) {
                    logger.warn("浏览器需要重启: ID={}, 任务={}, 原因={}",
                               info.getBrowserId(), info.getTaskName(), info.getReason());
                }
                
                // 重启浏览器
                restartBrowser();
            }
        } catch (Exception e) {
            logger.error("浏览器资源检查任务异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 重启浏览器
     */
    private void restartBrowser() {
        try {
            logger.info("开始重启浏览器...");
            
            // 关闭浏览器
            browserManager.closeBrowser();
            
            // 执行垃圾回收
            resourceMonitor.performGarbageCollection();
            
            // 等待一段时间
            try {
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            // 重新初始化浏览器
            browserManager.getBrowser();
            
            logger.info("浏览器重启完成");
        } catch (Exception e) {
            logger.error("浏览器重启失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 每天凌晨3点执行一次计划性浏览器重启
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void scheduledBrowserRestart() {
        try {
            logger.info("执行计划性浏览器重启...");
            restartBrowser();
        } catch (Exception e) {
            logger.error("计划性浏览器重启失败: {}", e.getMessage(), e);
        }
    }
}
