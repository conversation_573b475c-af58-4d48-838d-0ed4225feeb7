package com.z3rd0.workbench.event;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 任务事件发布器
 * 负责发布各种任务事件
 */
@Component
public class TaskEventPublisher {
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    /**
     * 发布任务进度更新事件
     */
    public void publishProgressUpdate(String taskName, String progress, int processedItems) {
        eventPublisher.publishEvent(new TaskProgressEvent(taskName, progress, processedItems));
    }
    
    /**
     * 发布数据统计更新事件
     */
    public void publishDataStats(String taskName, int itemsInRange, int itemsOutOfRange, int duplicateItems) {
        eventPublisher.publishEvent(new TaskDataStatsEvent(taskName, itemsInRange, itemsOutOfRange, duplicateItems));
    }
    
    /**
     * 发布任务日志事件
     */
    public void publishLog(String taskName, String message, String type) {
        eventPublisher.publishEvent(new TaskLogEvent(taskName, message, type));
    }
    
    /**
     * 发布信息类型日志事件
     */
    public void publishInfoLog(String taskName, String message) {
        publishLog(taskName, message, "info");
    }
    
    /**
     * 发布成功类型日志事件
     */
    public void publishSuccessLog(String taskName, String message) {
        publishLog(taskName, message, "success");
    }
    
    /**
     * 发布警告类型日志事件
     */
    public void publishWarningLog(String taskName, String message) {
        publishLog(taskName, message, "warning");
    }
    
    /**
     * 发布错误类型日志事件
     */
    public void publishErrorLog(String taskName, String message) {
        publishLog(taskName, message, "error");
    }
} 