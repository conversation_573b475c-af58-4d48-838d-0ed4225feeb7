package com.z3rd0.workbench.service;

import com.z3rd0.workbench.event.TaskEventPublisher;
import com.z3rd0.workbench.exception.TaskProcessingException;
import com.z3rd0.common.model.SearchResult;
import com.z3rd0.workbench.repository.SearchResultRepository;
import com.z3rd0.workbench.utils.SingleBrowserManager;
import com.z3rd0.workbench.utils.PlaywrightUtils;
import com.z3rd0.workbench.config.CrawlerConfig;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAccessor;
import java.time.Instant;
import org.json.JSONObject;

@Service
public class TaskService {
    private static final Logger logger = LoggerFactory.getLogger(TaskService.class);
    
    @Autowired
    private SearchResultRepository searchResultRepository;
    
    @Autowired
    private SingleBrowserManager singleBrowserManager;
    
    @Autowired
    private CrawlerConfig crawlerConfig;
    
    @Autowired
    private TaskEventPublisher eventPublisher;
    
    @Autowired
    private TaskStateManager taskStateManager;

    // 移除自动注入的PlaywrightUtils，改为使用SingleBrowserManager管理的实例
    // @Autowired
    // private PlaywrightUtils playwrightUtils;

    // 记录最后一次任务处理时间
    private LocalDateTime lastTaskProcessTime;
    
    /**
     * 构造函数 - 初始化最后任务处理时间为当前时间
     */
    public TaskService() {
        this.lastTaskProcessTime = LocalDateTime.now();
        logger.info("TaskService初始化，设置初始最后任务处理时间：{}", lastTaskProcessTime);
    }
    
    // 浏览器空闲时间阈值（毫秒）- 从配置文件中获取，不再使用固定常量
    
    @PreDestroy
    public void cleanup() {
        // 清理工作在BrowserPool中已实现
    }
    
    /**
     * 处理搜索任务
     * @param searchRule 搜索规则
     * @param timeRange 时间范围 (格式: "2023-01-01 00:00:00,2023-12-31 23:59:59")
     * @param taskName 任务名称，用于保存结果
     * @throws TaskProcessingException 任务处理过程中的异常
     */
    public void processSearchTask(String searchRule, String timeRange, String taskName) throws TaskProcessingException {
        logger.info("开始处理搜索任务, 规则: {}, 时间范围: {}, 任务名称: {}", searchRule, timeRange, taskName);
        
        // 更新最后一次任务处理时间
        lastTaskProcessTime = LocalDateTime.now();
        
        // 先检查是否有任务名称，如果没有则设置默认名称
        if (taskName == null || taskName.isEmpty()) {
            taskName = "搜索任务_" + System.currentTimeMillis();
            logger.info("未提供任务名称，使用默认名称: {}", taskName);
        }
        
        // 更新任务进度和日志
        eventPublisher.publishProgressUpdate(taskName, "准备任务中", 0);
        eventPublisher.publishInfoLog(taskName, "开始处理任务: " + taskName);
        eventPublisher.publishInfoLog(taskName, "搜索规则: " + searchRule);
        eventPublisher.publishInfoLog(taskName, "时间范围: " + timeRange);
        
        // 清理已完成的任务状态，确保状态管理器中只保留活跃任务
        taskStateManager.cleanupCompletedTasks();
        logger.info("已清理完成任务状态，开始新任务: {}", taskName);

        // 设置任务开始时间
        TaskStateManager.TaskState state = taskStateManager.getOrCreateTaskState(taskName);
        state.setStartTime(LocalDateTime.now());
        
        // 获取账号数据量限制
        int dataLimit = crawlerConfig.getCurrentAccountLimit();
        eventPublisher.publishInfoLog(taskName, "当前账号等级限制: 最多处理 " + dataLimit + " 条数据");
        logger.info("当前账号数据量限制: {}", dataLimit);
        
        // 记录配置参数
        logger.info("当前任务配置 - 账号数据量限制: {}, 连续超时阈值: {}, 浏览器空闲超时: {}毫秒", 
                  dataLimit, crawlerConfig.getOutOfRangeThreshold(), crawlerConfig.getBrowserIdleTimeout());
        eventPublisher.publishInfoLog(taskName, String.format(
            "配置信息 - 账号限制: %d条数据, 连续超时阈值: %d条", 
            dataLimit, crawlerConfig.getOutOfRangeThreshold()));
        
        // 使用try-with-resources确保浏览器资源释放
        try (SingleBrowserManager.BrowserResource browserResource = singleBrowserManager.acquireBrowser()) {
            
            // 获取浏览器实例
            eventPublisher.publishInfoLog(taskName, "初始化浏览器...");
            PlaywrightUtils playwrightUtils = browserResource.getBrowser();
            
            if (playwrightUtils == null) {
                throw TaskProcessingException.of(taskName, TaskProcessingException.ErrorType.BROWSER_ERROR, "无法获取浏览器实例");
            }
            
            eventPublisher.publishSuccessLog(taskName, "浏览器初始化完成");
            
            // 执行搜索
            logger.info("执行搜索，规则: {}", searchRule);
            eventPublisher.publishInfoLog(taskName, "开始执行搜索: " + searchRule);
            
            try {
            List<Map<String, String>> results = playwrightUtils.search(searchRule);
            
            // 保存结果并初始化不在时间范围内的数据计数
            int outOfRangeCount = 0;
            int inRangeCount = 0;
            int duplicateCount = 0;
            
            // 保存第一页数据
            if (results.isEmpty()) {
                    eventPublisher.publishWarningLog(taskName, "未找到匹配的结果");
            } else {
                // 估计总页数 (假设每页10-20条数据)
                int estimatedTotalPages = 1; // 默认至少1页
                
                // 更新任务状态
                    eventPublisher.publishProgressUpdate(taskName, "获取到第1页数据，准备处理", results.size());
                    state.updatePageInfo(1, estimatedTotalPages);
                
                // 保存第一页结果
                outOfRangeCount = saveSearchResults(results, timeRange, taskName, searchRule, inRangeCount, outOfRangeCount, duplicateCount);
                
                    // 获取最新统计值
                    inRangeCount = state.getItemsInRange();
                    duplicateCount = state.getDuplicateItems();
                
                // 检查是否需要提前结束任务（连续多个超出时间范围）
                boolean hasNextPage = true;
                if (outOfRangeCount == -1) {
                        eventPublisher.publishWarningLog(taskName, "由于连续数据超出时间范围，提前结束任务");
                    // 将outOfRangeCount重置为正常值，以便正确计算统计
                        outOfRangeCount = state.getItemsOutOfRange();
                    hasNextPage = false; // 不再继续翻页
                } else {
                        outOfRangeCount = state.getItemsOutOfRange();
                }
                
                // 初始化翻页相关变量
                int pageNum = 1;
                int consecutiveEmptyPages = 0; // 连续空页计数
                int maxConsecutiveEmptyPages = 3; // 最大连续空页数

                // 更新处理完第一页后的状态
                int totalProcessed = inRangeCount + outOfRangeCount + duplicateCount;
                eventPublisher.publishProgressUpdate(taskName, "已处理第1页数据", totalProcessed);
                eventPublisher.publishSuccessLog(taskName, "第1页处理完成，共" + results.size() + "条数据");

                // 检查第一页是否为空
                if (results.isEmpty()) {
                    consecutiveEmptyPages++;
                    eventPublisher.publishWarningLog(taskName, "第1页无数据，连续空页计数: " + consecutiveEmptyPages);
                } else {
                    consecutiveEmptyPages = 0; // 重置计数
                }

                // 检查是否达到数据量限制
                if (totalProcessed >= dataLimit) {
                    eventPublisher.publishWarningLog(taskName, "已达到账号处理数据量限制 (" + dataLimit + " 条)，任务将结束");
                    logger.info("已达到账号数据量限制: {}，停止处理", dataLimit);
                    hasNextPage = false;
                } else {
                    // 重新检查是否有下一页（使用增强的检测逻辑）
                    logger.info("第1页处理完成，开始检查是否有下一页...");
                    hasNextPage = playwrightUtils.hasNextPage();
                    logger.info("第1页处理完成后翻页状态检查结果: {}", hasNextPage);

                    if (!hasNextPage) {
                        eventPublisher.publishInfoLog(taskName, "检测到搜索结果只有一页，任务完成");
                        logger.info("搜索结果只有一页，无需翻页，任务完成");
                    }
                }
                
                // 翻页处理 - 增强版循环逻辑
                while (hasNextPage) {
                    pageNum++;

                    // 更新状态为准备翻页
                    eventPublisher.publishProgressUpdate(taskName, "正在翻到第" + pageNum + "页", totalProcessed);
                    eventPublisher.publishInfoLog(taskName, "正在翻到第" + pageNum + "页...");
                    logger.info("开始处理第{}页，当前总页数估计: {}, 连续空页计数: {}", pageNum, estimatedTotalPages, consecutiveEmptyPages);

                    try {
                        // 翻页前状态检查和准备
                        if (!prepareForPagination(playwrightUtils, taskName, pageNum)) {
                            break;
                        }

                        // 执行翻页操作（带重试机制）
                        results = executePageTurnWithRetry(playwrightUtils, taskName, pageNum);

                        // 验证翻页结果
                        if (!validatePageTurnResult(results, taskName, pageNum)) {
                            break;
                        }

                        // 更新翻页成功状态
                        eventPublisher.publishProgressUpdate(taskName, "获取到第" + pageNum + "页数据，准备处理", totalProcessed);
                        logger.info("第{}页翻页成功，获取到{}条数据", pageNum, results.size());

                        // 检查是否为空页
                        if (results.isEmpty()) {
                            consecutiveEmptyPages++;
                            eventPublisher.publishWarningLog(taskName, "第" + pageNum + "页无数据，连续空页计数: " + consecutiveEmptyPages);

                            // 如果连续多页无数据，提前结束
                            if (consecutiveEmptyPages >= maxConsecutiveEmptyPages) {
                                logger.warn("连续{}页无数据，提前结束翻页", consecutiveEmptyPages);
                                eventPublisher.publishWarningLog(taskName, "连续" + consecutiveEmptyPages + "页无数据，提前结束任务");
                                break;
                            }
                        } else {
                            consecutiveEmptyPages = 0; // 重置连续空页计数
                        }

                        // 从响应中获取总记录数和每页大小
                        String responseText = playwrightUtils.getLastResponseText();
                        if (responseText != null) {
                            try {
                                JSONObject jsonResponse = new JSONObject(responseText);
                                if (jsonResponse.has("meta") && jsonResponse.getJSONObject("meta").has("pagination")) {
                                    JSONObject pagination = jsonResponse.getJSONObject("meta").getJSONObject("pagination");
                                    int total = pagination.getInt("total");
                                    int pageSize = pagination.getInt("page_size");
                                    int currentPage = pagination.optInt("current_page", pageNum);
                                    estimatedTotalPages = (int) Math.ceil((double) total / pageSize);

                                    logger.info("分页信息更新 - 当前页: {}, 总页数: {}, 总记录: {}, 每页大小: {}",
                                               currentPage, estimatedTotalPages, total, pageSize);

                                    // 如果当前页已经是最后一页，标记无下一页
                                    if (currentPage >= estimatedTotalPages) {
                                        logger.info("根据分页信息判断已到达最后一页: {}/{}", currentPage, estimatedTotalPages);
                                        hasNextPage = false;
                                    }
                                }
                            } catch (Exception e) {
                                logger.warn("解析分页信息失败: {}", e.getMessage());
                            }
                        }

                        state.updatePageInfo(pageNum, estimatedTotalPages);
                        
                        // 保存本页结果
                        outOfRangeCount = saveSearchResults(results, timeRange, taskName, searchRule, inRangeCount, outOfRangeCount, duplicateCount);
                        
                        // 重新获取累计统计值，确保不会丢失
                            inRangeCount = state.getItemsInRange();
                            duplicateCount = state.getDuplicateItems();
                        if (outOfRangeCount != -1) {
                                outOfRangeCount = state.getItemsOutOfRange();
                        }
                        
                        // 检查是否需要提前结束任务（连续多个超出时间范围）
                        if (outOfRangeCount == -1) {
                            eventPublisher.publishWarningLog(taskName, "由于连续数据超出时间范围，提前结束任务");
                            // 将outOfRangeCount重置为正常值，以便正确计算统计
                            outOfRangeCount = state.getItemsOutOfRange();
                            hasNextPage = false; // 不再继续翻页
                            break; // 结束翻页循环
                        }

                        // 更新任务状态
                        totalProcessed = inRangeCount + outOfRangeCount + duplicateCount;
                        eventPublisher.publishProgressUpdate(taskName, "已处理第" + pageNum + "页数据", totalProcessed);
                        eventPublisher.publishSuccessLog(taskName, "第" + pageNum + "页处理完成，共" + results.size() + "条数据");

                        // 检查是否达到数据量限制
                        if (totalProcessed >= dataLimit) {
                            eventPublisher.publishWarningLog(taskName, "已达到账号处理数据量限制 (" + dataLimit + " 条)，任务将结束");
                            logger.info("已达到账号数据量限制: {}，停止处理", dataLimit);
                            hasNextPage = false;
                            break; // 结束翻页循环
                        }

                        // 在循环末尾检查是否有下一页（如果之前没有被设置为false）
                        if (hasNextPage) {
                            hasNextPage = playwrightUtils.hasNextPage();
                            logger.info("第{}页处理完成后检查翻页状态: {}", pageNum, hasNextPage);

                            // 如果没有下一页，记录详细信息
                            if (!hasNextPage) {
                                logger.info("检测到已到达最后一页，准备结束翻页循环");
                                eventPublisher.publishInfoLog(taskName, "已到达最后一页，翻页结束");
                            }
                        }

                    } catch (Exception e) {
                        logger.error("翻页处理异常: {}", e.getMessage(), e);
                        eventPublisher.publishErrorLog(taskName, "第" + pageNum + "页处理异常: " + e.getMessage());

                        // 判断异常类型，决定是否继续
                        if (e.getMessage() != null &&
                            (e.getMessage().contains("已到达最后一页") ||
                             e.getMessage().contains("下一页按钮") ||
                             e.getMessage().contains("翻页操作超时"))) {
                            logger.info("翻页异常表明已到达最后一页，正常结束");
                            eventPublisher.publishInfoLog(taskName, "翻页结束：" + e.getMessage());
                        } else {
                            eventPublisher.publishErrorLog(taskName, "翻页异常，停止处理：" + e.getMessage());
                        }
                        break; // 出现异常停止翻页
                    }
                }
                
                // 任务完成 - 详细统计信息
                int finalTotalProcessed = inRangeCount + outOfRangeCount + duplicateCount;
                logger.info("搜索任务完成 - 总页数: {}, 总处理: {}, 有效: {}, 超出范围: {}, 重复: {}, 连续空页: {}",
                           pageNum, finalTotalProcessed, inRangeCount, outOfRangeCount, duplicateCount, consecutiveEmptyPages);
                eventPublisher.publishSuccessLog(taskName, String.format(
                    "搜索任务完成，共获取 %d 页数据，处理 %d 条记录（有效: %d, 超出范围: %d, 重复: %d）",
                    pageNum, finalTotalProcessed, inRangeCount, outOfRangeCount, duplicateCount));

                // 记录翻页性能统计
                logPaginationPerformanceStats(taskName);
            }
        } catch (Exception e) {
            logger.error("处理搜索任务异常: {}", e.getMessage(), e);
                eventPublisher.publishErrorLog(taskName, "处理搜索任务异常: " + e.getMessage());
            
                // 如果是浏览器问题，转换为特定异常
            if (e.getMessage() != null && (
                        e.getMessage().contains("browser has been closed") || 
                        e.getMessage().contains("Target page, context or browser has been") || 
                        e.getMessage().contains("Browser closed") || 
                    e.getMessage().contains("closed"))) {
                    logger.info("检测到浏览器问题");
                    eventPublisher.publishWarningLog(taskName, "检测到浏览器问题，将重新初始化浏览器");
                    throw TaskProcessingException.of(taskName, TaskProcessingException.ErrorType.BROWSER_ERROR, e);
            }
            
                throw TaskProcessingException.of(taskName, TaskProcessingException.ErrorType.UNKNOWN_ERROR, e);
        } finally {
            // 更新任务完成时间
            lastTaskProcessTime = LocalDateTime.now();

            try {
                // 获取任务状态并标记为完成
                TaskStateManager.TaskState finalState = taskStateManager.getOrCreateTaskState(taskName);
                finalState.setCompleted(true);
                logger.info("任务已标记为完成: {}", taskName);

                // 完成最终进度更新
                eventPublisher.publishProgressUpdate(taskName, "任务完成", finalState.getProcessedItems());
            } catch (Exception e) {
                logger.error("标记任务完成状态失败: {}, 错误: {}", taskName, e.getMessage());
                // 即使状态更新失败，也要发布基本的完成事件
                try {
                    eventPublisher.publishProgressUpdate(taskName, "任务完成", 0);
                } catch (Exception ex) {
                    logger.error("发布任务完成事件失败: {}", ex.getMessage());
                }
            }
        }
        }
    }
    
    /**
     * 保存搜索结果
     * @param results 搜索结果列表
     * @param timeRange 时间范围
     * @param taskName 任务名称
     * @param searchRule 搜索规则
     * @param inRangeCount 之前统计的范围内数据数量
     * @param outOfRangeCount 之前统计的范围外数据数量
     * @param duplicateCount 之前统计的重复数据数量
     * @return 更新后的范围外数据数量，如果返回-1表示需要中断处理
     */
    private int saveSearchResults(List<Map<String, String>> results, String timeRange, String taskName, 
                                 String searchRule, int inRangeCount, int outOfRangeCount, int duplicateCount) {
        
        // 任务状态
        TaskStateManager.TaskState state = taskStateManager.getOrCreateTaskState(taskName);
        
        // 添加时间范围详细日志
        logger.info("开始处理批次数据 - 时间范围: {}, 批次数量: {}", timeRange, results.size());
        if (timeRange != null && timeRange.contains(",")) {
            String[] parts = timeRange.split(",");
            if (parts.length == 2) {
                logger.info("解析时间范围 - 开始时间: {}, 结束时间: {}", parts[0].trim(), parts[1].trim());
            }
        }
        
        // 更新任务统计和日志
        eventPublisher.publishInfoLog(taskName, "开始保存数据, 本批数量: " + results.size() + "条");
        
        // 存储当前批次处理结果的统计信息
        int batchInRange = 0;
        int batchOutOfRange = 0;
        int batchDuplicate = 0;
        
        // 用于判断是否需要提前终止（连续超出范围的数据达到阈值）
        int consecutiveOutOfRange = 0;
        
        // 添加时间范围标记，确保外部程序可以直接读取
        for (Map<String, String> resultMap : results) {
            try {
                // 调试: 记录resultMap中所有键
                StringBuilder keysInfo = new StringBuilder("数据项可用字段: ");
                for (String key : resultMap.keySet()) {
                    keysInfo.append(key).append("=").append(resultMap.get(key) != null ? "有值" : "null").append(", ");
                }
                logger.debug(keysInfo.toString());
                
                // 获取原始创建时间
                String createdTime = resultMap.get("createdTime");
                
                // 时间字段降级查找 - 如果createdTime为空，尝试从time字段获取
                if (createdTime == null || createdTime.trim().isEmpty()) {
                    String timeField = resultMap.get("time");
                    if (timeField != null && !timeField.trim().isEmpty()) {
                        logger.info("字段降级: createdTime为空，使用time字段值: {} (假设为UTC时间)", timeField);
                        createdTime = timeField;
                        // 同时更新resultMap，保证后续处理一致
                        resultMap.put("createdTime", createdTime);
                    } else {
                        // 尝试查找是否有任何包含"time"字样的字段
                        for (String key : resultMap.keySet()) {
                            if (key.toLowerCase().contains("time") && resultMap.get(key) != null && !resultMap.get(key).trim().isEmpty()) {
                                logger.info("查找时间相关字段: 找到 {} = {} (假设为UTC时间)", key, resultMap.get(key));
                                createdTime = resultMap.get(key);
                                resultMap.put("createdTime", createdTime);
                                break;
                            }
                        }
                    }
                } else {
                    logger.info("使用原始createdTime字段: {} (假设为UTC时间)", createdTime);
                }
                
                // 如果没有时间，则视为不在范围内
                if (createdTime == null || createdTime.trim().isEmpty()) {
                    logger.warn("数据缺少时间信息，视为超出范围 - 数据ID: {}, title: {}", 
                               resultMap.get("originalId"), resultMap.get("title"));
                    batchOutOfRange++;
                    consecutiveOutOfRange++;
                    logger.warn("数据无时间增加计数 - batchOutOfRange: {}, consecutiveOutOfRange: {}", 
                               batchOutOfRange, consecutiveOutOfRange);
                
                    // 如果连续超出时间范围的数据量达到阈值，提前终止处理
                    if (consecutiveOutOfRange >= crawlerConfig.getOutOfRangeThreshold()) {
                        // 更新统计
                        state.updateDataStats(
                            inRangeCount + batchInRange, 
                            outOfRangeCount + batchOutOfRange, 
                            duplicateCount + batchDuplicate
                        );
                    
                        logger.warn("达到连续超出阈值(无时间数据) - 连续超出数: {}, 阈值: {}", 
                                   consecutiveOutOfRange, crawlerConfig.getOutOfRangeThreshold());
                        eventPublisher.publishWarningLog(taskName, "连续 " + consecutiveOutOfRange + " 条数据超出时间范围，任务提前结束");
                    
                        // 返回-1表示需要中断处理
                        return -1;
                    }
                
                    continue;
                }

                // 预解析时间，仅用于日志记录
                try {
                    // 尝试解析数据时间，以便在日志中显示转换后的北京时间
                    LocalDateTime parsedTime = parseDateTime(createdTime);
                    logger.info("数据时间解析: 原始值={} (UTC时间) -> 转换后={} (北京时间)", createdTime, parsedTime);
                } catch (Exception e) {
                    logger.warn("数据时间预解析失败(仅用于日志): {}, 错误: {}", createdTime, e.getMessage());
                }

                // 判断是否在指定时间范围内
                boolean inRange = isInTimeRange(createdTime, timeRange);

                // 添加时间范围标记
                resultMap.put("inTimeRange", String.valueOf(inRange));

                // 详细日志输出
                logger.info("时间范围判断 - 数据时间: {}, 时间范围: {}, 是否在范围内: {}", createdTime, timeRange, inRange);

                // 更新统计
                if (inRange) {
                    batchInRange++;
                    consecutiveOutOfRange = 0; // 重置连续超出范围计数
                    logger.info("数据在时间范围内 - 时间: {}, batchInRange: {}, consecutiveOutOfRange: {}", createdTime, batchInRange, consecutiveOutOfRange);
                } else {
                    batchOutOfRange++;
                    consecutiveOutOfRange++;
                    logger.warn("数据超出时间范围，不保存到数据库 - 时间: {}, batchOutOfRange: {}, consecutiveOutOfRange: {}", createdTime, batchOutOfRange, consecutiveOutOfRange);

                    // 如果连续超出时间范围的数据量达到阈值，提前终止处理
                    if (consecutiveOutOfRange >= crawlerConfig.getOutOfRangeThreshold()) {
                        // 更新统计
                        state.updateDataStats(
                            inRangeCount + batchInRange,
                            outOfRangeCount + batchOutOfRange,
                            duplicateCount + batchDuplicate
                        );

                        logger.warn("达到连续超出阈值(9条) - 连续超出数量: {}, 阈值: {}", consecutiveOutOfRange, crawlerConfig.getOutOfRangeThreshold());
                        eventPublisher.publishWarningLog(taskName, "连续 " + consecutiveOutOfRange + " 条数据超出时间范围，任务提前结束");

                        // 返回-1表示需要中断处理
                        return -1;
                    }

                    // 跳过保存，直接处理下一条数据
                    continue;
                }
                
                // 保存搜索结果
                SearchResult searchResult = new SearchResult();
                searchResult.setTaskName(taskName);
                searchResult.setSearchRule(searchRule);

                // IP资产关联将通过数据库触发器自动处理
                
                // 处理title字段
                String title = resultMap.get("title");
                if (title == null || title.isEmpty()) {
                    // 如果title为空，尝试从其他字段生成
                    String url = resultMap.get("url");
                    String domain = resultMap.get("domain");
                    if (url != null && !url.isEmpty()) {
                        title = "URL: " + url;
                    } else if (domain != null && !domain.isEmpty()) {
                        title = "Domain: " + domain;
                    } else {
                        title = "Untitled_" + System.currentTimeMillis();
                    }
                }
                searchResult.setTitle(title);

                // 设置基本网络信息字段
                searchResult.setIp(resultMap.get("ip"));
                searchResult.setPort(resultMap.get("port"));
                searchResult.setDomain(resultMap.get("domain"));
                searchResult.setHostname(resultMap.get("hostname"));
                searchResult.setTransport(resultMap.get("transport"));
                searchResult.setOrg(resultMap.get("org"));
                searchResult.setUrl(resultMap.get("url"));
                searchResult.setPath(resultMap.get("path"));
                searchResult.setHost(resultMap.get("host"));
                searchResult.setServer(resultMap.get("server"));
                searchResult.setXPoweredBy(resultMap.get("xPoweredBy"));

                // 设置网络扩展信息
                if (resultMap.get("asn") != null) {
                    try {
                        searchResult.setAsn(Integer.valueOf(resultMap.get("asn")));
                    } catch (NumberFormatException e) {
                        logger.warn("ASN格式错误: {}", resultMap.get("asn"));
                    }
                }
                if (resultMap.get("isIpv6") != null) {
                    searchResult.setIsIpv6(Boolean.valueOf(resultMap.get("isIpv6")));
                }
                searchResult.setSysTag(resultMap.get("sysTag"));

                // 设置状态码
                if (resultMap.get("statusCode") != null) {
                    try {
                        searchResult.setStatusCode(Integer.valueOf(resultMap.get("statusCode")));
                    } catch (NumberFormatException e) {
                        logger.warn("状态码格式错误: {}", resultMap.get("statusCode"));
                    }
                }

                // 设置位置信息字段
                searchResult.setLocationIsp(resultMap.get("locationIsp"));
                searchResult.setLocationScene(resultMap.get("locationScene"));
                searchResult.setLocationCountry(resultMap.get("locationCountry"));
                searchResult.setLocationProvince(resultMap.get("locationProvince"));
                searchResult.setLocationCity(resultMap.get("locationCity"));

                // 设置HTTP相关字段
                searchResult.setHttpLoadUrl(resultMap.get("httpLoadUrl"));
                searchResult.setPageType(resultMap.get("pageType"));

                // 设置技术栈信息
                searchResult.setTechStack(resultMap.get("techStack"));

                // 设置ICP备案信息
                searchResult.setIcpLicence(resultMap.get("icpLicence"));
                searchResult.setIcpUnit(resultMap.get("icpUnit"));
                searchResult.setIcpNature(resultMap.get("icpNature"));

                // 设置网站元信息
                searchResult.setMetaKeywords(resultMap.get("metaKeywords"));
                searchResult.setFaviconHash(resultMap.get("faviconHash"));
                searchResult.setFaviconUrl(resultMap.get("faviconUrl"));

                // 设置内容字段
                searchResult.setContent(resultMap.get("content"));
                searchResult.setSummary(resultMap.get("summary"));
                searchResult.setCreatedTime(createdTime);
                searchResult.setInTimeRange(inRange);
                searchResult.setCreatedAt(LocalDateTime.now());
                
                // 处理originalId字段
                String originalId = resultMap.get("originalId");
                if (originalId == null || originalId.isEmpty()) {
                    // 如果originalId为空，使用domain_port_tcp或ip_port_tcp格式生成
                    String domain = resultMap.getOrDefault("domain", "");
                    String ip = resultMap.getOrDefault("ip", "");
                    String port = resultMap.getOrDefault("port", "");
                    if (!domain.isEmpty() && !port.isEmpty()) {
                        originalId = domain + "_" + port + "_tcp";
                    } else if (!ip.isEmpty() && !port.isEmpty()) {
                        originalId = ip + "_" + port + "_tcp";
                    } else {
                        // 如果连domain/ip和port都没有，使用URL的hash作为originalId
                        String url = resultMap.get("url");
                        if (url != null && !url.isEmpty()) {
                            originalId = "url_" + Math.abs(url.hashCode());
                        } else {
                            // 最后的手段：使用时间戳
                            originalId = "time_" + System.currentTimeMillis();
                        }
                    }
                }
                searchResult.setOriginalId(originalId);
                
                // 处理path字段
                String path = resultMap.get("path");
                if (path == null || path.isEmpty()) {
                    // 如果path为空，尝试从URL中提取
                    String url = resultMap.get("url");
                    if (url != null && !url.isEmpty()) {
                        try {
                            java.net.URI uri = new java.net.URI(url);
                            path = uri.getPath();
                            if (path == null || path.isEmpty()) {
                                path = "/";
                            }
                        } catch (Exception e) {
                            path = "/";
                        }
                    } else {
                        path = "/";
                    }
                }
                searchResult.setPath(path);
                
                // 检查是否已存在相同记录
                boolean isDuplicate = searchResultRepository.existsByOriginalIdAndTitleAndPath(
                    originalId, 
                    title, 
                    path
                );
                
                if (isDuplicate) {
                    logger.info("发现重复数据 - originalId: {}, title: {}, path: {}", 
                              originalId, searchResult.getTitle(), searchResult.getPath());
                    batchDuplicate++;
                    // 保持统计一致性
                    if (inRange) {
                        batchInRange--;
                    } else {
                        batchOutOfRange--;
                        if (consecutiveOutOfRange > 0) {
                            consecutiveOutOfRange--;
                        }
                    }
                } else {
                    try {
                        searchResultRepository.save(searchResult);
                    } catch (Exception e) {
                        // 捕获保存异常，通常是由于重复记录
                        if (e.getMessage() != null && e.getMessage().contains("duplicate")) {
                            batchDuplicate++;
                            // 保持统计一致性
                            if (inRange) {
                                batchInRange--;
                            } else {
                                batchOutOfRange--;
                                if (consecutiveOutOfRange > 0) {
                                    consecutiveOutOfRange--;
                                }
                            }
                        } else {
                            throw e; // 如果是其他异常则继续抛出
                        }
                    }
                }
                
                // 更新当前URL
                String currentUrl = resultMap.get("url");
                if (currentUrl == null || currentUrl.isEmpty()) {
                    currentUrl = resultMap.get("domain");
                }
                state.updateCurrentUrl(currentUrl);
                
            } catch (Exception e) {
                logger.error("保存数据失败: {}", e.getMessage(), e);
                eventPublisher.publishErrorLog(taskName, "保存数据项失败: " + e.getMessage());
            }
        }
        
        // 更新任务统计
        state.updateDataStats(
            inRangeCount + batchInRange, 
            outOfRangeCount + batchOutOfRange, 
            duplicateCount + batchDuplicate
        );
        
        // 添加日志
        eventPublisher.publishSuccessLog(taskName, String.format("本批数据处理完成: %d条有效, %d条超出时间范围, %d条重复", 
                         batchInRange, batchOutOfRange, batchDuplicate));
        
        // 返回更新后的超出时间范围数量
        return outOfRangeCount + batchOutOfRange;
    }
    
    /**
     * 定时任务：检查浏览器空闲状态
     * 每5分钟检查一次，如果浏览器空闲超过配置的时间阈值且当前不在处理任务，关闭浏览器
     */
    @Scheduled(fixedRate = 5 * 60 * 1000)
    public void checkBrowserIdleStatus() {
        // 如果浏览器已初始化且不在处理任务
        if (singleBrowserManager.isBrowserInitialized() && !singleBrowserManager.isProcessingTask()) {
            // 判断当前是否在指定时间范围内（夜间模式时间段）
            LocalTime now = LocalTime.now();
            // 从配置文件获取夜间模式时间范围
            LocalTime nightModeStart = LocalTime.of(crawlerConfig.getNightModeStartHour(), 0);
            LocalTime nightModeEnd = LocalTime.of(crawlerConfig.getNightModeEndHour(), 0);
            
            // 处理跨日的情况（如果开始时间大于或等于结束时间，表示跨越了午夜）
            boolean isInClosingTimeRange;
            if (nightModeStart.compareTo(nightModeEnd) >= 0) {
                // 跨午夜的情况：在开始时间之后或在结束时间之前
                isInClosingTimeRange = !now.isBefore(nightModeStart) || !now.isAfter(nightModeEnd);
            } else {
                // 同一天内：在开始时间之后且在结束时间之前
                isInClosingTimeRange = !now.isBefore(nightModeStart) && !now.isAfter(nightModeEnd);
            }
            
            // 判断浏览器是否空闲超过阈值
            long idleTime = 0;
            if (lastTaskProcessTime != null) {
                idleTime = java.time.Duration.between(lastTaskProcessTime, LocalDateTime.now()).toMillis();
            }
            
            // 在指定时间范围或空闲时间过长时关闭浏览器
            if (isInClosingTimeRange || idleTime > crawlerConfig.getBrowserIdleTimeout()) {
                logger.info("浏览器空闲时间: {} 毫秒, 当前时间: {}, 夜间模式时间段: {}-{}, 关闭浏览器以节省资源", 
                          idleTime, now, nightModeStart, nightModeEnd);
                singleBrowserManager.closeBrowser();
            }
        }
    }
    
    /**
     * 获取浏览器状态
     */
    public String getBrowserStatus() {
        return singleBrowserManager.getBrowserStatus();
    }
    
    /**
     * 判断指定时间是否在时间范围内
     * @param timeStr 时间字符串
     * @param timeRange 时间范围 (格式: "2023-01-01 00:00:00,2023-12-31 23:59:59")
     * @return 是否在时间范围内
     */
    private boolean isInTimeRange(String timeStr, String timeRange) {
        if (timeStr == null || timeStr.isEmpty() || timeRange == null || timeRange.isEmpty()) {
            logger.warn("时间检查 - 参数为空: timeStr={}, timeRange={}", timeStr, timeRange);
            return true; // 修改：如果没有时间范围，默认认为在范围内
        }
        
        try {
            // 记录原始输入
            logger.info("时间范围检查 - 原始输入: 数据时间={}, 时间范围={}", timeStr, timeRange);
            
            // 解析时间范围
            String[] rangeParts = timeRange.split(",");
            if (rangeParts.length != 2) {
                logger.warn("时间范围格式错误: {}, 期望格式为'startTime,endTime'", timeRange);
                return true; // 修改：如果范围格式错误，默认认为在范围内
            }
            
            logger.debug("解析时间范围: startTime={}, endTime={}", rangeParts[0].trim(), rangeParts[1].trim());
            
            // 解析开始和结束时间（假设任务时间范围已经是北京时间）
            LocalDateTime startTime = parseDateTime(rangeParts[0].trim());
            LocalDateTime endTime = parseDateTime(rangeParts[1].trim());
            
            // 解析数据时间 (会自动将UTC时间转换为北京时间)
            LocalDateTime dataTime = parseDateTime(timeStr);
            
            // 判断是否在范围内
            boolean afterOrEqualStart = dataTime.isEqual(startTime) || dataTime.isAfter(startTime);
            boolean beforeOrEqualEnd = dataTime.isEqual(endTime) || dataTime.isBefore(endTime);
            boolean inRange = afterOrEqualStart && beforeOrEqualEnd;
            
            // 添加更详细的调试信息
            logger.info("*** 时间判断详情 ***");
            logger.info("原始数据时间: {} (假设为UTC时间)", timeStr);
            logger.info("数据时间(转换后北京时间): {}", dataTime);
            logger.info("任务开始时间(北京时间): {}", startTime);
            logger.info("任务结束时间(北京时间): {}", endTime);
            logger.info("大于等于开始时间?: {}", afterOrEqualStart);
            logger.info("小于等于结束时间?: {}", beforeOrEqualEnd);
            logger.info("最终判断结果 (inRange): {}", inRange);
            
            // 最终返回前记录
            logger.info("isInTimeRange最终返回: {} (数据时间: {}, 在范围 {} 到 {} 内)", 
                       inRange, dataTime, startTime, endTime);
            return inRange;
        } catch (Exception e) {
            logger.error("时间范围判断失败: {}", e.getMessage(), e);
            return true; // 修改：发生错误时默认认为在范围内
        }
    }
    
    /**
     * 解析日期时间字符串，转换为北京时间
     * @param dateTimeStr 日期时间字符串
     * @return 转换后的北京时间LocalDateTime对象
     */
    private LocalDateTime parseDateTime(String dateTimeStr) {
        logger.debug("开始解析时间: {}", dateTimeStr);
        
        try {
            // 如果包含T和Z，说明是ISO UTC格式，需要特殊处理
            if (dateTimeStr.contains("T") && (dateTimeStr.contains("Z") || dateTimeStr.contains("+") || dateTimeStr.contains("-"))) {
                logger.debug("检测到ISO格式时间: {}", dateTimeStr);
                return parseIsoDateTime(dateTimeStr);
            }
            
            // 尝试使用标准格式解析
            try {
                // 注意：此处假设搜索结果中的标准格式时间是UTC时间，需要转换为北京时间
                LocalDateTime utcDateTime = LocalDateTime.parse(dateTimeStr.trim(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                logger.debug("使用标准格式解析时间成功: {} -> {} (UTC时间)", dateTimeStr, utcDateTime);
                
                // 将UTC时间转换为北京时间（UTC+8）
                ZonedDateTime utcZoned = utcDateTime.atZone(ZoneId.of("UTC"));
                ZonedDateTime bjZoned = utcZoned.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
                LocalDateTime bjDateTime = bjZoned.toLocalDateTime();
                
                logger.debug("UTC时间转换为北京时间: {} -> {}", utcDateTime, bjDateTime);
                return bjDateTime;
            } catch (Exception e) {
                logger.debug("标准格式解析失败: {}, 尝试其他格式", e.getMessage());
                // 尝试ISO格式作为回退
                return parseIsoDateTime(dateTimeStr);
            }
        } catch (Exception e) {
            logger.error("时间解析异常: {} -> {}", dateTimeStr, e.getMessage());
            throw new IllegalArgumentException("无法解析日期时间: " + dateTimeStr, e);
        }
    }
    
    /**
     * 解析ISO格式日期时间字符串，并转换为北京时间
     * @param isoTimeStr ISO格式日期时间字符串
     * @return 转换为北京时间的LocalDateTime对象
     */
    private LocalDateTime parseIsoDateTime(String isoTimeStr) {
        logger.debug("解析ISO时间: {}", isoTimeStr);
        
        try {
            // 处理特殊的ISO时间格式
            if (isoTimeStr.endsWith("Z")) {
                // 标准UTC时间，需要转换为北京时间
                try {
                    Instant instant = Instant.parse(isoTimeStr);
                    ZonedDateTime utcTime = instant.atZone(ZoneId.of("UTC"));
                    ZonedDateTime bjTime = utcTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
                    logger.debug("UTC时间转换为北京时间: {} -> {}", isoTimeStr, bjTime);
                    return bjTime.toLocalDateTime();
                } catch (Exception e) {
                    logger.warn("UTC时间解析失败: {}, {}", isoTimeStr, e.getMessage());
                }
            }
            
            // 尝试作为ISO日期时间解析
            try {
                TemporalAccessor temporalAccessor = DateTimeFormatter.ISO_INSTANT.parse(isoTimeStr);
                ZonedDateTime utcTime = ZonedDateTime.from(temporalAccessor);
                ZonedDateTime bjTime = utcTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
                logger.debug("ISO_INSTANT解析成功: {} -> UTC: {} -> 北京: {}", 
                           isoTimeStr, utcTime, bjTime);
                return bjTime.toLocalDateTime();
            } catch (Exception e) {
                logger.debug("ISO_INSTANT解析失败: {}, {}", isoTimeStr, e.getMessage());
            }
            
            // 尝试带偏移量的ISO格式
            try {
                ZonedDateTime zonedDateTime = ZonedDateTime.parse(isoTimeStr);
                ZonedDateTime bjTime = zonedDateTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
                logger.debug("带时区解析成功: {} -> {}", isoTimeStr, bjTime);
                return bjTime.toLocalDateTime();
            } catch (Exception e) {
                logger.debug("带时区解析失败: {}, {}", isoTimeStr, e.getMessage());
            }
            
            // 尝试只有日期部分的ISO格式
            try {
                LocalDate date = LocalDate.parse(isoTimeStr, DateTimeFormatter.ISO_DATE);
                logger.debug("ISO日期格式解析成功: {} -> {}", isoTimeStr, date);
                return date.atStartOfDay(); // 北京时间当天0点
            } catch (Exception e) {
                logger.debug("ISO日期解析失败: {}, {}", isoTimeStr, e.getMessage());
            }
            
            // 所有尝试都失败，抛出异常
            throw new IllegalArgumentException("无法解析ISO格式日期时间: " + isoTimeStr);
        } catch (Exception e) {
            logger.error("ISO时间解析彻底失败: {} -> {}", isoTimeStr, e.getMessage());
            throw new IllegalArgumentException("无法解析ISO格式日期时间: " + isoTimeStr, e);
        }
    }

    /**
     * 翻页前准备工作 - 修复版本，使用正确的PlaywrightUtils实例
     */
    private boolean prepareForPagination(PlaywrightUtils playwrightUtils, String taskName, int pageNum) {
        logger.info("=== 开始翻页前准备工作，准备翻页到第{}页 ===", pageNum);

        try {
            // 检查浏览器管理器状态
            logger.debug("检查浏览器管理器状态...");
            if (!singleBrowserManager.isBrowserInitialized()) {
                logger.error("浏览器管理器显示未初始化，无法执行翻页操作");
                eventPublisher.publishErrorLog(taskName, "浏览器未初始化，翻页操作失败");
                logger.info("=== 翻页前准备失败: 浏览器未初始化 ===");
                return false;
            }
            logger.debug("浏览器管理器状态检查通过");

            // 检查PlaywrightUtils实例状态
            logger.debug("检查PlaywrightUtils实例状态...");
            if (playwrightUtils == null) {
                logger.error("PlaywrightUtils实例为null，无法执行翻页操作");
                eventPublisher.publishErrorLog(taskName, "浏览器实例为null，翻页操作失败");
                logger.info("=== 翻页前准备失败: PlaywrightUtils实例为null ===");
                return false;
            }
            logger.debug("PlaywrightUtils实例状态检查通过");

            // 深度检查浏览器状态
            logger.debug("执行深度浏览器状态检查...");
            if (!validateAndRecoverBrowserState(playwrightUtils, taskName)) {
                logger.info("=== 翻页前准备失败: 浏览器状态验证失败 ===");
                return false;
            }
            logger.debug("深度浏览器状态检查通过");

            // 检查翻页状态（使用增强的检测逻辑）
            logger.info("=== 开始执行翻页前状态检查 ===");
            logger.info("准备翻到第{}页，执行翻页可行性检查...", pageNum);

            boolean hasNext = playwrightUtils.hasNextPage();
            logger.info("翻页状态检查结果: {} (第{}页)", hasNext, pageNum);

            if (!hasNext) {
                logger.info("翻页前检查发现已无下一页，当前为最后一页");
                eventPublisher.publishInfoLog(taskName, "检测到已到达最后一页，停止翻页");
                logger.info("=== 翻页前准备完成: 已到达最后一页，停止翻页 ===");
                return false;
            }

            logger.info("翻页状态检查通过，确认可以翻到第{}页", pageNum);

            logger.info("翻页状态检查通过，可以继续翻页");
            logger.info("=== 翻页前准备完成: 可以继续翻页到第{}页 ===", pageNum);
            return true;

        } catch (Exception e) {
            logger.error("翻页准备阶段失败: {}", e.getMessage(), e);
            eventPublisher.publishErrorLog(taskName, "翻页准备失败: " + e.getMessage());
            logger.info("=== 翻页前准备失败: 异常 - {} ===", e.getMessage());
            return false;
        }
    }

    /**
     * 验证并恢复浏览器状态 - 修复版本，使用正确的PlaywrightUtils实例
     */
    private boolean validateAndRecoverBrowserState(PlaywrightUtils playwrightUtils, String taskName) {
        logger.debug("开始验证并恢复浏览器状态...");

        try {
            // 检查基本初始化状态 - 优化版本，支持恢复
            logger.debug("检查PlaywrightUtils初始化状态...");
            if (!playwrightUtils.isInitialized()) {
                logger.warn("PlaywrightUtils未初始化，尝试恢复浏览器状态...");

                // 尝试重新获取浏览器实例
                try {
                    logger.info("尝试重新获取浏览器实例...");
                    // 通过SingleBrowserManager重新获取浏览器资源
                    if (singleBrowserManager.isBrowserInitialized()) {
                        logger.info("浏览器管理器状态正常，浏览器实例可能只是临时断开");
                        // 继续执行，让后续的页面有效性检查来处理
                    } else {
                        logger.error("浏览器管理器也显示未初始化，翻页操作失败");
                        eventPublisher.publishErrorLog(taskName, "浏览器管理器未初始化，翻页操作失败");
                        return false;
                    }
                } catch (Exception e) {
                    logger.warn("重新获取浏览器实例失败: {}", e.getMessage());
                    // 不立即返回false，继续检查其他状态
                }
            } else {
                logger.debug("PlaywrightUtils初始化状态检查通过");
            }

            // 检查连接状态 - 优化版本，更宽松的检查
            logger.debug("检查浏览器连接状态...");
            try {
                if (!playwrightUtils.isConnected()) {
                    logger.warn("浏览器连接检查失败，但继续尝试翻页操作");
                    eventPublisher.publishWarningLog(taskName, "浏览器连接状态异常，但继续尝试翻页");
                    // 不立即返回false，让翻页操作自己处理连接问题
                } else {
                    logger.debug("浏览器连接状态检查通过");
                }
            } catch (Exception e) {
                logger.warn("浏览器连接状态检查异常: {}", e.getMessage());
                // 继续执行，不因为连接检查异常而终止
            }

            // 检查页面对象状态 - 这是最重要的检查
            logger.debug("检查页面对象状态...");
            try {
                if (!playwrightUtils.isPageValid()) {
                    logger.warn("页面对象无效，但翻页操作会自动处理");
                    eventPublisher.publishWarningLog(taskName, "检测到页面对象异常，翻页操作将自动处理");
                    // 不返回false，让翻页操作中的hasNextPage方法来处理页面对象问题
                } else {
                    logger.debug("页面对象状态检查通过");
                }
            } catch (Exception e) {
                logger.warn("页面对象状态检查异常: {}", e.getMessage());
                // 继续执行，让翻页操作自己处理页面对象问题
            }

            logger.debug("浏览器状态验证完成，允许继续翻页操作");
            return true;

        } catch (Exception e) {
            logger.error("浏览器状态验证过程异常: {}", e.getMessage(), e);
            eventPublisher.publishWarningLog(taskName, "浏览器状态验证异常，但继续尝试翻页: " + e.getMessage());
            // 即使验证过程异常，也允许继续尝试翻页，让具体的翻页操作来处理问题
            return true;
        }
    }

    /**
     * 执行翻页操作（带重试机制） - 修复版本，使用正确的PlaywrightUtils实例
     */
    private List<Map<String, String>> executePageTurnWithRetry(PlaywrightUtils playwrightUtils, String taskName, int pageNum) throws Exception {
        int maxRetries = 3;
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                logger.info("执行第{}页翻页操作，尝试次数: {}/{}", pageNum, attempt, maxRetries);

                List<Map<String, String>> results = playwrightUtils.clickNextPageWithRealisticInteraction();

                if (results != null) {
                    logger.info("第{}页翻页成功，获取{}条数据", pageNum, results.size());
                    return results;
                }

                logger.warn("第{}页翻页返回null结果，尝试次数: {}/{}", pageNum, attempt, maxRetries);

            } catch (Exception e) {
                lastException = e;
                logger.warn("第{}页翻页失败，尝试次数: {}/{}, 错误: {}", pageNum, attempt, maxRetries, e.getMessage());

                // 判断是否为可重试的错误
                if (!isRetryablePaginationError(e)) {
                    logger.error("遇到不可重试的翻页错误: {}", e.getMessage());
                    throw e;
                }

                // 如果不是最后一次尝试，等待后重试
                if (attempt < maxRetries) {
                    try {
                        int delay = attempt * 2000; // 递增延迟
                        logger.info("等待{}ms后重试翻页操作", delay);
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("翻页重试被中断", ie);
                    }
                }
            }
        }

        // 所有重试都失败了
        String errorMsg = String.format("第%d页翻页操作在%d次尝试后仍然失败", pageNum, maxRetries);
        logger.error(errorMsg);
        eventPublisher.publishErrorLog(taskName, errorMsg);

        if (lastException != null) {
            throw lastException;
        } else {
            throw new RuntimeException(errorMsg);
        }
    }

    /**
     * 验证翻页结果
     */
    private boolean validatePageTurnResult(List<Map<String, String>> results, String taskName, int pageNum) {
        if (results == null) {
            logger.warn("第{}页翻页操作返回null结果，可能已到达最后一页", pageNum);
            eventPublisher.publishWarningLog(taskName, "第" + pageNum + "页翻页操作无返回结果，停止翻页");
            return false;
        }

        if (results.isEmpty()) {
            logger.info("第{}页翻页操作返回空结果", pageNum);
            // 空结果不一定是错误，可能是正常的空页
        }

        logger.info("第{}页翻页结果验证通过，数据条数: {}", pageNum, results.size());
        return true;
    }

    /**
     * 判断是否为可重试的翻页错误
     */
    private boolean isRetryablePaginationError(Exception e) {
        if (e == null || e.getMessage() == null) {
            return false;
        }

        String message = e.getMessage().toLowerCase();

        // 不可重试的错误
        String[] nonRetryableErrors = {
            "已到达最后一页",
            "下一页按钮已禁用",
            "无法定位到可用的下一页按钮",
            "browser has been closed",
            "target page, context or browser has been"
        };

        for (String error : nonRetryableErrors) {
            if (message.contains(error.toLowerCase())) {
                return false;
            }
        }

        // 可重试的错误
        String[] retryableErrors = {
            "timeout",
            "network",
            "connection",
            "等待",
            "加载",
            "响应"
        };

        for (String error : retryableErrors) {
            if (message.contains(error.toLowerCase())) {
                return true;
            }
        }

        // 默认认为是可重试的
        return true;
    }

    /**
     * 记录翻页性能统计
     */
    private void logPaginationPerformanceStats(String taskName) {
        try {
            // 这里可以记录翻页相关的性能统计
            logger.info("任务 {} 翻页性能统计记录完成", taskName);
        } catch (Exception e) {
            logger.warn("记录翻页性能统计失败: {}", e.getMessage());
        }
    }
}