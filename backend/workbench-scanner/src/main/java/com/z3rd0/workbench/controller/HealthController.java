package com.z3rd0.workbench.controller;

import com.z3rd0.workbench.utils.SingleBrowserManager;
import com.z3rd0.workbench.utils.NetworkDiagnostic;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 提供应用和浏览器状态监控接口
 */
@RestController
@RequestMapping("/api/health")
public class HealthController {
    
    private static final Logger logger = LoggerFactory.getLogger(HealthController.class);
    
    @Autowired
    private SingleBrowserManager browserManager;
    
    /**
     * 应用健康检查
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getHealthStatus() {
        Map<String, Object> status = new HashMap<>();
        
        try {
            // 应用基本状态
            status.put("application", "running");
            status.put("timestamp", System.currentTimeMillis());
            
            // 浏览器状态
            status.put("browser_initialized", browserManager.isBrowserInitialized());
            status.put("browser_status", browserManager.getBrowserStatus());
            status.put("processing_task", browserManager.isProcessingTask());
            status.put("async_init_status", getAsyncInitStatus());
            
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            logger.error("获取健康状态失败", e);
            status.put("application", "error");
            status.put("error", e.getMessage());
            return ResponseEntity.status(500).body(status);
        }
    }
    
    /**
     * 浏览器状态检查
     */
    @GetMapping("/browser")
    public ResponseEntity<Map<String, Object>> getBrowserStatus() {
        Map<String, Object> status = new HashMap<>();
        
        try {
            status.put("initialized", browserManager.isBrowserInitialized());
            status.put("status", browserManager.getBrowserStatus());
            status.put("processing_task", browserManager.isProcessingTask());
            
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            logger.error("获取浏览器状态失败", e);
            status.put("error", e.getMessage());
            return ResponseEntity.status(500).body(status);
        }
    }
    
    /**
     * 手动初始化浏览器
     */
    @PostMapping("/browser/init")
    public ResponseEntity<Map<String, Object>> initBrowser() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("收到手动初始化浏览器请求");
            
            // 尝试获取浏览器实例（会触发初始化）
            try (SingleBrowserManager.BrowserResource browserResource = browserManager.acquireBrowser()) {
                result.put("success", true);
                result.put("message", "浏览器初始化成功");
                result.put("status", browserManager.getBrowserStatus());
                
                logger.info("手动初始化浏览器成功");
                return ResponseEntity.ok(result);
            }
        } catch (Exception e) {
            logger.error("手动初始化浏览器失败", e);
            result.put("success", false);
            result.put("message", "浏览器初始化失败: " + e.getMessage());
            result.put("error", e.getMessage());
            
            return ResponseEntity.status(500).body(result);
        }
    }
    
    /**
     * 关闭浏览器
     */
    @PostMapping("/browser/close")
    public ResponseEntity<Map<String, Object>> closeBrowser() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("收到关闭浏览器请求");
            browserManager.closeBrowser();
            
            result.put("success", true);
            result.put("message", "浏览器已关闭");
            result.put("status", browserManager.getBrowserStatus());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("关闭浏览器失败", e);
            result.put("success", false);
            result.put("message", "关闭浏览器失败: " + e.getMessage());
            result.put("error", e.getMessage());
            
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 网络连接诊断
     */
    @GetMapping("/network")
    public ResponseEntity<Map<String, Object>> getNetworkStatus() {
        Map<String, Object> result = new HashMap<>();

        try {
            logger.info("开始网络连接诊断");

            // 检查网络连接
            boolean networkOk = NetworkDiagnostic.checkNetworkConnection();
            result.put("network_connected", networkOk);

            // 获取详细网络报告
            String report = NetworkDiagnostic.getNetworkStatusReport();
            result.put("network_report", report);

            result.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("网络诊断失败", e);
            result.put("error", e.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 获取异步初始化状态
     */
    private String getAsyncInitStatus() {
        try {
            // 使用反射获取initFuture字段的状态
            java.lang.reflect.Field initFutureField = SingleBrowserManager.class.getDeclaredField("initFuture");
            initFutureField.setAccessible(true);
            java.util.concurrent.CompletableFuture<?> initFuture =
                (java.util.concurrent.CompletableFuture<?>) initFutureField.get(browserManager);

            if (initFuture == null) {
                return "not_started";
            } else if (initFuture.isDone()) {
                if (initFuture.isCompletedExceptionally()) {
                    return "failed";
                } else if (initFuture.isCancelled()) {
                    return "cancelled";
                } else {
                    return "completed";
                }
            } else {
                return "running";
            }
        } catch (Exception e) {
            logger.warn("获取异步初始化状态失败: {}", e.getMessage());
            return "unknown";
        }
    }
}
