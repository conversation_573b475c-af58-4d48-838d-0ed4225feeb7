package com.z3rd0.common.enums;

/**
 * 定时任务状态枚举
 * 定义定时任务的各种状态
 */
public enum ScheduleStatus {
    
    /**
     * 活跃状态
     * 定时任务已启用且正在按计划执行
     */
    ACTIVE("ACTIVE", "活跃", "定时任务已启用且正在按计划执行"),
    
    /**
     * 非活跃状态
     * 定时任务未启用或已禁用
     */
    INACTIVE("INACTIVE", "非活跃", "定时任务未启用或已禁用"),
    
    /**
     * 暂停状态
     * 定时任务已启用但临时暂停执行
     */
    PAUSED("PAUSED", "暂停", "定时任务已启用但临时暂停执行"),
    
    /**
     * 错误状态
     * 定时任务配置有误或执行出错
     */
    ERROR("ERROR", "错误", "定时任务配置有误或执行出错");
    
    private final String code;
    private final String displayName;
    private final String description;
    
    ScheduleStatus(String code, String displayName, String description) {
        this.code = code;
        this.displayName = displayName;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取状态
     * @param code 状态代码
     * @return 对应的状态枚举，如果不存在则返回INACTIVE
     */
    public static ScheduleStatus fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return INACTIVE;
        }
        
        for (ScheduleStatus status : values()) {
            if (status.code.equalsIgnoreCase(code.trim())) {
                return status;
            }
        }
        
        return INACTIVE; // 默认返回非活跃状态
    }
    
    /**
     * 判断是否为活跃状态
     * @return true如果是活跃状态
     */
    public boolean isActive() {
        return this == ACTIVE;
    }
    
    /**
     * 判断是否为非活跃状态
     * @return true如果是非活跃状态
     */
    public boolean isInactive() {
        return this == INACTIVE;
    }
    
    /**
     * 判断是否为暂停状态
     * @return true如果是暂停状态
     */
    public boolean isPaused() {
        return this == PAUSED;
    }
    
    /**
     * 判断是否为错误状态
     * @return true如果是错误状态
     */
    public boolean isError() {
        return this == ERROR;
    }
    
    /**
     * 判断是否可以执行
     * @return true如果可以执行（活跃状态）
     */
    public boolean canExecute() {
        return this == ACTIVE;
    }
}
