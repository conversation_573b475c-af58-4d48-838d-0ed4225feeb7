package com.z3rd0.common.utils;

import com.z3rd0.common.enums.TimeWindowStrategy;
import com.z3rd0.common.model.Task;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 智能时间窗口计算器
 * 解决数据搜索时间窗口问题，避免数据遗漏和重复搜索
 * 
 * 核心问题：
 * 如果设置搜索范围为"今日8:00-24:00"，但任务在12:00执行，
 * 那么12:00-24:00之间的数据尚未产生，次日搜索"明日8:00-24:00"时会遗漏前一天12:00-24:00的数据
 * 
 * 解决方案：
 * 智能计算实际搜索时间范围，确保数据完整性
 */
public class SmartTimeWindowCalculator {
    
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 时间范围结果类
     */
    public static class TimeRange {
        private final LocalDateTime startTime;
        private final LocalDateTime endTime;
        private final String originalRange;
        private final String calculatedRange;
        private final String strategy;
        private final String reason;
        
        public TimeRange(LocalDateTime startTime, LocalDateTime endTime, String originalRange, 
                        String calculatedRange, String strategy, String reason) {
            this.startTime = startTime;
            this.endTime = endTime;
            this.originalRange = originalRange;
            this.calculatedRange = calculatedRange;
            this.strategy = strategy;
            this.reason = reason;
        }
        
        // Getters
        public LocalDateTime getStartTime() { return startTime; }
        public LocalDateTime getEndTime() { return endTime; }
        public String getOriginalRange() { return originalRange; }
        public String getCalculatedRange() { return calculatedRange; }
        public String getStrategy() { return strategy; }
        public String getReason() { return reason; }
        
        /**
         * 获取格式化的时间范围字符串
         * @return 格式化的时间范围
         */
        public String getFormattedRange() {
            return startTime.format(TIME_FORMATTER) + "," + endTime.format(TIME_FORMATTER);
        }
    }
    
    /**
     * 计算实际搜索时间范围
     * @param task 任务对象
     * @param executionTime 执行时间
     * @return 计算后的时间范围
     */
    public TimeRange calculateSearchTimeRange(Task task, LocalDateTime executionTime) {
        
        TimeWindowStrategy strategy = TimeWindowStrategy.fromCode(task.getTimeWindowStrategy());
        String originalRange = task.getTimeRange();
        
        try {
            switch (strategy) {
                case SMART:
                    return calculateSmartTimeRange(task, executionTime, originalRange);
                case ROLLING:
                    return calculateRollingTimeRange(task, executionTime, originalRange);
                case FIXED:
                default:
                    return calculateFixedTimeRange(task, executionTime, originalRange);
            }
        } catch (Exception e) {
            // 计算失败时使用固定策略
            return calculateFixedTimeRange(task, executionTime, originalRange);
        }
    }
    
    /**
     * 智能时间范围计算
     * 根据执行时间和配置的时间范围智能调整搜索窗口
     */
    private TimeRange calculateSmartTimeRange(Task task, LocalDateTime executionTime, String originalRange) {
        
        // 解析原始时间范围
        TimeRangeParts parts = parseTimeRange(originalRange);
        if (parts == null) {
            return createDefaultTimeRange(executionTime, originalRange, "SMART", "原始时间范围解析失败，使用默认范围");
        }
        
        LocalDateTime lastScheduled = task.getLastScheduledAt();
        LocalDateTime calculatedStart, calculatedEnd;
        String reason;
        
        if (lastScheduled == null) {
            // 首次执行，使用配置的时间范围
            calculatedStart = parts.startTime;
            calculatedEnd = parts.endTime;
            reason = "首次执行，使用配置的时间范围";
        } else {
            // 非首次执行，智能计算避免数据遗漏
            calculatedStart = lastScheduled;
            calculatedEnd = executionTime;
            reason = "基于上次执行时间智能计算，避免数据遗漏";
            
            // 如果时间窗口太小（小于1小时），扩展到至少1小时
            if (calculatedEnd.isBefore(calculatedStart.plusHours(1))) {
                calculatedEnd = calculatedStart.plusHours(1);
                reason += "，扩展到最小1小时窗口";
            }
        }
        
        String calculatedRange = calculatedStart.format(TIME_FORMATTER) + "," + calculatedEnd.format(TIME_FORMATTER);

        return new TimeRange(calculatedStart, calculatedEnd, originalRange, calculatedRange, "SMART", reason);
    }
    
    /**
     * 滚动时间范围计算
     * 基于上次执行时间的滚动窗口
     */
    private TimeRange calculateRollingTimeRange(Task task, LocalDateTime executionTime, String originalRange) {
        
        LocalDateTime lastScheduled = task.getLastScheduledAt();
        LocalDateTime calculatedStart, calculatedEnd;
        String reason;
        
        if (lastScheduled == null) {
            // 首次执行，使用24小时窗口
            calculatedStart = executionTime.minusHours(24);
            calculatedEnd = executionTime;
            reason = "首次执行，使用24小时滚动窗口";
        } else {
            // 从上次执行时间开始到当前时间
            calculatedStart = lastScheduled;
            calculatedEnd = executionTime;
            reason = "基于上次执行时间的滚动窗口";
        }
        
        String calculatedRange = calculatedStart.format(TIME_FORMATTER) + "," + calculatedEnd.format(TIME_FORMATTER);

        return new TimeRange(calculatedStart, calculatedEnd, originalRange, calculatedRange, "ROLLING", reason);
    }
    
    /**
     * 固定时间范围计算
     * 严格按照配置的时间范围执行
     */
    private TimeRange calculateFixedTimeRange(Task task, LocalDateTime executionTime, String originalRange) {
        
        TimeRangeParts parts = parseTimeRange(originalRange);
        if (parts == null) {
            return createDefaultTimeRange(executionTime, originalRange, "FIXED", "原始时间范围解析失败，使用默认范围");
        }
        
        String reason = "严格按照配置的固定时间范围执行";

        return new TimeRange(parts.startTime, parts.endTime, originalRange, originalRange, "FIXED", reason);
    }
    
    /**
     * 创建默认时间范围（最近24小时）
     */
    private TimeRange createDefaultTimeRange(LocalDateTime executionTime, String originalRange, String strategy, String reason) {
        LocalDateTime start = executionTime.minusHours(24);
        LocalDateTime end = executionTime;
        String defaultRange = start.format(TIME_FORMATTER) + "," + end.format(TIME_FORMATTER);

        return new TimeRange(start, end, originalRange, defaultRange, strategy, reason);
    }
    
    /**
     * 时间范围解析结果
     */
    private static class TimeRangeParts {
        final LocalDateTime startTime;
        final LocalDateTime endTime;
        
        TimeRangeParts(LocalDateTime startTime, LocalDateTime endTime) {
            this.startTime = startTime;
            this.endTime = endTime;
        }
    }
    
    /**
     * 解析时间范围字符串
     * @param timeRange 时间范围字符串，格式: "startTime,endTime"
     * @return 解析结果，失败返回null
     */
    private TimeRangeParts parseTimeRange(String timeRange) {
        if (timeRange == null || timeRange.trim().isEmpty()) {
            return null;
        }
        
        try {
            String[] parts = timeRange.split(",");
            if (parts.length != 2) {
                return null;
            }
            
            LocalDateTime startTime = LocalDateTime.parse(parts[0].trim(), TIME_FORMATTER);
            LocalDateTime endTime = LocalDateTime.parse(parts[1].trim(), TIME_FORMATTER);
            
            if (startTime.isAfter(endTime)) {
                return null;
            }
            
            return new TimeRangeParts(startTime, endTime);
        } catch (DateTimeParseException e) {
            return null;
        }
    }
}
