package com.z3rd0.common.enums;

/**
 * 时间窗口策略枚举
 * 定义不同的时间窗口计算策略，用于解决数据搜索时间窗口问题
 */
public enum TimeWindowStrategy {
    
    /**
     * 智能策略
     * 根据任务执行时间和上次执行时间智能计算搜索范围，避免数据遗漏和重复
     * 例如：任务设置"今日8:00-24:00"，但在12:00执行，则搜索"昨日12:00-今日12:00"
     */
    SMART("SMART", "智能策略", "根据执行时间智能计算，避免数据遗漏"),
    
    /**
     * 固定策略
     * 严格按照配置的时间范围执行，不做任何调整
     * 适用于对时间范围有严格要求的场景
     */
    FIXED("FIXED", "固定策略", "严格按照配置的时间范围执行"),
    
    /**
     * 滚动策略
     * 基于上次执行时间计算滚动时间窗口
     * 适用于需要连续覆盖时间段的场景
     */
    ROLLING("ROLLING", "滚动策略", "基于上次执行时间的滚动窗口");
    
    private final String code;
    private final String displayName;
    private final String description;
    
    TimeWindowStrategy(String code, String displayName, String description) {
        this.code = code;
        this.displayName = displayName;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取策略
     * @param code 策略代码
     * @return 对应的策略枚举，如果不存在则返回SMART
     */
    public static TimeWindowStrategy fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return SMART;
        }
        
        for (TimeWindowStrategy strategy : values()) {
            if (strategy.code.equalsIgnoreCase(code.trim())) {
                return strategy;
            }
        }
        
        return SMART; // 默认返回智能策略
    }
    
    /**
     * 判断是否为智能策略
     * @return true如果是智能策略
     */
    public boolean isSmart() {
        return this == SMART;
    }
    
    /**
     * 判断是否为固定策略
     * @return true如果是固定策略
     */
    public boolean isFixed() {
        return this == FIXED;
    }
    
    /**
     * 判断是否为滚动策略
     * @return true如果是滚动策略
     */
    public boolean isRolling() {
        return this == ROLLING;
    }
}
