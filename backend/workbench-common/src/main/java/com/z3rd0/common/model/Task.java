package com.z3rd0.common.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "tasks")
@JsonIgnoreProperties(value = {"executionStatusDisplayName", "statusDisplayName", "executedToday"}, ignoreUnknown = true)
public class Task {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(name = "task_name", nullable = false)
    private String name;
    @Column(columnDefinition = "TEXT")
    private String rule;
    @Column(name = "filter_range")
    private String filterRange; // 实际执行时的数据筛选范围
    @Column(name = "time_range", length = 100)
    private String timeRange; // 任务时间范围参数
    @Column(name = "priority", columnDefinition = "INT DEFAULT 5")
    private Integer priority = 5; // 任务优先级：1-10，数字越小优先级越高，默认为5
    @Column(name = "executed_at")
    private LocalDateTime executedAt; // 任务执行时间
    @Column(name = "status")
    private String status;
    @Transient
    private com.z3rd0.common.model.TaskState taskState;
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    @Column(name = "retry_count")
    private Integer retryCount = 0;
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    @Column(name = "completed_at")
    private LocalDateTime completedAt;

    // 定时任务相关字段
    @Column(name = "schedule_enabled", columnDefinition = "BOOLEAN DEFAULT FALSE")
    private Boolean scheduleEnabled = false; // 是否启用定时执行

    @Column(name = "schedule_time", length = 20)
    private String scheduleTime; // 定时执行时间(HH:mm格式)

    @Column(name = "schedule_cron", length = 50)
    private String scheduleCron; // Cron表达式(可选)

    @Column(name = "time_window_strategy", length = 20, columnDefinition = "VARCHAR(20) DEFAULT 'SMART'")
    private String timeWindowStrategy = "SMART"; // 时间窗口策略

    @Column(name = "last_scheduled_at")
    private LocalDateTime lastScheduledAt; // 上次定时执行时间

    @Column(name = "schedule_frequency", length = 20, columnDefinition = "VARCHAR(20) DEFAULT 'DAILY'")
    private String scheduleFrequency = "DAILY"; // 执行频率：DAILY-每日，EVERY_THREE_DAYS-每三日

    @Column(name = "time_window_config", columnDefinition = "TEXT")
    private String timeWindowConfig; // 时间窗口配置JSON

    @Column(name = "schedule_status", length = 20, columnDefinition = "VARCHAR(20) DEFAULT 'INACTIVE'")
    private String scheduleStatus = "INACTIVE"; // 定时任务状态

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (status == null) {
            setTaskState(com.z3rd0.common.model.TaskState.PENDING);
        }
        if (retryCount == null) {
            retryCount = 0;
        }
        if (priority == null) {
            priority = 5; // 默认优先级
        }
        if (scheduleEnabled == null) {
            scheduleEnabled = false; // 默认不启用定时任务
        }
        if (timeWindowStrategy == null) {
            timeWindowStrategy = "SMART"; // 默认智能时间窗口策略
        }
        if (scheduleStatus == null) {
            scheduleStatus = "INACTIVE"; // 默认非活跃状态
        }
    }
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    public com.z3rd0.common.model.TaskState getTaskState() {
        if (taskState == null && status != null) {
            taskState = com.z3rd0.common.model.TaskState.fromString(status);
        }
        return taskState != null ? taskState : com.z3rd0.common.model.TaskState.PENDING;
    }
    public void setTaskState(com.z3rd0.common.model.TaskState taskState) {
        this.taskState = taskState;
        this.status = taskState.name();
        if (com.z3rd0.common.model.TaskState.COMPLETED == taskState && completedAt == null) {
            completedAt = LocalDateTime.now();
        }
    }
    public com.z3rd0.common.model.TaskState moveToNextState() {
        com.z3rd0.common.model.TaskState currentState = getTaskState();
        com.z3rd0.common.model.TaskState nextState = currentState.next();
        setTaskState(nextState);
        return nextState;
    }
    public void markAsFailed(String errorMsg) {
        setTaskState(com.z3rd0.common.model.TaskState.FAILED);
        this.errorMessage = errorMsg;
    }

    public void markAsSkipped(String skipReason) {
        setTaskState(com.z3rd0.common.model.TaskState.SKIPPED);
        this.errorMessage = skipReason;
        this.executedAt = LocalDateTime.now();
    }
    @JsonIgnore
    public String getStatusDisplayName() {
        return getTaskState().getDisplayName();
    }

    /**
     * 判断任务今天是否已执行
     * @return true如果今天已执行，false如果今天未执行
     */
    @JsonIgnore
    public boolean isExecutedToday() {
        if (executedAt == null) {
            return false;
        }
        LocalDateTime today = LocalDateTime.now();
        return executedAt.toLocalDate().equals(today.toLocalDate());
    }

    /**
     * 获取基于执行状态的显示状态
     * @return 状态显示名称
     */
    @JsonIgnore
    public String getExecutionStatusDisplayName() {
        return isExecutedToday() ? "已完成" : "未完成";
    }

    /**
     * 更新任务执行信息
     * @param filterRange 筛选范围
     */
    public void updateExecutionInfo(String filterRange) {
        this.filterRange = filterRange;
        this.executedAt = LocalDateTime.now();
    }

    // Priority字段的Getter和Setter方法
    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    // 定时任务相关的Getter和Setter方法
    public Boolean getScheduleEnabled() {
        return scheduleEnabled;
    }

    public void setScheduleEnabled(Boolean scheduleEnabled) {
        this.scheduleEnabled = scheduleEnabled;
        // 当启用定时任务时，自动设置状态为ACTIVE
        if (scheduleEnabled != null && scheduleEnabled) {
            this.scheduleStatus = "ACTIVE";
        } else {
            this.scheduleStatus = "INACTIVE";
        }
    }

    public String getScheduleTime() {
        return scheduleTime;
    }

    public void setScheduleTime(String scheduleTime) {
        this.scheduleTime = scheduleTime;
    }

    public String getScheduleCron() {
        return scheduleCron;
    }

    public void setScheduleCron(String scheduleCron) {
        this.scheduleCron = scheduleCron;
    }

    public String getTimeWindowStrategy() {
        return timeWindowStrategy;
    }

    public void setTimeWindowStrategy(String timeWindowStrategy) {
        this.timeWindowStrategy = timeWindowStrategy;
    }

    public LocalDateTime getLastScheduledAt() {
        return lastScheduledAt;
    }

    public void setLastScheduledAt(LocalDateTime lastScheduledAt) {
        this.lastScheduledAt = lastScheduledAt;
    }

    public String getScheduleFrequency() {
        return scheduleFrequency;
    }

    public void setScheduleFrequency(String scheduleFrequency) {
        this.scheduleFrequency = scheduleFrequency;
    }

    public String getTimeWindowConfig() {
        return timeWindowConfig;
    }

    public void setTimeWindowConfig(String timeWindowConfig) {
        this.timeWindowConfig = timeWindowConfig;
    }

    public String getScheduleStatus() {
        return scheduleStatus;
    }

    public void setScheduleStatus(String scheduleStatus) {
        this.scheduleStatus = scheduleStatus;
    }

    /**
     * 判断任务是否应该在指定时间执行
     * @param currentTime 当前时间
     * @return true如果应该执行，false如果不应该执行
     */
    @JsonIgnore
    public boolean shouldExecuteAt(LocalDateTime currentTime) {
        if (!Boolean.TRUE.equals(scheduleEnabled) || scheduleTime == null) {
            return false;
        }

        // 解析调度时间
        try {
            String[] timeParts = scheduleTime.split(":");
            int hour = Integer.parseInt(timeParts[0]);
            int minute = Integer.parseInt(timeParts[1]);

            // 检查当前时间是否匹配调度时间
            return currentTime.getHour() == hour && currentTime.getMinute() == minute;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断任务今天是否已经定时执行过
     * @return true如果今天已执行，false如果今天未执行
     */
    @JsonIgnore
    public boolean isScheduledExecutedToday() {
        if (lastScheduledAt == null) {
            return false;
        }
        LocalDateTime today = LocalDateTime.now();
        return lastScheduledAt.toLocalDate().equals(today.toLocalDate());
    }

    /**
     * 更新定时执行信息
     */
    public void updateScheduledExecution() {
        this.lastScheduledAt = LocalDateTime.now();
        this.scheduleStatus = "ACTIVE";
    }

    /**
     * 暂停定时任务
     */
    public void pauseSchedule() {
        this.scheduleStatus = "PAUSED";
    }

    /**
     * 恢复定时任务
     */
    public void resumeSchedule() {
        if (Boolean.TRUE.equals(scheduleEnabled)) {
            this.scheduleStatus = "ACTIVE";
        }
    }
}