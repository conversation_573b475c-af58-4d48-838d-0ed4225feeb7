package com.z3rd0.common.model;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * IP资产实体类
 * 用于管理和统计IP地址相关的资产信息
 */
@Data
@Entity
@Table(name = "ip_assets",
       uniqueConstraints = {
           @UniqueConstraint(name = "uk_ip_address", columnNames = "ip_address")
       },
       indexes = {
           @Index(name = "idx_ip_address", columnList = "ip_address"),
           @Index(name = "idx_status", columnList = "status"),
           @Index(name = "idx_first_discovered", columnList = "first_discovered"),
           @Index(name = "idx_last_seen", columnList = "last_seen"),
           @Index(name = "idx_asset_count", columnList = "asset_count")
       })
public class IpAsset {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * IP地址（IPv4或IPv6）
     */
    @Column(name = "ip_address", length = 45, nullable = false, unique = true)
    private String ipAddress;
    
    /**
     * 首次发现时间
     */
    @Column(name = "first_discovered", nullable = false)
    private LocalDateTime firstDiscovered;
    
    /**
     * 最后发现时间
     */
    @Column(name = "last_seen", nullable = false)
    private LocalDateTime lastSeen;
    
    /**
     * 关联资产数量
     */
    @Column(name = "asset_count", columnDefinition = "INT DEFAULT 0")
    private Integer assetCount = 0;
    
    /**
     * IP状态：ACTIVE(活跃)、INACTIVE(非活跃)
     */
    @Column(name = "status", length = 20, columnDefinition = "VARCHAR(20) DEFAULT 'ACTIVE'")
    private String status = "ACTIVE";
    
    /**
     * 地理位置信息（从关联资产中聚合）
     */
    @Column(name = "location_country", length = 100)
    private String locationCountry;
    
    @Column(name = "location_province", length = 100)
    private String locationProvince;
    
    @Column(name = "location_city", length = 100)
    private String locationCity;
    
    /**
     * ISP信息
     */
    @Column(name = "isp", length = 200)
    private String isp;
    
    /**
     * ASN信息
     */
    @Column(name = "asn")
    private Integer asn;
    
    /**
     * 组织信息
     */
    @Column(name = "organization", length = 200)
    private String organization;
    
    /**
     * 是否为IPv6地址
     */
    @Column(name = "is_ipv6", columnDefinition = "BOOLEAN DEFAULT FALSE")
    private Boolean isIpv6 = false;
    
    /**
     * 端口列表（JSON格式存储常见端口）
     */
    @Column(name = "common_ports", columnDefinition = "JSON")
    private String commonPorts;
    
    /**
     * 域名列表（JSON格式存储关联域名）
     */
    @Column(name = "associated_domains", columnDefinition = "JSON")
    private String associatedDomains;
    
    /**
     * 备注信息
     */
    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        if (createdAt == null) {
            createdAt = now;
        }
        if (updatedAt == null) {
            updatedAt = now;
        }
        if (firstDiscovered == null) {
            firstDiscovered = now;
        }
        if (lastSeen == null) {
            lastSeen = now;
        }
        if (assetCount == null) {
            assetCount = 0;
        }
        if (isIpv6 == null) {
            isIpv6 = ipAddress != null && ipAddress.contains(":");
        }
        if (status == null) {
            status = "ACTIVE";
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 更新最后发现时间
     */
    public void updateLastSeen() {
        this.lastSeen = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 增加资产数量
     */
    public void incrementAssetCount() {
        this.assetCount = (this.assetCount == null ? 0 : this.assetCount) + 1;
        updateLastSeen();
    }
    
    /**
     * 减少资产数量
     */
    public void decrementAssetCount() {
        this.assetCount = Math.max(0, (this.assetCount == null ? 0 : this.assetCount) - 1);
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 判断IP是否活跃（最近30天内有发现记录）
     */
    public boolean isActive() {
        if (lastSeen == null) {
            return false;
        }
        return lastSeen.isAfter(LocalDateTime.now().minusDays(30));
    }
    
    /**
     * 自动更新状态
     */
    public void updateStatus() {
        this.status = isActive() ? "ACTIVE" : "INACTIVE";
        this.updatedAt = LocalDateTime.now();
    }
}
