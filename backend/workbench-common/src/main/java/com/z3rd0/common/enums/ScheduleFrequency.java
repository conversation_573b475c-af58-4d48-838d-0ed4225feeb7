package com.z3rd0.common.enums;

/**
 * 定时任务执行频率枚举
 */
public enum ScheduleFrequency {
    
    /**
     * 每日执行 - 每天在指定时间执行一次
     */
    DAILY("DAILY", "每日执行", "每天在指定时间执行一次", 1),
    
    /**
     * 每三日执行 - 每三天在指定时间执行一次
     */
    EVERY_THREE_DAYS("EVERY_THREE_DAYS", "每三日执行", "每三天在指定时间执行一次", 3);
    
    private final String code;
    private final String name;
    private final String description;
    private final int intervalDays;
    
    ScheduleFrequency(String code, String name, String description, int intervalDays) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.intervalDays = intervalDays;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public int getIntervalDays() {
        return intervalDays;
    }
    
    /**
     * 根据代码获取枚举值
     * @param code 频率代码
     * @return 对应的枚举值
     * @throws IllegalArgumentException 如果代码不存在
     */
    public static ScheduleFrequency fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return DAILY; // 默认使用每日执行
        }
        
        for (ScheduleFrequency frequency : values()) {
            if (frequency.code.equalsIgnoreCase(code.trim())) {
                return frequency;
            }
        }
        
        throw new IllegalArgumentException("未知的执行频率代码: " + code);
    }
    
    /**
     * 检查代码是否有效
     * @param code 频率代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        try {
            fromCode(code);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    @Override
    public String toString() {
        return String.format("%s(%s)", name, code);
    }
}
