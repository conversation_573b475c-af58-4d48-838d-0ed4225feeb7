# Workbench 分布式任务系统

本项目是一个现代化分布式任务系统，采用前后端分离架构，支持多业务模块协作与自动化采集。主要用于网络资产发现、数据采集和自动化任务处理。

## 📁 项目结构

```
Workbench/                    # 项目根目录
├── backend/                  # 后端工程（Maven多模块）
│   ├── workbench-system/     # 主控与API服务模块
│   │   ├── src/main/java/    # Java源码
│   │   │   └── com/z3rd0/system/
│   │   │       ├── controller/    # REST API控制器
│   │   │       ├── service/       # 业务逻辑服务
│   │   │       ├── config/        # 配置类
│   │   │       └── constants/     # 常量定义
│   │   ├── src/main/resources/    # 配置文件
│   │   └── pom.xml               # Maven配置
│   ├── workbench-scanner/    # 采集与自动化模块
│   │   ├── src/main/java/    # Java源码
│   │   │   └── com/z3rd0/workbench/
│   │   │       ├── service/       # 任务处理服务
│   │   │       ├── controller/    # 健康检查等API
│   │   │       ├── config/        # RabbitMQ等配置
│   │   │       └── utils/         # 工具类
│   │   ├── src/main/resources/    # 配置文件
│   │   └── pom.xml               # Maven配置
│   ├── workbench-common/     # 公共模块
│   │   ├── src/main/java/    # 共享实体和工具
│   │   │   └── com/z3rd0/common/
│   │   │       ├── model/         # 数据模型
│   │   │       └── utils/         # 通用工具
│   │   └── pom.xml               # Maven配置
│   └── pom.xml               # 父级Maven配置
├── workbench-web/            # 前端工程
│   ├── src/                  # Vue3源码
│   │   ├── api/              # API接口定义
│   │   ├── components/       # Vue组件
│   │   ├── views/            # 页面视图
│   │   ├── router/           # 路由配置
│   │   ├── store/            # Pinia状态管理
│   │   └── utils/            # 工具函数
│   ├── public/               # 静态资源
│   ├── build/                # 构建配置
│   ├── types/                # TypeScript类型定义
│   ├── package.json          # 前端依赖配置
│   ├── vite.config.ts        # Vite构建配置
│   └── tsconfig.json         # TypeScript配置
├── scripts/                  # 部署脚本
│   ├── quick-deploy.bat      # 快速部署脚本
│   └── recreate-database.sql # 数据库重建脚本
├── StartWorkbench.bat        # 项目启动器（批处理）
├── StartWorkbench.py         # 项目启动器（Python GUI）
├── start-web.bat             # 前端快速启动脚本
└── README.md                 # 项目说明文档
```

## 🏗️ 系统架构

### 技术栈概览

**前端技术栈**
- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **样式框架**: Tailwind CSS
- **模板**: Pure Admin
- **路由**: Vue Router 4

**后端技术栈**
- **框架**: Spring Boot 3.5.3
- **数据库**: MySQL 8.0
- **ORM**: Spring Data JPA + Hibernate
- **消息队列**: RabbitMQ
- **自动化**: Playwright
- **构建工具**: Maven
- **Java版本**: JDK 17

### 模块架构

#### 🎯 workbench-system（主控服务）
- **端口**: 38889
- **职责**:
  - 提供统一的REST API接口
  - 任务创建、查询、管理
  - 任务分发到消息队列
  - 搜索结果数据管理
- **主要组件**:
  - `TaskController`: 任务管理API
  - `CompatibilityController`: 兼容性API
  - `TaskService`: 任务业务逻辑
  - `TaskPublisher`: 消息队列发布者

#### 🔍 workbench-scanner（采集服务）
- **端口**: 38888
- **职责**:
  - 消费RabbitMQ任务消息
  - 执行网络资产扫描和数据采集
  - Playwright自动化浏览器操作
  - 任务状态更新和结果存储
- **主要组件**:
  - `TaskConsumer`: 消息队列消费者
  - `BrowserManager`: 浏览器管理器
  - `TaskProcessor`: 任务处理器
  - `HealthController`: 健康检查

#### 📦 workbench-common（公共模块）
- **职责**:
  - 共享数据模型定义
  - 通用工具类和常量
  - 跨模块复用组件
- **主要实体**:
  - `Task`: 任务实体
  - `SearchResult`: 搜索结果实体
  - `MessageUtils`: 消息工具类

#### 🌐 workbench-web（前端应用）
- **端口**: 5173（开发环境）
- **职责**:
  - 任务管理界面
  - 数据查询和展示
  - 系统监控面板
  - 用户交互界面
- **主要功能**:
  - 任务创建和管理
  - 搜索结果查看
  - 实时状态监控
  - 数据导出功能

## 🚀 快速启动

### 环境要求

**基础环境**
- **Java**: JDK 17+ (推荐使用OpenJDK 17)
- **Node.js**: 18.18.0+ 或 20.9.0+ 或 22.0.0+
- **pnpm**: 9.0+ (必须使用pnpm，不支持npm/yarn)
- **MySQL**: 8.0+ (需要支持utf8mb4字符集)
- **RabbitMQ**: 3.8+ (需要启用Management插件)

**Python环境（可选，用于GUI启动器）**
- **Python**: 3.8+
- **依赖包**: ttkbootstrap, psutil

**开发工具推荐**
- **IDE**: IntelliJ IDEA (后端) + VS Code (前端)
- **数据库工具**: MySQL Workbench 或 Navicat
- **API测试**: Postman 或 Apifox

### 启动方式

#### 方式一：GUI启动器（推荐）
```bash
# 在项目根目录执行
StartWorkbench.bat
```
或直接运行：
```bash
python StartWorkbench.py
```

**GUI启动器功能**：
- 一键构建所有模块
- 独立启动/停止各个服务
- 实时查看各模块日志
- 清理构建文件
- 进程管理和监控

#### 方式二：手动启动

**1. 启动后端服务**
```bash
# 启动主控服务（必须）
cd backend/workbench-system
mvn spring-boot:run

# 启动采集服务（必须）
cd backend/workbench-scanner
mvn spring-boot:run
```

**2. 启动前端服务**
```bash
# 方式1：使用快速启动脚本
start-web.bat

# 方式2：手动启动
cd workbench-web
pnpm install  # 首次运行需要安装依赖
pnpm dev
```

#### 方式三：快速部署脚本
```bash
# 一键构建并启动所有服务
scripts/quick-deploy.bat
```

### 环境搭建详细步骤

#### 1. 安装基础环境

**安装Java 17**
```bash
# Windows (使用Chocolatey)
choco install openjdk17

# macOS (使用Homebrew)
brew install openjdk@17

# Linux (Ubuntu/Debian)
sudo apt update
sudo apt install openjdk-17-jdk

# 验证安装
java -version
```

**安装Node.js和pnpm**
```bash
# 安装Node.js (推荐使用nvm)
# Windows: 下载安装包 https://nodejs.org/
# macOS/Linux: 使用nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 20
nvm use 20

# 安装pnpm
npm install -g pnpm@latest

# 验证安装
node -v
pnpm -v
```

**安装MySQL 8.0**
```bash
# Windows: 下载MySQL Installer
# https://dev.mysql.com/downloads/installer/

# macOS (使用Homebrew)
brew install mysql
brew services start mysql

# Linux (Ubuntu/Debian)
sudo apt update
sudo apt install mysql-server
sudo systemctl start mysql
sudo systemctl enable mysql

# 创建数据库
mysql -u root -p
CREATE DATABASE workbench CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

**安装RabbitMQ**
```bash
# Windows: 下载安装包
# https://www.rabbitmq.com/install-windows.html

# macOS (使用Homebrew)
brew install rabbitmq
brew services start rabbitmq

# Linux (Ubuntu/Debian)
sudo apt update
sudo apt install rabbitmq-server
sudo systemctl start rabbitmq-server
sudo systemctl enable rabbitmq-server

# 启用管理插件
sudo rabbitmq-plugins enable rabbitmq_management

# 创建管理员用户
sudo rabbitmqctl add_user admin your_password
sudo rabbitmqctl set_user_tags admin administrator
sudo rabbitmqctl set_permissions -p / admin ".*" ".*" ".*"
```

#### 2. 配置环境变量

**创建环境配置文件**
```bash
# 在项目根目录创建 .env 文件
cp .env.example .env
```

**编辑配置文件**
```bash
# 数据库配置
DB_URL=************************************************************************************************************************************
DB_USERNAME=root
DB_PASSWORD=your_mysql_password

# RabbitMQ配置
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=admin
RABBITMQ_PASSWORD=your_rabbitmq_password

# 爬虫账号配置（可选）
CRAWLER_USERNAME=your_crawler_username
CRAWLER_PASSWORD=your_crawler_password
```

### 服务访问地址

- **前端应用**: http://localhost:8848 (开发环境)
- **主控API**: http://localhost:38889
- **采集服务**: http://localhost:38888
- **健康检查**: http://localhost:38888/health/status
- **RabbitMQ管理界面**: http://localhost:15672 (admin/your_password)

## 🔧 配置说明

### 配置说明

#### 数据库配置

**配置文件位置**
- System模块: `backend/workbench-system/src/main/resources/application.yml`
- Scanner模块: `backend/workbench-scanner/src/main/resources/application.yml`

**实际配置示例**
```yaml
spring:
  datasource:
    url: ${DB_URL:************************************************************************************************************************************}
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:your_password}
    driver-class-name: com.mysql.cj.jdbc.Driver

  jpa:
    hibernate:
      ddl-auto: validate  # System模块使用validate，Scanner模块使用update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
```

**数据库初始化**
```bash
# 方式1: 使用提供的SQL脚本（开发环境）
mysql -u root -p workbench < scripts/recreate-database.sql

# 方式2: 手动创建数据库
mysql -u root -p
CREATE DATABASE workbench CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE workbench;
# 启动应用后会自动创建表结构
```

#### RabbitMQ配置

**连接配置**
```yaml
spring:
  rabbitmq:
    host: ${RABBITMQ_HOST:localhost}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:admin}
    password: ${RABBITMQ_PASSWORD:your_password}
    listener:
      simple:
        concurrency: 1
        max-concurrency: 1
        prefetch: 1
        retry:
          enabled: true
          initial-interval: 5000
          max-attempts: 3
          multiplier: 1.5
```

**队列配置**
- `task_queue`: 主任务队列，用于任务分发
- `task_update_queue`: 任务状态更新队列
- `task_dead_letter_queue`: 死信队列，处理失败任务

**验证RabbitMQ配置**
```bash
# 检查RabbitMQ状态
sudo rabbitmqctl status

# 查看队列信息
sudo rabbitmqctl list_queues

# 访问管理界面
# http://localhost:15672 (用户名: admin, 密码: 你设置的密码)
```

#### 前端配置

**环境变量配置**
```bash
# 在workbench-web目录下创建.env.local文件
cd workbench-web
cp .env.example .env.local
```

**编辑.env.local文件**
```bash
# 开发环境端口
VITE_PORT=8848

# API代理配置（开发环境）
VITE_API_BASE_URL=http://localhost:38889

# 高德地图API密钥（可选）
VITE_AMAP_KEY=your_amap_api_key
```

**Vite代理配置**
前端已配置Vite代理，所有`/api`请求自动转发到后端：
```typescript
// vite.config.ts
server: {
  port: 8848,  // 实际开发端口
  host: "0.0.0.0",
  proxy: {
    '/api': {
      target: 'http://localhost:38889',  // System主控服务地址
      changeOrigin: true,
      rewrite: path => path.replace(/^\/api/, "/api")
    }
  }
}
```

**生产环境配置**
```bash
# 构建生产版本
pnpm build

# 生产环境通过Nginx反向代理，无需修改前端配置
```

## 📊 核心功能

### 任务管理
- **任务创建**: 支持自定义搜索规则和时间范围
- **任务调度**: 基于RabbitMQ的异步任务处理
- **状态跟踪**: 实时任务状态监控和更新
- **错误处理**: 自动重试机制和死信队列

### 数据采集
- **网络扫描**: 基于规则的网络资产发现
- **自动化操作**: Playwright浏览器自动化
- **数据存储**: 结构化存储搜索结果
- **去重处理**: 基于多字段的智能去重

### 系统监控
- **健康检查**: 各服务状态实时监控
- **性能指标**: 任务执行统计和性能分析
- **日志管理**: 分模块的详细日志记录
- **异常告警**: 异常情况自动检测和通知

## 🏭 生产部署

### Docker部署（推荐）

#### 快速部署
```bash
# 使用提供的部署脚本
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

#### 手动Docker部署
```bash
# 1. 构建前端
cd workbench-web
pnpm install
pnpm build
cd ..

# 2. 构建后端
cd backend
mvn clean package -DskipTests
cd ..

# 3. 构建Docker镜像
docker build -t workbench-web:latest ./workbench-web
docker build -t workbench-system:latest ./backend/workbench-system
docker build -t workbench-scanner:latest ./backend/workbench-scanner

# 4. 创建docker-compose.yml文件
cat > docker-compose.yml << EOF
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: your_password
      MYSQL_DATABASE: workbench
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  rabbitmq:
    image: rabbitmq:3-management
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: your_password
    ports:
      - "5672:5672"
      - "15672:15672"

  workbench-system:
    image: workbench-system:latest
    depends_on:
      - mysql
      - rabbitmq
    environment:
      DB_URL: ***************************************************************************
      DB_USERNAME: root
      DB_PASSWORD: your_password
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_USERNAME: admin
      RABBITMQ_PASSWORD: your_password
    ports:
      - "38889:38889"

  workbench-scanner:
    image: workbench-scanner:latest
    depends_on:
      - mysql
      - rabbitmq
    environment:
      DB_URL: ***************************************************************************
      DB_USERNAME: root
      DB_PASSWORD: your_password
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_USERNAME: admin
      RABBITMQ_PASSWORD: your_password
    ports:
      - "38888:38888"

  workbench-web:
    image: workbench-web:latest
    depends_on:
      - workbench-system
    ports:
      - "80:80"

volumes:
  mysql_data:
EOF

# 5. 启动所有服务
docker-compose up -d
```

### 传统部署

#### 后端部署
```bash
# 1. 打包应用
cd backend
mvn clean package -DskipTests

# 2. 部署System模块
java -jar workbench-system/target/workbench-system-0.0.1-SNAPSHOT.jar \
  --spring.profiles.active=prod \
  --server.port=38889

# 3. 部署Scanner模块
java -jar workbench-scanner/target/workbench-scanner-0.0.1-SNAPSHOT.jar \
  --spring.profiles.active=prod \
  --server.port=38888
```

#### 前端部署
```bash
# 1. 构建前端
cd workbench-web
pnpm install
pnpm build

# 2. 配置Nginx
cat > /etc/nginx/sites-available/workbench << EOF
server {
    listen 80;
    server_name your_domain.com;
    root /var/www/workbench;
    index index.html;

    # 前端静态文件
    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:38889/api/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

# 3. 启用站点
ln -s /etc/nginx/sites-available/workbench /etc/nginx/sites-enabled/
nginx -t
systemctl reload nginx

# 4. 复制构建文件
cp -r dist/* /var/www/workbench/
```

### 负载均衡配置
- 前端：通过Nginx实现静态资源CDN和负载均衡
- 后端：支持多实例部署，通过注册中心实现服务发现
- 数据库：支持读写分离和主从复制
- 消息队列：支持集群模式提高可用性

## 🛠️ 开发指南

### 开发环境配置

#### IDE配置推荐

**IntelliJ IDEA (后端开发)**
```bash
# 推荐插件
- Lombok Plugin
- Spring Boot Helper
- Maven Helper
- Database Navigator
- Rainbow Brackets

# JVM参数配置
-Xmx2g -Xms1g
-Dfile.encoding=UTF-8
-Dspring.profiles.active=dev
```

**VS Code (前端开发)**
```json
// .vscode/settings.json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode"
}

// 推荐扩展
- Vue Language Features (Volar)
- TypeScript Vue Plugin (Volar)
- ESLint
- Prettier
- Tailwind CSS IntelliSense
```

#### 代码规范

**后端代码规范**
```java
// 使用Lombok简化代码
@Data
@Entity
@Table(name = "tasks")
public class Task {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 100)
    private String name;

    // 使用枚举定义状态
    @Enumerated(EnumType.STRING)
    private TaskStatus status = TaskStatus.PENDING;
}

// 统一异常处理
@RestControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleException(Exception e) {
        log.error("Unexpected error", e);
        return ResponseEntity.status(500)
            .body(new ErrorResponse("Internal server error"));
    }
}
```

**前端代码规范**
```typescript
// 使用组合式API
<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import type { Task } from '@/types/task'

// 响应式数据
const loading = ref(false)
const taskList = ref<Task[]>([])

// 计算属性
const completedTasks = computed(() =>
  taskList.value.filter(task => task.status === 'COMPLETED')
)

// 生命周期
onMounted(() => {
  fetchTasks()
})
</script>

<!-- 使用Tailwind CSS -->
<template>
  <div class="p-4 bg-white rounded-lg shadow-md">
    <h2 class="text-xl font-semibold mb-4">任务列表</h2>
    <!-- 组件内容 -->
  </div>
</template>
```

#### 调试技巧

**后端调试**
```bash
# 启用调试模式
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005"

# 查看SQL执行
logging:
  level:
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# 性能监控
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
```

**前端调试**
```bash
# 启用Vue DevTools
npm install -g @vue/devtools

# 查看构建分析
pnpm build --report

# 调试网络请求
# 在浏览器开发者工具中查看Network标签页
```

### API接口文档

#### 任务管理API

**创建任务**
```http
POST /api/tasks
Content-Type: application/json

{
  "name": "任务名称",
  "rule": "搜索规则",
  "timeRange": "时间范围"
}
```

**查询任务列表**
```http
GET /api/tasks?page=0&size=20&sort=id,desc
```

**获取任务详情**
```http
GET /api/tasks/{id}
```

**查询搜索结果**
```http
GET /api/search-results?taskName=任务名称&page=0&size=20
```

#### 兼容性API

**发布搜索任务**
```http
POST /api/publish-search-task
Content-Type: application/json

{
  "name": "任务名称",
  "rule": "搜索规则",
  "timeRange": "时间范围"
}
```

### 数据模型

#### Task（任务实体）
```java
public class Task {
    private Long id;              // 任务ID
    private String name;          // 任务名称
    private String rule;          // 搜索规则
    private String filterRange;   // 筛选范围
    private String timeRange;     // 时间范围
    private LocalDateTime executedAt; // 执行时间
    private String status;        // 任务状态
    private String errorMessage;  // 错误信息
    private Integer retryCount;   // 重试次数
}
```

#### SearchResult（搜索结果实体）
```java
public class SearchResult {
    private Long id;              // 结果ID
    private String originalId;    // 原始ID
    private String taskName;      // 任务名称
    private String ip;            // IP地址
    private String port;          // 端口
    private String domain;        // 域名
    private String hostname;      // 主机名
    private String org;           // 组织
    private Integer asn;          // ASN号
    private String server;        // 服务器信息
    private Integer statusCode;   // HTTP状态码
    private String title;         // 页面标题
    private String icpLicence;    // ICP备案号
    private String locationCountry; // 国家
    private String locationProvince; // 省份
    private String locationCity;  // 城市
    // ... 更多字段
}
```

### 消息队列

#### 队列配置
- **task_queue**: 主任务队列，用于任务分发
- **task_update_queue**: 任务状态更新队列
- **task_dead_letter_queue**: 死信队列，处理失败任务

#### 消息格式
```json
{
  "taskId": 123,
  "name": "任务名称",
  "rule": "搜索规则",
  "timeRange": "时间范围",
  "timestamp": "2025-01-01T00:00:00Z"
}
```

### 扩展开发

#### 添加新的数据采集器
1. 在`workbench-scanner`模块创建采集器类
2. 实现`DataCollector`接口
3. 在`TaskProcessor`中注册新采集器
4. 更新配置文件和文档

#### 添加新的API接口
1. 在`workbench-system`模块创建Controller
2. 定义Service业务逻辑
3. 更新API文档
4. 编写单元测试

## ❓ 常见问题

### 环境问题

**Q: Maven依赖下载慢怎么办？**
A: 配置阿里云镜像源，在`~/.m2/settings.xml`中添加：
```xml
<settings>
  <mirrors>
    <mirror>
      <id>aliyun-central</id>
      <mirrorOf>central</mirrorOf>
      <name>Aliyun Central</name>
      <url>https://maven.aliyun.com/repository/central</url>
    </mirror>
    <mirror>
      <id>aliyun-public</id>
      <mirrorOf>public</mirrorOf>
      <name>Aliyun Public</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </mirror>
  </mirrors>
</settings>
```

**Q: pnpm安装依赖失败？**
A: 解决方案：
```bash
# 清理缓存
pnpm store prune

# 使用国内镜像源
pnpm config set registry https://registry.npmmirror.com

# 重新安装
rm -rf node_modules pnpm-lock.yaml
pnpm install
```

**Q: 数据库连接失败？**
A: 检查以下配置：
1. **MySQL服务状态**
   ```bash
   # Windows
   net start mysql80

   # Linux/macOS
   sudo systemctl status mysql
   brew services list | grep mysql
   ```

2. **数据库和用户权限**
   ```sql
   -- 创建数据库
   CREATE DATABASE workbench CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

   -- 检查用户权限
   SHOW GRANTS FOR 'root'@'localhost';

   -- 如需创建专用用户
   CREATE USER 'workbench'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON workbench.* TO 'workbench'@'localhost';
   FLUSH PRIVILEGES;
   ```

3. **连接参数检查**
   - 确认端口号（默认3306）
   - 检查防火墙设置
   - 验证字符集配置

**Q: RabbitMQ连接失败？**
A: 排查步骤：
1. **服务状态检查**
   ```bash
   # 检查服务状态
   sudo systemctl status rabbitmq-server  # Linux
   brew services list | grep rabbitmq     # macOS

   # 检查端口占用
   netstat -an | grep 5672
   ```

2. **用户和权限配置**
   ```bash
   # 查看用户列表
   sudo rabbitmqctl list_users

   # 创建管理员用户
   sudo rabbitmqctl add_user admin your_password
   sudo rabbitmqctl set_user_tags admin administrator
   sudo rabbitmqctl set_permissions -p / admin ".*" ".*" ".*"

   # 启用管理插件
   sudo rabbitmq-plugins enable rabbitmq_management
   ```

3. **网络连接测试**
   ```bash
   # 测试连接
   telnet localhost 5672

   # 访问管理界面
   curl http://localhost:15672
   ```

### 功能问题

**Q: 任务执行失败？**
A: 排查步骤：
1. **查看日志文件**
   ```bash
   # System模块日志
   tail -f logs/workbench-system.log

   # Scanner模块日志
   tail -f logs/workbench-scanner.log

   # 查看错误日志
   grep ERROR logs/*.log
   ```

2. **检查任务状态**
   ```bash
   # 通过API查看任务状态
   curl http://localhost:38889/api/tasks

   # 检查RabbitMQ队列
   sudo rabbitmqctl list_queues
   ```

3. **常见错误解决**
   - **数据库连接超时**: 检查数据库连接池配置
   - **内存不足**: 调整JVM参数 `-Xmx2g -Xms1g`
   - **网络超时**: 检查网络连接和防火墙设置

**Q: 前端页面空白或加载失败？**
A: 解决方案：
1. **检查后端服务**
   ```bash
   # 测试API连接
   curl http://localhost:38889/api/tasks
   curl http://localhost:38888/health/status
   ```

2. **检查前端构建**
   ```bash
   # 重新安装依赖
   cd workbench-web
   rm -rf node_modules pnpm-lock.yaml
   pnpm install

   # 重新启动开发服务器
   pnpm dev
   ```

3. **浏览器调试**
   - 打开浏览器开发者工具 (F12)
   - 查看Console标签页的错误信息
   - 检查Network标签页的请求状态
   - 清除浏览器缓存和Cookie

**Q: Playwright自动化失败？**
A: 解决方案：
1. **安装浏览器驱动**
   ```bash
   # 在Scanner模块目录下执行
   cd backend/workbench-scanner
   mvn exec:java -Dexec.mainClass="com.microsoft.playwright.CLI" -Dexec.args="install"
   ```

2. **系统资源检查**
   ```bash
   # 检查内存使用
   free -h

   # 检查磁盘空间
   df -h

   # 调整JVM内存参数
   export JAVA_OPTS="-Xmx2g -Xms1g"
   ```

3. **网络和权限检查**
   - 确保网络连接正常
   - 检查防火墙设置
   - 验证目标网站可访问性
   - 确认爬虫账号有效性

**Q: GUI启动器无法启动？**
A: 解决方案：
```bash
# 安装Python依赖
pip install ttkbootstrap psutil

# 如果是Windows系统，可能需要安装Visual C++运行库
# 下载地址：https://aka.ms/vs/17/release/vc_redist.x64.exe

# 检查Python版本
python --version  # 需要3.8+

# 直接运行Python脚本
python StartWorkbench.py
```

### 性能问题

**Q: 任务处理速度慢？**
A: 性能优化方案：
1. **RabbitMQ优化**
   ```yaml
   # application.yml
   spring:
     rabbitmq:
       listener:
         simple:
           concurrency: 3          # 增加并发消费者
           max-concurrency: 10     # 最大并发数
           prefetch: 5             # 预取消息数
   ```

2. **数据库优化**
   ```sql
   -- 添加索引
   CREATE INDEX idx_task_status ON tasks(status);
   CREATE INDEX idx_search_result_task ON search_results(task_name);
   CREATE INDEX idx_search_result_ip ON search_results(ip);

   -- 查看慢查询
   SHOW VARIABLES LIKE 'slow_query_log';
   SET GLOBAL slow_query_log = 'ON';
   ```

3. **JVM参数调优**
   ```bash
   # 启动参数示例
   java -jar -Xmx4g -Xms2g -XX:+UseG1GC \
        -XX:MaxGCPauseMillis=200 \
        -XX:+HeapDumpOnOutOfMemoryError \
        workbench-scanner.jar
   ```

4. **应用层优化**
   - 启用数据库连接池
   - 使用异步处理
   - 实现结果缓存
   - 批量数据操作

**Q: 内存占用过高？**
A: 内存优化方案：
1. **监控内存使用**
   ```bash
   # 查看JVM内存使用
   jstat -gc <pid>

   # 生成内存快照
   jmap -dump:format=b,file=heap.hprof <pid>

   # 使用VisualVM或MAT分析内存
   ```

2. **配置优化**
   ```yaml
   # application.yml
   spring:
     datasource:
       hikari:
         maximum-pool-size: 10      # 限制连接池大小
         minimum-idle: 5
         connection-timeout: 30000
         idle-timeout: 600000

   # 浏览器资源限制
   crawler:
     browser:
       max-memory-mb: 1024          # 限制浏览器内存
       max-runtime-minutes: 120     # 限制运行时间
   ```

3. **定期清理**
   ```sql
   -- 清理过期数据
   DELETE FROM search_results WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

   -- 清理失败任务
   DELETE FROM tasks WHERE status = 'FAILED' AND created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
   ```

**Q: 前端页面加载慢？**
A: 前端优化方案：
1. **构建优化**
   ```bash
   # 启用压缩
   pnpm build --mode production

   # 分析构建包大小
   pnpm build --report
   ```

2. **CDN配置**
   ```typescript
   // vite.config.ts
   import { cdn } from './build/cdn'

   export default {
     plugins: [
       // 使用CDN加载大型依赖
       cdn(['vue', 'element-plus', 'echarts'])
     ]
   }
   ```

3. **缓存策略**
   ```nginx
   # Nginx配置
   location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
       expires 1y;
       add_header Cache-Control "public, immutable";
   }
   ```

## 📈 监控运维

### 健康检查
```bash
# 检查系统服务状态
curl http://localhost:38888/health/status

# 检查主控服务
curl http://localhost:38889/actuator/health

# 检查前端服务
curl http://localhost:5173
```

### 日志监控
```bash
# 实时查看系统日志
tail -f backend/workbench-system/logs/workbench-system.log

# 实时查看采集日志
tail -f backend/workbench-scanner/logs/workbench-scanner.log

# 查看错误日志
grep ERROR backend/*/logs/*.log
```

### 性能监控
- JVM监控：使用JVisualVM或JProfiler
- 数据库监控：MySQL Performance Schema
- 消息队列监控：RabbitMQ Management Plugin
- 应用监控：集成Micrometer + Prometheus

## 🔄 版本更新

### 当前版本：v1.2.0 (2025-07-28)

#### ✅ 已完成功能
- **核心功能**
  - ✅ 分布式任务管理系统
  - ✅ 网络资产扫描和数据采集
  - ✅ 基于RabbitMQ的异步任务处理
  - ✅ MySQL数据存储和查询
  - ✅ Playwright浏览器自动化

- **用户界面**
  - ✅ Vue3 + Element Plus管理界面
  - ✅ 实时任务状态监控
  - ✅ 数据查询和导出功能
  - ✅ 响应式设计和主题切换

- **部署和运维**
  - ✅ Python GUI启动器
  - ✅ Docker容器化支持
  - ✅ 多种部署方式
  - ✅ 健康检查和监控

- **开发体验**
  - ✅ Maven多模块架构
  - ✅ 热重载开发环境
  - ✅ 完整的配置管理
  - ✅ 详细的文档和示例

#### 🔄 开发路线图

**v1.3.0 (计划中)**
- 🔄 增强规则引擎和数据源支持
- 🔄 任务调度和定时执行
- 🔄 数据去重和清洗优化
- 🔄 API限流和安全增强

**v1.4.0 (规划中)**
- 📋 实时数据流处理
- 📋 数据分析和可视化
- 📋 集群模式和负载均衡
- 📋 移动端适配

**v2.0.0 (长期规划)**
- 📋 机器学习数据分析
- 📋 微服务架构重构
- 📋 云原生部署支持
- 📋 多租户和权限管理

#### 📝 更新日志

**v1.2.0 (2025-07-28)**
- 🆕 完善项目文档和部署指南
- 🆕 优化配置管理和环境变量
- 🆕 增强错误处理和日志记录
- 🔧 修复已知问题和性能优化

**v1.1.0 (2025-07-20)**
- 🆕 添加GUI启动器
- 🆕 支持Docker部署
- 🆕 增加健康检查接口
- 🔧 优化任务处理性能

**v1.0.0 (2025-07-15)**
- 🎉 首个正式版本发布
- ✅ 基础功能完整实现
- ✅ 文档和示例完善

## 📚 相关文档
- [Pure Admin 官方文档](https://pure-admin.cn/)
- [Spring Boot 官方文档](https://spring.io/projects/spring-boot)
- [RabbitMQ 官方文档](https://www.rabbitmq.com/)
- [MySQL 官方文档](https://dev.mysql.com/doc/)

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献指南

我们欢迎所有形式的贡献！无论是报告Bug、提出新功能建议，还是提交代码改进。

### 如何贡献

#### 报告问题
1. **搜索现有Issue**: 在提交新Issue前，请先搜索是否已有相关问题
2. **使用Issue模板**: 选择合适的Issue模板（Bug报告/功能请求）
3. **提供详细信息**: 包括环境信息、重现步骤、期望结果等

#### 提交代码
1. **Fork项目**: 点击右上角Fork按钮
2. **创建分支**:
   ```bash
   git checkout -b feature/your-feature-name
   # 或
   git checkout -b fix/your-bug-fix
   ```
3. **编写代码**: 遵循项目代码规范
4. **测试验证**: 确保所有测试通过
   ```bash
   # 后端测试
   cd backend
   mvn test

   # 前端测试
   cd workbench-web
   pnpm test
   ```
5. **提交更改**: 使用清晰的提交信息
   ```bash
   git commit -m "feat: add new task scheduling feature"
   git commit -m "fix: resolve database connection timeout issue"
   ```
6. **推送分支**:
   ```bash
   git push origin feature/your-feature-name
   ```
7. **创建PR**: 在GitHub上创建Pull Request

### 代码规范

#### 提交信息规范
使用[Conventional Commits](https://www.conventionalcommits.org/)格式：
```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型说明**:
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### 代码审查清单
- [ ] 代码符合项目规范
- [ ] 添加了必要的测试
- [ ] 更新了相关文档
- [ ] 通过了所有CI检查
- [ ] 没有引入安全漏洞

### 开发流程

#### 本地开发环境
1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/workbench.git
   cd workbench
   ```

2. **安装依赖**
   ```bash
   # 后端依赖
   cd backend
   mvn clean install

   # 前端依赖
   cd ../workbench-web
   pnpm install
   ```

3. **配置环境**
   ```bash
   # 复制配置文件
   cp .env.example .env
   # 编辑配置文件，填入实际配置
   ```

4. **启动开发环境**
   ```bash
   # 使用GUI启动器
   python StartWorkbench.py

   # 或手动启动各服务
   ```

#### 测试指南
```bash
# 运行单元测试
cd backend
mvn test

# 运行集成测试
mvn verify

# 前端测试
cd workbench-web
pnpm test

# E2E测试
pnpm test:e2e
```

### 社区准则

#### 行为准则
- 保持友善和专业的态度
- 尊重不同的观点和经验
- 接受建设性的批评
- 关注对社区最有利的事情

#### 获得帮助
- 📖 查看[文档](README.md)
- 💬 在[Discussions](https://github.com/your-repo/discussions)中提问
- 🐛 在[Issues](https://github.com/your-repo/issues)中报告问题
- 📧 联系维护者: <EMAIL>

### 致谢

感谢所有为项目做出贡献的开发者！

<!-- 贡献者列表将自动生成 -->
<a href="https://github.com/your-repo/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=your-repo" />
</a>

---

**⚠️ 免责声明**: 本项目仅供学习和研究使用，请遵守相关法律法规，不得用于非法用途。