# Workbench 环境变量配置示例
# 复制此文件为 .env 并填入实际配置值

# ===========================================
# 数据库配置
# ===========================================
# MySQL数据库连接URL
DB_URL=************************************************************************************************************************************

# 数据库用户名
DB_USERNAME=root

# 数据库密码
DB_PASSWORD=your_mysql_password

# ===========================================
# RabbitMQ配置
# ===========================================
# RabbitMQ服务器地址
RABBITMQ_HOST=localhost

# RabbitMQ端口
RABBITMQ_PORT=5672

# RabbitMQ用户名
RABBITMQ_USERNAME=admin

# RabbitMQ密码
RABBITMQ_PASSWORD=your_rabbitmq_password

# ===========================================
# 应用服务配置
# ===========================================
# System模块端口
SYSTEM_PORT=38889

# Scanner模块端口
SCANNER_PORT=38888

# 前端开发端口
VITE_PORT=8848

# ===========================================
# 爬虫配置
# ===========================================
# 爬虫账号用户名
CRAWLER_USERNAME=your_crawler_username

# 爬虫账号密码
CRAWLER_PASSWORD=your_crawler_password

# ===========================================
# 第三方API配置
# ===========================================
# 天眼查API Token（可选）
TIANYANCHA_TOKEN=your_tianyancha_token

# 天眼查ID（可选）
TIANYANCHA_TYC_ID=your_tianyancha_id

# 高德地图API密钥（可选）
VITE_AMAP_KEY=your_amap_api_key

# ===========================================
# 生产环境配置
# ===========================================
# 生产环境数据库配置
PROD_DB_URL=***********************************************************************************
PROD_DB_USERNAME=workbench_user
PROD_DB_PASSWORD=your_prod_db_password

# 生产环境RabbitMQ配置
PROD_RABBITMQ_HOST=your-prod-rabbitmq-host
PROD_RABBITMQ_USERNAME=workbench_user
PROD_RABBITMQ_PASSWORD=your_prod_rabbitmq_password

# ===========================================
# 日志配置
# ===========================================
# 日志级别 (DEBUG, INFO, WARN, ERROR)
LOG_LEVEL=INFO

# 日志文件路径
LOG_PATH=logs

# ===========================================
# 安全配置
# ===========================================
# JWT密钥（如果启用认证）
JWT_SECRET=your_jwt_secret_key

# 加密盐值
ENCRYPT_SALT=your_encrypt_salt

# ===========================================
# 监控配置
# ===========================================
# 是否启用监控端点
MANAGEMENT_ENDPOINTS_ENABLED=true

# 监控端点端口
MANAGEMENT_PORT=9090

# ===========================================
# Docker配置
# ===========================================
# Docker网络名称
DOCKER_NETWORK=workbench-network

# Docker数据卷路径
DOCKER_DATA_PATH=./docker-data

# ===========================================
# 开发配置
# ===========================================
# 开发模式 (development, production, test)
NODE_ENV=development

# 是否启用热重载
HOT_RELOAD=true

# 是否启用调试模式
DEBUG_MODE=true

# API基础URL（前端使用）
VITE_API_BASE_URL=http://localhost:38889
