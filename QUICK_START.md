# 🚀 Workbench 快速开始指南

本指南将帮助您在5分钟内快速启动Workbench系统。

## 📋 前置条件检查

在开始之前，请确保您的系统已安装以下软件：

```bash
# 检查Java版本 (需要17+)
java -version

# 检查Node.js版本 (需要18+)
node -v

# 检查pnpm版本 (需要9+)
pnpm -v

# 检查MySQL服务状态
mysql --version

# 检查RabbitMQ服务状态
rabbitmq-diagnostics status
```

## ⚡ 一键启动 (推荐)

### 方式1: 使用GUI启动器
```bash
# 克隆项目
git clone <your-repo-url>
cd workbench

# 安装Python依赖 (如果没有安装)
pip install ttkbootstrap psutil

# 启动GUI管理器
python StartWorkbench.py
```

在GUI界面中：
1. 点击"构建所有模块"
2. 等待构建完成
3. 依次启动"System服务"和"Scanner服务"
4. 启动"前端服务"
5. 访问 http://localhost:8848

### 方式2: 使用快速部署脚本
```bash
# Windows
scripts\quick-deploy.bat

# Linux/macOS
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

## 🔧 手动启动

如果您喜欢手动控制每个步骤：

### 1. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件，修改数据库和RabbitMQ密码
nano .env
```

### 2. 数据库初始化
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE workbench CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
exit

# 导入初始数据 (可选)
mysql -u root -p workbench < scripts/recreate-database.sql
```

### 3. 启动后端服务
```bash
# 构建项目
cd backend
mvn clean compile -DskipTests

# 启动System服务 (新终端)
cd workbench-system
mvn spring-boot:run

# 启动Scanner服务 (新终端)
cd workbench-scanner
mvn spring-boot:run
```

### 4. 启动前端服务
```bash
# 安装依赖
cd workbench-web
pnpm install

# 启动开发服务器
pnpm dev
```

## 🌐 访问系统

启动完成后，您可以访问：

- **前端界面**: http://localhost:8848
- **System API**: http://localhost:38889
- **Scanner API**: http://localhost:38888
- **健康检查**: http://localhost:38888/health/status
- **RabbitMQ管理**: http://localhost:15672

## 🧪 验证安装

### 1. 检查服务状态
```bash
# 检查System服务
curl http://localhost:38889/api/tasks

# 检查Scanner服务
curl http://localhost:38888/health/status

# 检查前端服务
curl http://localhost:8848
```

### 2. 创建测试任务
```bash
# 通过API创建任务
curl -X POST http://localhost:38889/api/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试任务",
    "rule": "test.com",
    "timeRange": "1天内"
  }'
```

### 3. 查看任务执行
- 访问前端界面查看任务状态
- 检查日志文件：`logs/workbench-system.log` 和 `logs/workbench-scanner.log`

## 🚨 常见问题

### 端口冲突
如果遇到端口占用问题：
```bash
# 查看端口占用
netstat -an | grep :8848
netstat -an | grep :38889
netstat -an | grep :38888

# 修改配置文件中的端口号
```

### 数据库连接失败
```bash
# 检查MySQL服务
sudo systemctl status mysql  # Linux
brew services list | grep mysql  # macOS

# 检查数据库权限
mysql -u root -p
SHOW GRANTS FOR 'root'@'localhost';
```

### RabbitMQ连接失败
```bash
# 启动RabbitMQ服务
sudo systemctl start rabbitmq-server  # Linux
brew services start rabbitmq  # macOS

# 检查管理插件
sudo rabbitmq-plugins enable rabbitmq_management
```

### 前端编译失败
```bash
# 清理缓存
cd workbench-web
rm -rf node_modules pnpm-lock.yaml
pnpm install

# 检查Node.js版本
node -v  # 需要18+
```

## 📚 下一步

系统启动成功后，您可以：

1. **阅读完整文档**: [README.md](README.md)
2. **查看API文档**: 访问 http://localhost:38889/swagger-ui.html
3. **配置爬虫账号**: 编辑 `.env` 文件中的爬虫配置
4. **创建第一个任务**: 在前端界面中创建数据采集任务
5. **监控系统状态**: 查看日志和监控面板

## 🆘 获取帮助

如果遇到问题：

1. 查看 [常见问题解答](README.md#❓-常见问题)
2. 检查 [故障排除指南](README.md#🛠️-故障排除)
3. 提交 [Issue](https://github.com/your-repo/issues)
4. 联系维护者

---

**🎉 恭喜！您已成功启动Workbench系统！**

现在可以开始使用系统进行数据采集和任务管理了。
